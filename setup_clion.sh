#!/bin/bash

# BambuStudio CLion Setup Script
# This script helps configure the development environment for CLion

set -e

PROJECT_ROOT="/Users/<USER>/Documents/augment-projects/BambuStudio"
DEPS_DIR="/Users/<USER>/Documents/augment-projects/BambuStudio_dep"

echo "=== BambuStudio CLion Setup ==="
echo "Project Root: $PROJECT_ROOT"
echo "Dependencies: $DEPS_DIR"

# Check if dependencies are built
if [ ! -d "$DEPS_DIR/usr/local" ]; then
    echo "❌ Dependencies not found. Please build dependencies first:"
    echo "   cd $PROJECT_ROOT/deps/build"
    echo "   make -j12"
    exit 1
fi

# Check critical dependency libraries
echo "🔍 Checking critical dependencies..."
REQUIRED_LIBS=(
    "lib/libexpat.a"
    "lib/libblosc.a"
    "lib/libqhullstatic_r.a"
    "lib/libjpeg.a"
    "lib/libpng.a"
    "lib/libfreetype.a"
    "lib/libglfw3.a"
    "lib/libtbb.a"
    "lib/libssl.a"
    "lib/libcrypto.a"
)

MISSING_LIBS=()
for lib in "${REQUIRED_LIBS[@]}"; do
    if [ ! -f "$DEPS_DIR/usr/local/$lib" ]; then
        MISSING_LIBS+=("$lib")
    fi
done

if [ ${#MISSING_LIBS[@]} -ne 0 ]; then
    echo "⚠️  Some dependencies are still missing:"
    for lib in "${MISSING_LIBS[@]}"; do
        echo "   - $lib"
    done
    echo ""
    echo "Dependencies may still be building. You can:"
    echo "1. Wait for the build to complete"
    echo "2. Run './check_deps.sh' to check status"
    echo "3. Continue anyway (some features may not work)"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "✅ Dependencies found"

# Create build directories
mkdir -p "$PROJECT_ROOT/build/debug"
mkdir -p "$PROJECT_ROOT/build/release"
mkdir -p "$PROJECT_ROOT/build/xcode"
mkdir -p "$PROJECT_ROOT/install_dir"

echo "✅ Build directories created"

# Check if Ninja is available
if ! command -v ninja &> /dev/null; then
    echo "⚠️  Ninja not found. Installing via Homebrew..."
    brew install ninja
fi

echo "✅ Ninja available"

# Configure debug build
echo "🔧 Configuring debug build..."
cd "$PROJECT_ROOT/build/debug"
cmake ../.. \
    -G Ninja \
    -DCMAKE_BUILD_TYPE=Debug \
    -DCMAKE_PREFIX_PATH="$DEPS_DIR/usr/local" \
    -DCMAKE_INSTALL_PREFIX="$PROJECT_ROOT/install_dir" \
    -DCMAKE_MACOSX_RPATH=ON \
    -DCMAKE_INSTALL_RPATH="$DEPS_DIR/usr/local" \
    -DCMAKE_MACOSX_BUNDLE=ON \
    -DBBL_RELEASE_TO_PUBLIC=1 \
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON

echo "✅ Debug configuration complete"

# Configure Xcode build for debugging
echo "🔧 Configuring Xcode build..."
cd "$PROJECT_ROOT/build/xcode"
cmake ../.. \
    -G Xcode \
    -DCMAKE_BUILD_TYPE=Debug \
    -DCMAKE_PREFIX_PATH="$DEPS_DIR/usr/local" \
    -DCMAKE_INSTALL_PREFIX="$PROJECT_ROOT/install_dir" \
    -DCMAKE_MACOSX_RPATH=ON \
    -DCMAKE_INSTALL_RPATH="$DEPS_DIR/usr/local" \
    -DCMAKE_MACOSX_BUNDLE=ON \
    -DBBL_RELEASE_TO_PUBLIC=1 \
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON

echo "✅ Xcode configuration complete"

# Create compile_commands.json symlink for better IDE support
cd "$PROJECT_ROOT"
if [ -f "build/debug/compile_commands.json" ]; then
    ln -sf build/debug/compile_commands.json .
    echo "✅ Created compile_commands.json symlink"
fi

echo ""
echo "🎉 Setup complete! You can now:"
echo "1. Open CLion"
echo "2. Open the project folder: $PROJECT_ROOT"
echo "3. CLion should automatically detect the CMakePresets.json"
echo "4. Select 'debug' configuration (recommended for development)"
echo "5. Wait for CMake configuration to complete"
echo "6. Build and debug the project"
echo ""
echo "📋 Configuration Summary:"
echo "   Project Root: $PROJECT_ROOT"
echo "   Dependencies: $DEPS_DIR/usr/local"
echo "   Build System: Ninja (debug), Xcode (xcode-debug)"
echo ""
echo "📁 Build Directories:"
echo "   Debug (Ninja): $PROJECT_ROOT/build/debug"
echo "   Xcode:         $PROJECT_ROOT/build/xcode"
echo ""
echo "🎯 Main Build Targets:"
echo "   BambuStudio_app_gui - Main GUI application"
echo "   libslic3r - Core slicing library"
echo "   libslic3r_gui - GUI-related library"
echo ""
echo "📝 CLion Tips:"
echo "- Use 'debug' preset for faster builds with Ninja"
echo "- Use 'xcode-debug' preset for Xcode integration"
echo "- Set breakpoints by clicking line numbers"
echo "- Use Cmd+F9 to build, Ctrl+D to debug"
echo "- Check 'View → Tool Windows → CMake' for build output"
echo ""
echo "🔧 Troubleshooting:"
echo "- If CMake fails, check the CMake tool window for errors"
echo "- Ensure all dependencies are built: ./check_deps.sh"
echo "- For build issues, try cleaning: rm -rf build/debug && ./setup_clion.sh"
