# SPDX-License-Identifier: BSD-3-Clause
# Copyright Contributors to the OpenEXR Project.
#
# SonarCloud analysis configuration file
# https://sonarcloud.io/documentation/analysis/analysis-parameters

sonar.host.url=https://sonarcloud.io

# Required metadata
sonar.organization=academysoftwarefoundation
sonar.projectKey=AcademySoftwareFoundation_openexr
sonar.projectName=OpenEXR
sonar.projectVersion=2.5

# Project links
sonar.links.homepage=http://openexr.com
sonar.links.ci=https://github.com/AcademySoftwareFoundation/openexr/actions
sonar.links.scm=https://github.com/AcademySoftwareFoundation/openexr
sonar.links.issue=https://github.com/AcademySoftwareFoundation/openexr/issues

# Source properties
sonar.sources=IlmBase,OpenEXR,PyIlmBase
sonar.sourceEncoding=UTF-8
sonar.exclusions=src/bindings/java/**,*.java

# C/C++ analyzer properties
sonar.cfamily.build-wrapper-output=_build/bw_output
sonar.cfamily.gcov.reportsPath=_coverage

