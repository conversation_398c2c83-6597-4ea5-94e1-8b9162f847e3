#!@PYTHON@

from __future__ import print_function

import sys
import numpy
import imathnumpy
from imath import Float<PERSON>rray, IntArray

length = 10
a = numpy.random.uniform(1,5,length)
b = numpy.random.uniform(1,5,length)
fdest = FloatArray(length)

dest = imathnumpy.arrayToNumpy(fdest)
dest[:] = a * b

results = fdest - a*b

print( "a: {}".format(a) )
print( "b: {}".format(b) )
print( "dest: {}".format(fdest) )
print( "diff: {}".format(results) )

ia = numpy.floor(numpy.random.uniform(1,5,length))
ib = numpy.floor(numpy.random.uniform(1,5,length))
idest = IntArray(length)
idest = imathnumpy.arrayToNumpy(idest)
idest[:] = ia * ib
iresults = idest - ia*ib

print( "a: {}".format(ia) )
print( "b: {}".format(ib) )
print( "dest: {}".format(idest) )
print( "diff: {}".format(iresults) )
