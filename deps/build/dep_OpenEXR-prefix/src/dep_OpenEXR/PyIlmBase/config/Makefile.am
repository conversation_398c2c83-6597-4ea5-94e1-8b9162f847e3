##
## SPDX-License-Identifier: BSD-3-Clause
## Copyright Contributors to the OpenEXR Project.
##

## Process this file with automake to produce Makefile.in

configincludedir = $(includedir)/OpenEXR

nodist_configinclude_HEADERS = PyIlmBaseConfig.h PyIlmBaseConfigInternal.h

EXTRA_DIST = PyIlmBaseConfig.h.in \
             PyIlmBaseConfigInternal.h.in \
             PyIlmBaseConfigInternal.h.in_cmake \
             PyIlmBaseSetup.cmake \
	     NumPyLocate.cmake \
	     ModuleDefine.cmake \
	     ParseConfigure.cmake \
             CMakeLists.txt
