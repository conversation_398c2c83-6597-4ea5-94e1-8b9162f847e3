# OpenEXR Project Roles and Responsibilities

OpenEXR is a project of the Academy Software Foundation and relies on
the ASWF governance policies, supported by the Linux Foundation.

There are three primary project roles: Contributors submit code to the
project; Committers approve code to be included into the project; and
the Technical Steering Committee (TSC) provides overall high-level
project guidance.

* [Contributors](#Contributors)
* [Committers](#Committers)
* [Technical Steering Committee](#Technical-Steering-Committee)

## Contributors

The OpenEXR project grows and thrives from assistance from
Contributors.  Contributors include anyone in the community that
submits code, documentation, or other technical artifacts to the
project. However, such contributions must be approved by a project
Committer before they become a part of the project.  

Anyone can be a Contributor. You need no formal approval from the
project, beyond the legal forms.

### How to Become a Contributor

* Review the coding standards to ensure your contribution is in line
  with the project's coding and styling guidelines.

* Sign the Individual CLA, or have your organization sign the Corporate CLA.

* Submit your code as a PR with the appropriate DCO sign-off.

## Committers

Project Committers have merge access on the OpenEXR GitHub repository
and are responsible for approving submissions by Contributors.

### Committer Responsibilities

Typical activities of a Committer include:

* Helping users and novice contributors.

* Ensuring a response to questions posted to the
  <EMAIL> mailing list.

* Contributing code and documentation changes that improve the
  project.

* Reviewing and commenting on issues and pull requests.

* Ensuring that changes and new code meet acceptable standards and are
  in the long-term interest of the project.

* Participation in working groups.

* Merging pull requests.

### How to Become a Committer

Any existing Committer can nominate an individual making significant
and valuable contributions to the OpenEXR project to become a new
Committer.  New committers are approved by vote of the TSC.

If you are interested in becomming a Committer, contact the TSC at
<EMAIL>.

## Technical Steering Committee

The Technical Steering Committee (TSC) oversees the overall technical
direction of OpenEXR, as defined in the project
[charter](ASWF/charter/OpenEXR-Technical-Charter.md).  This
charter defines the TSC member terms and succession policies.

The responsibilities of the TSC include:

* Coordinating technical direction of the project.

* Project governance and contribution policy.

* GitHub repository administration.

* Maintaining the list of additional Committers.

* Appointing representatives to work with other open source or open
  standards communities.

* Discussions, seeking consensus, and where necessary, voting on
  technical matters relating to the code base that affect multiple
  projects.

* Coordinating any marketing, events, or communications regarding the
  project.

The TSC elects a Chair person, who acts as the project manager,
organizing meetings and providing oversight to project
administration. The Chair is elected by the TSC.  The Chair also
serves as the OpenEXR representative on the Academy Software
Foundation (ASWF) Technical Advisory Council (TAC). The chair
represents the project at ASWF TAC meetings.

### Current TSC Members

* Cary Phillips (chair) - Industrial Light & Magic
* Rod Bogart - Epic Games
* Larry Gritz - Sony Pictures ImageWorks
* Peter Hillman - Weta Digital, Ltd.
* Kimball Thurston - Weta Digital, Ltd.
* Nick Porcino - Pixar Animation Studios
* Christina Tempelaar-Lietz - Epic Games
* Joseph Goldstone - ARRI
* John Mertic - The Linux Foundation

### TSC Meetings

All meetings of the TSC are open to participation by any member of the
OpenEXR community. Meeting times are listed in the [ASWF technical
community calendar](https://lists.aswf.io/g/tac/calendar), currently
each Thursday at 1pm Pacific Time via Zoom video conference.  The TSC
Chair moderates the meeting, or appoints another TSC member to
moderate in his or her absence.

Items are added to the TSC agenda which are considered contentious or
are modifications of governance, contribution policy, TSC membership,
or release process, in addition to topics involving the high-level
technical direction of the project.

The intention of the agenda is not to approve or review all
patches. That should happen continuously on GitHub and be handled by
the larger group of Committers.

Any community member or Contributor can ask that something be reviewed
by the TSC at the meeting by logging a GitHub issue. Any Committer,
TSC member, or the meeting chair can bring the issue to the TSC's
attention by applying the `TSC` label.

Prior to each TSC meeting, the meeting chair will share the agenda with members
of the TSC. TSC members can also add items to the agenda at the beginning of
each meeting. The meeting chair and the TSC cannot veto or remove items.

The TSC may invite additional persons to participate in a non-voting capacity.

The meeting chair is responsible for archiving the minutes, stored at 
https://github.com/AcademySoftwareFoundation/openexr/tree/master/ASWF/tsc-meetings.

Due to the challenges of scheduling a global meeting with participants
in several time zones, the TSC will seek to resolve as many agenda
items as possible outside of meetings on the public mailing list.

