<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Name="freetype"
	ProjectGUID="{78B079BD-9FC7-4B9E-B4A6-96DA0F00248B}"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="..\..\..\objs\$(PlatformName)\$(ConfigurationName)\"
			IntermediateDirectory="..\..\..\objs\$(PlatformName)\$(ConfigurationName)\"
			ConfigurationType="2"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="0"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				AdditionalIncludeDirectories="..\..\..\include"
				PreprocessorDefinitions="NDEBUG;WIN32;_LIB;_CRT_SECURE_NO_WARNINGS;FT2_BUILD_LIBRARY;DLL_EXPORT"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableEnhancedInstructionSet="2"
				EnableFunctionLevelLinking="true"
				DisableLanguageExtensions="true"
				WarningLevel="4"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings="4001"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG;DLL_EXPORT"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release Static|Win32"
			OutputDirectory="..\..\..\objs\$(PlatformName)\$(ConfigurationName)\"
			IntermediateDirectory="..\..\..\objs\$(PlatformName)\$(ConfigurationName)\"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="0"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				AdditionalIncludeDirectories="..\..\..\include"
				PreprocessorDefinitions="NDEBUG;WIN32;_LIB;_CRT_SECURE_NO_WARNINGS;FT2_BUILD_LIBRARY"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableEnhancedInstructionSet="2"
				EnableFunctionLevelLinking="true"
				DisableLanguageExtensions="true"
				WarningLevel="4"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings="4001"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="..\..\..\objs\$(PlatformName)\$(ConfigurationName)\"
			IntermediateDirectory="..\..\..\objs\$(PlatformName)\$(ConfigurationName)\"
			ConfigurationType="2"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="0"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\..\include"
				PreprocessorDefinitions="_DEBUG;WIN32;_LIB;_CRT_SECURE_NO_WARNINGS;FT_DEBUG_LEVEL_ERROR;FT_DEBUG_LEVEL_TRACE;FT2_BUILD_LIBRARY;DLL_EXPORT"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				DisableLanguageExtensions="true"
				WarningLevel="4"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings="4001"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG;DLL_EXPORT"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug Static|Win32"
			OutputDirectory="..\..\..\objs\$(PlatformName)\$(ConfigurationName)\"
			IntermediateDirectory="..\..\..\objs\$(PlatformName)\$(ConfigurationName)\"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="0"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\..\include"
				PreprocessorDefinitions="_DEBUG;WIN32;_LIB;_CRT_SECURE_NO_WARNINGS;FT_DEBUG_LEVEL_ERROR;FT_DEBUG_LEVEL_TRACE;FT2_BUILD_LIBRARY"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				DisableLanguageExtensions="true"
				WarningLevel="4"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings="4001"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="..\..\..\src\autofit\autofit.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\bdf\bdf.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\cff\cff.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\base\ftbase.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\base\ftbitmap.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\cache\ftcache.c"
				>
			</File>
			<File
				RelativePath="..\ftdebug.c"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						DisableLanguageExtensions="false"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						DisableLanguageExtensions="false"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						DisableLanguageExtensions="false"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						DisableLanguageExtensions="false"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\..\..\src\base\ftfstype.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\base\ftgasp.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\base\ftglyph.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\gzip\ftgzip.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\base\ftinit.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\lzw\ftlzw.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\base\ftstroke.c"
				>
			</File>
			<File
				RelativePath="..\ftsystem.c"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						DisableLanguageExtensions="false"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						DisableLanguageExtensions="false"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						DisableLanguageExtensions="false"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						DisableLanguageExtensions="false"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\..\..\src\sdf\sdf.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\smooth\smooth.c"
				>
			</File>
			<File
				RelativePath="..\..\..\src\svg\svg.c"
				>
			</File>
			<Filter
				Name="FT_MODULES"
				>
				<File
					RelativePath="..\..\..\src\base\ftbbox.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\base\ftbdf.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\base\ftcid.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\base\ftgxval.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\base\ftmm.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\base\ftotval.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\base\ftpatent.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\base\ftpfr.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\base\ftsynth.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\base\fttype1.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\base\ftwinfnt.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\pcf\pcf.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\pfr\pfr.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\psaux\psaux.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\pshinter\pshinter.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\psnames\psmodule.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\raster\raster.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\sfnt\sfnt.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\truetype\truetype.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\type1\type1.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\cid\type1cid.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\type42\type42.c"
					>
				</File>
				<File
					RelativePath="..\..\..\src\winfonts\winfnt.c"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="..\..\..\include\ft2build.h"
				>
			</File>
			<File
				RelativePath="..\..\..\include\freetype\config\ftconfig.h"
				>
			</File>
			<File
				RelativePath="..\..\..\include\freetype\config\ftheader.h"
				>
			</File>
			<File
				RelativePath="..\..\..\include\freetype\config\ftmodule.h"
				>
			</File>
			<File
				RelativePath="..\..\..\include\freetype\config\ftoption.h"
				>
			</File>
			<File
				RelativePath="..\..\..\include\freetype\config\ftstdlib.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx"
			>
			<File
				RelativePath="..\..\..\src\base\ftver.rc"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
