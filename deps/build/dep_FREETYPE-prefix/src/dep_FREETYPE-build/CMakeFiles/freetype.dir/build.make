# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build

# Include any dependencies generated for this target.
include CMakeFiles/freetype.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/freetype.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/freetype.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/freetype.dir/flags.make

CMakeFiles/freetype.dir/src/autofit/autofit.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/autofit/autofit.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/autofit/autofit.c
CMakeFiles/freetype.dir/src/autofit/autofit.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/freetype.dir/src/autofit/autofit.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/autofit/autofit.c.o -MF CMakeFiles/freetype.dir/src/autofit/autofit.c.o.d -o CMakeFiles/freetype.dir/src/autofit/autofit.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/autofit/autofit.c

CMakeFiles/freetype.dir/src/autofit/autofit.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/autofit/autofit.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/autofit/autofit.c > CMakeFiles/freetype.dir/src/autofit/autofit.c.i

CMakeFiles/freetype.dir/src/autofit/autofit.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/autofit/autofit.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/autofit/autofit.c -o CMakeFiles/freetype.dir/src/autofit/autofit.c.s

CMakeFiles/freetype.dir/src/base/ftbase.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftbase.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbase.c
CMakeFiles/freetype.dir/src/base/ftbase.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/freetype.dir/src/base/ftbase.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftbase.c.o -MF CMakeFiles/freetype.dir/src/base/ftbase.c.o.d -o CMakeFiles/freetype.dir/src/base/ftbase.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbase.c

CMakeFiles/freetype.dir/src/base/ftbase.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftbase.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbase.c > CMakeFiles/freetype.dir/src/base/ftbase.c.i

CMakeFiles/freetype.dir/src/base/ftbase.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftbase.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbase.c -o CMakeFiles/freetype.dir/src/base/ftbase.c.s

CMakeFiles/freetype.dir/src/base/ftbbox.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftbbox.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbbox.c
CMakeFiles/freetype.dir/src/base/ftbbox.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/freetype.dir/src/base/ftbbox.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftbbox.c.o -MF CMakeFiles/freetype.dir/src/base/ftbbox.c.o.d -o CMakeFiles/freetype.dir/src/base/ftbbox.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbbox.c

CMakeFiles/freetype.dir/src/base/ftbbox.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftbbox.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbbox.c > CMakeFiles/freetype.dir/src/base/ftbbox.c.i

CMakeFiles/freetype.dir/src/base/ftbbox.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftbbox.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbbox.c -o CMakeFiles/freetype.dir/src/base/ftbbox.c.s

CMakeFiles/freetype.dir/src/base/ftbdf.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftbdf.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbdf.c
CMakeFiles/freetype.dir/src/base/ftbdf.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/freetype.dir/src/base/ftbdf.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftbdf.c.o -MF CMakeFiles/freetype.dir/src/base/ftbdf.c.o.d -o CMakeFiles/freetype.dir/src/base/ftbdf.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbdf.c

CMakeFiles/freetype.dir/src/base/ftbdf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftbdf.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbdf.c > CMakeFiles/freetype.dir/src/base/ftbdf.c.i

CMakeFiles/freetype.dir/src/base/ftbdf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftbdf.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbdf.c -o CMakeFiles/freetype.dir/src/base/ftbdf.c.s

CMakeFiles/freetype.dir/src/base/ftbitmap.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftbitmap.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbitmap.c
CMakeFiles/freetype.dir/src/base/ftbitmap.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/freetype.dir/src/base/ftbitmap.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftbitmap.c.o -MF CMakeFiles/freetype.dir/src/base/ftbitmap.c.o.d -o CMakeFiles/freetype.dir/src/base/ftbitmap.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbitmap.c

CMakeFiles/freetype.dir/src/base/ftbitmap.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftbitmap.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbitmap.c > CMakeFiles/freetype.dir/src/base/ftbitmap.c.i

CMakeFiles/freetype.dir/src/base/ftbitmap.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftbitmap.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbitmap.c -o CMakeFiles/freetype.dir/src/base/ftbitmap.c.s

CMakeFiles/freetype.dir/src/base/ftcid.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftcid.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftcid.c
CMakeFiles/freetype.dir/src/base/ftcid.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/freetype.dir/src/base/ftcid.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftcid.c.o -MF CMakeFiles/freetype.dir/src/base/ftcid.c.o.d -o CMakeFiles/freetype.dir/src/base/ftcid.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftcid.c

CMakeFiles/freetype.dir/src/base/ftcid.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftcid.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftcid.c > CMakeFiles/freetype.dir/src/base/ftcid.c.i

CMakeFiles/freetype.dir/src/base/ftcid.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftcid.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftcid.c -o CMakeFiles/freetype.dir/src/base/ftcid.c.s

CMakeFiles/freetype.dir/src/base/ftfstype.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftfstype.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftfstype.c
CMakeFiles/freetype.dir/src/base/ftfstype.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/freetype.dir/src/base/ftfstype.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftfstype.c.o -MF CMakeFiles/freetype.dir/src/base/ftfstype.c.o.d -o CMakeFiles/freetype.dir/src/base/ftfstype.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftfstype.c

CMakeFiles/freetype.dir/src/base/ftfstype.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftfstype.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftfstype.c > CMakeFiles/freetype.dir/src/base/ftfstype.c.i

CMakeFiles/freetype.dir/src/base/ftfstype.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftfstype.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftfstype.c -o CMakeFiles/freetype.dir/src/base/ftfstype.c.s

CMakeFiles/freetype.dir/src/base/ftgasp.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftgasp.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftgasp.c
CMakeFiles/freetype.dir/src/base/ftgasp.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/freetype.dir/src/base/ftgasp.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftgasp.c.o -MF CMakeFiles/freetype.dir/src/base/ftgasp.c.o.d -o CMakeFiles/freetype.dir/src/base/ftgasp.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftgasp.c

CMakeFiles/freetype.dir/src/base/ftgasp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftgasp.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftgasp.c > CMakeFiles/freetype.dir/src/base/ftgasp.c.i

CMakeFiles/freetype.dir/src/base/ftgasp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftgasp.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftgasp.c -o CMakeFiles/freetype.dir/src/base/ftgasp.c.s

CMakeFiles/freetype.dir/src/base/ftglyph.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftglyph.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftglyph.c
CMakeFiles/freetype.dir/src/base/ftglyph.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/freetype.dir/src/base/ftglyph.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftglyph.c.o -MF CMakeFiles/freetype.dir/src/base/ftglyph.c.o.d -o CMakeFiles/freetype.dir/src/base/ftglyph.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftglyph.c

CMakeFiles/freetype.dir/src/base/ftglyph.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftglyph.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftglyph.c > CMakeFiles/freetype.dir/src/base/ftglyph.c.i

CMakeFiles/freetype.dir/src/base/ftglyph.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftglyph.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftglyph.c -o CMakeFiles/freetype.dir/src/base/ftglyph.c.s

CMakeFiles/freetype.dir/src/base/ftgxval.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftgxval.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftgxval.c
CMakeFiles/freetype.dir/src/base/ftgxval.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/freetype.dir/src/base/ftgxval.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftgxval.c.o -MF CMakeFiles/freetype.dir/src/base/ftgxval.c.o.d -o CMakeFiles/freetype.dir/src/base/ftgxval.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftgxval.c

CMakeFiles/freetype.dir/src/base/ftgxval.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftgxval.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftgxval.c > CMakeFiles/freetype.dir/src/base/ftgxval.c.i

CMakeFiles/freetype.dir/src/base/ftgxval.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftgxval.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftgxval.c -o CMakeFiles/freetype.dir/src/base/ftgxval.c.s

CMakeFiles/freetype.dir/src/base/ftinit.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftinit.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftinit.c
CMakeFiles/freetype.dir/src/base/ftinit.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/freetype.dir/src/base/ftinit.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftinit.c.o -MF CMakeFiles/freetype.dir/src/base/ftinit.c.o.d -o CMakeFiles/freetype.dir/src/base/ftinit.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftinit.c

CMakeFiles/freetype.dir/src/base/ftinit.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftinit.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftinit.c > CMakeFiles/freetype.dir/src/base/ftinit.c.i

CMakeFiles/freetype.dir/src/base/ftinit.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftinit.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftinit.c -o CMakeFiles/freetype.dir/src/base/ftinit.c.s

CMakeFiles/freetype.dir/src/base/ftmm.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftmm.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftmm.c
CMakeFiles/freetype.dir/src/base/ftmm.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/freetype.dir/src/base/ftmm.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftmm.c.o -MF CMakeFiles/freetype.dir/src/base/ftmm.c.o.d -o CMakeFiles/freetype.dir/src/base/ftmm.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftmm.c

CMakeFiles/freetype.dir/src/base/ftmm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftmm.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftmm.c > CMakeFiles/freetype.dir/src/base/ftmm.c.i

CMakeFiles/freetype.dir/src/base/ftmm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftmm.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftmm.c -o CMakeFiles/freetype.dir/src/base/ftmm.c.s

CMakeFiles/freetype.dir/src/base/ftotval.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftotval.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftotval.c
CMakeFiles/freetype.dir/src/base/ftotval.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/freetype.dir/src/base/ftotval.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftotval.c.o -MF CMakeFiles/freetype.dir/src/base/ftotval.c.o.d -o CMakeFiles/freetype.dir/src/base/ftotval.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftotval.c

CMakeFiles/freetype.dir/src/base/ftotval.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftotval.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftotval.c > CMakeFiles/freetype.dir/src/base/ftotval.c.i

CMakeFiles/freetype.dir/src/base/ftotval.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftotval.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftotval.c -o CMakeFiles/freetype.dir/src/base/ftotval.c.s

CMakeFiles/freetype.dir/src/base/ftpatent.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftpatent.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftpatent.c
CMakeFiles/freetype.dir/src/base/ftpatent.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/freetype.dir/src/base/ftpatent.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftpatent.c.o -MF CMakeFiles/freetype.dir/src/base/ftpatent.c.o.d -o CMakeFiles/freetype.dir/src/base/ftpatent.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftpatent.c

CMakeFiles/freetype.dir/src/base/ftpatent.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftpatent.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftpatent.c > CMakeFiles/freetype.dir/src/base/ftpatent.c.i

CMakeFiles/freetype.dir/src/base/ftpatent.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftpatent.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftpatent.c -o CMakeFiles/freetype.dir/src/base/ftpatent.c.s

CMakeFiles/freetype.dir/src/base/ftpfr.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftpfr.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftpfr.c
CMakeFiles/freetype.dir/src/base/ftpfr.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/freetype.dir/src/base/ftpfr.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftpfr.c.o -MF CMakeFiles/freetype.dir/src/base/ftpfr.c.o.d -o CMakeFiles/freetype.dir/src/base/ftpfr.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftpfr.c

CMakeFiles/freetype.dir/src/base/ftpfr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftpfr.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftpfr.c > CMakeFiles/freetype.dir/src/base/ftpfr.c.i

CMakeFiles/freetype.dir/src/base/ftpfr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftpfr.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftpfr.c -o CMakeFiles/freetype.dir/src/base/ftpfr.c.s

CMakeFiles/freetype.dir/src/base/ftstroke.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftstroke.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftstroke.c
CMakeFiles/freetype.dir/src/base/ftstroke.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/freetype.dir/src/base/ftstroke.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftstroke.c.o -MF CMakeFiles/freetype.dir/src/base/ftstroke.c.o.d -o CMakeFiles/freetype.dir/src/base/ftstroke.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftstroke.c

CMakeFiles/freetype.dir/src/base/ftstroke.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftstroke.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftstroke.c > CMakeFiles/freetype.dir/src/base/ftstroke.c.i

CMakeFiles/freetype.dir/src/base/ftstroke.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftstroke.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftstroke.c -o CMakeFiles/freetype.dir/src/base/ftstroke.c.s

CMakeFiles/freetype.dir/src/base/ftsynth.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftsynth.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftsynth.c
CMakeFiles/freetype.dir/src/base/ftsynth.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object CMakeFiles/freetype.dir/src/base/ftsynth.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftsynth.c.o -MF CMakeFiles/freetype.dir/src/base/ftsynth.c.o.d -o CMakeFiles/freetype.dir/src/base/ftsynth.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftsynth.c

CMakeFiles/freetype.dir/src/base/ftsynth.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftsynth.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftsynth.c > CMakeFiles/freetype.dir/src/base/ftsynth.c.i

CMakeFiles/freetype.dir/src/base/ftsynth.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftsynth.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftsynth.c -o CMakeFiles/freetype.dir/src/base/ftsynth.c.s

CMakeFiles/freetype.dir/src/base/fttype1.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/fttype1.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/fttype1.c
CMakeFiles/freetype.dir/src/base/fttype1.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object CMakeFiles/freetype.dir/src/base/fttype1.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/fttype1.c.o -MF CMakeFiles/freetype.dir/src/base/fttype1.c.o.d -o CMakeFiles/freetype.dir/src/base/fttype1.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/fttype1.c

CMakeFiles/freetype.dir/src/base/fttype1.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/fttype1.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/fttype1.c > CMakeFiles/freetype.dir/src/base/fttype1.c.i

CMakeFiles/freetype.dir/src/base/fttype1.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/fttype1.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/fttype1.c -o CMakeFiles/freetype.dir/src/base/fttype1.c.s

CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftwinfnt.c
CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o -MF CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o.d -o CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftwinfnt.c

CMakeFiles/freetype.dir/src/base/ftwinfnt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftwinfnt.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftwinfnt.c > CMakeFiles/freetype.dir/src/base/ftwinfnt.c.i

CMakeFiles/freetype.dir/src/base/ftwinfnt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftwinfnt.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftwinfnt.c -o CMakeFiles/freetype.dir/src/base/ftwinfnt.c.s

CMakeFiles/freetype.dir/src/bdf/bdf.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/bdf/bdf.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/bdf/bdf.c
CMakeFiles/freetype.dir/src/bdf/bdf.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object CMakeFiles/freetype.dir/src/bdf/bdf.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/bdf/bdf.c.o -MF CMakeFiles/freetype.dir/src/bdf/bdf.c.o.d -o CMakeFiles/freetype.dir/src/bdf/bdf.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/bdf/bdf.c

CMakeFiles/freetype.dir/src/bdf/bdf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/bdf/bdf.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/bdf/bdf.c > CMakeFiles/freetype.dir/src/bdf/bdf.c.i

CMakeFiles/freetype.dir/src/bdf/bdf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/bdf/bdf.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/bdf/bdf.c -o CMakeFiles/freetype.dir/src/bdf/bdf.c.s

CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/bzip2/ftbzip2.c
CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o -MF CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o.d -o CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/bzip2/ftbzip2.c

CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/bzip2/ftbzip2.c > CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.i

CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/bzip2/ftbzip2.c -o CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.s

CMakeFiles/freetype.dir/src/cache/ftcache.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/cache/ftcache.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cache/ftcache.c
CMakeFiles/freetype.dir/src/cache/ftcache.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object CMakeFiles/freetype.dir/src/cache/ftcache.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/cache/ftcache.c.o -MF CMakeFiles/freetype.dir/src/cache/ftcache.c.o.d -o CMakeFiles/freetype.dir/src/cache/ftcache.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cache/ftcache.c

CMakeFiles/freetype.dir/src/cache/ftcache.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/cache/ftcache.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cache/ftcache.c > CMakeFiles/freetype.dir/src/cache/ftcache.c.i

CMakeFiles/freetype.dir/src/cache/ftcache.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/cache/ftcache.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cache/ftcache.c -o CMakeFiles/freetype.dir/src/cache/ftcache.c.s

CMakeFiles/freetype.dir/src/cff/cff.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/cff/cff.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cff/cff.c
CMakeFiles/freetype.dir/src/cff/cff.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object CMakeFiles/freetype.dir/src/cff/cff.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/cff/cff.c.o -MF CMakeFiles/freetype.dir/src/cff/cff.c.o.d -o CMakeFiles/freetype.dir/src/cff/cff.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cff/cff.c

CMakeFiles/freetype.dir/src/cff/cff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/cff/cff.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cff/cff.c > CMakeFiles/freetype.dir/src/cff/cff.c.i

CMakeFiles/freetype.dir/src/cff/cff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/cff/cff.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cff/cff.c -o CMakeFiles/freetype.dir/src/cff/cff.c.s

CMakeFiles/freetype.dir/src/cid/type1cid.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/cid/type1cid.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cid/type1cid.c
CMakeFiles/freetype.dir/src/cid/type1cid.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object CMakeFiles/freetype.dir/src/cid/type1cid.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/cid/type1cid.c.o -MF CMakeFiles/freetype.dir/src/cid/type1cid.c.o.d -o CMakeFiles/freetype.dir/src/cid/type1cid.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cid/type1cid.c

CMakeFiles/freetype.dir/src/cid/type1cid.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/cid/type1cid.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cid/type1cid.c > CMakeFiles/freetype.dir/src/cid/type1cid.c.i

CMakeFiles/freetype.dir/src/cid/type1cid.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/cid/type1cid.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cid/type1cid.c -o CMakeFiles/freetype.dir/src/cid/type1cid.c.s

CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/gzip/ftgzip.c
CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o -MF CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o.d -o CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/gzip/ftgzip.c

CMakeFiles/freetype.dir/src/gzip/ftgzip.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/gzip/ftgzip.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/gzip/ftgzip.c > CMakeFiles/freetype.dir/src/gzip/ftgzip.c.i

CMakeFiles/freetype.dir/src/gzip/ftgzip.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/gzip/ftgzip.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/gzip/ftgzip.c -o CMakeFiles/freetype.dir/src/gzip/ftgzip.c.s

CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/lzw/ftlzw.c
CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o -MF CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o.d -o CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/lzw/ftlzw.c

CMakeFiles/freetype.dir/src/lzw/ftlzw.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/lzw/ftlzw.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/lzw/ftlzw.c > CMakeFiles/freetype.dir/src/lzw/ftlzw.c.i

CMakeFiles/freetype.dir/src/lzw/ftlzw.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/lzw/ftlzw.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/lzw/ftlzw.c -o CMakeFiles/freetype.dir/src/lzw/ftlzw.c.s

CMakeFiles/freetype.dir/src/pcf/pcf.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/pcf/pcf.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pcf/pcf.c
CMakeFiles/freetype.dir/src/pcf/pcf.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object CMakeFiles/freetype.dir/src/pcf/pcf.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/pcf/pcf.c.o -MF CMakeFiles/freetype.dir/src/pcf/pcf.c.o.d -o CMakeFiles/freetype.dir/src/pcf/pcf.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pcf/pcf.c

CMakeFiles/freetype.dir/src/pcf/pcf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/pcf/pcf.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pcf/pcf.c > CMakeFiles/freetype.dir/src/pcf/pcf.c.i

CMakeFiles/freetype.dir/src/pcf/pcf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/pcf/pcf.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pcf/pcf.c -o CMakeFiles/freetype.dir/src/pcf/pcf.c.s

CMakeFiles/freetype.dir/src/pfr/pfr.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/pfr/pfr.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pfr/pfr.c
CMakeFiles/freetype.dir/src/pfr/pfr.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object CMakeFiles/freetype.dir/src/pfr/pfr.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/pfr/pfr.c.o -MF CMakeFiles/freetype.dir/src/pfr/pfr.c.o.d -o CMakeFiles/freetype.dir/src/pfr/pfr.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pfr/pfr.c

CMakeFiles/freetype.dir/src/pfr/pfr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/pfr/pfr.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pfr/pfr.c > CMakeFiles/freetype.dir/src/pfr/pfr.c.i

CMakeFiles/freetype.dir/src/pfr/pfr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/pfr/pfr.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pfr/pfr.c -o CMakeFiles/freetype.dir/src/pfr/pfr.c.s

CMakeFiles/freetype.dir/src/psaux/psaux.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/psaux/psaux.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/psaux/psaux.c
CMakeFiles/freetype.dir/src/psaux/psaux.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object CMakeFiles/freetype.dir/src/psaux/psaux.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/psaux/psaux.c.o -MF CMakeFiles/freetype.dir/src/psaux/psaux.c.o.d -o CMakeFiles/freetype.dir/src/psaux/psaux.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/psaux/psaux.c

CMakeFiles/freetype.dir/src/psaux/psaux.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/psaux/psaux.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/psaux/psaux.c > CMakeFiles/freetype.dir/src/psaux/psaux.c.i

CMakeFiles/freetype.dir/src/psaux/psaux.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/psaux/psaux.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/psaux/psaux.c -o CMakeFiles/freetype.dir/src/psaux/psaux.c.s

CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pshinter/pshinter.c
CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o -MF CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o.d -o CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pshinter/pshinter.c

CMakeFiles/freetype.dir/src/pshinter/pshinter.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/pshinter/pshinter.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pshinter/pshinter.c > CMakeFiles/freetype.dir/src/pshinter/pshinter.c.i

CMakeFiles/freetype.dir/src/pshinter/pshinter.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/pshinter/pshinter.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pshinter/pshinter.c -o CMakeFiles/freetype.dir/src/pshinter/pshinter.c.s

CMakeFiles/freetype.dir/src/psnames/psnames.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/psnames/psnames.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/psnames/psnames.c
CMakeFiles/freetype.dir/src/psnames/psnames.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object CMakeFiles/freetype.dir/src/psnames/psnames.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/psnames/psnames.c.o -MF CMakeFiles/freetype.dir/src/psnames/psnames.c.o.d -o CMakeFiles/freetype.dir/src/psnames/psnames.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/psnames/psnames.c

CMakeFiles/freetype.dir/src/psnames/psnames.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/psnames/psnames.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/psnames/psnames.c > CMakeFiles/freetype.dir/src/psnames/psnames.c.i

CMakeFiles/freetype.dir/src/psnames/psnames.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/psnames/psnames.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/psnames/psnames.c -o CMakeFiles/freetype.dir/src/psnames/psnames.c.s

CMakeFiles/freetype.dir/src/raster/raster.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/raster/raster.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/raster/raster.c
CMakeFiles/freetype.dir/src/raster/raster.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building C object CMakeFiles/freetype.dir/src/raster/raster.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/raster/raster.c.o -MF CMakeFiles/freetype.dir/src/raster/raster.c.o.d -o CMakeFiles/freetype.dir/src/raster/raster.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/raster/raster.c

CMakeFiles/freetype.dir/src/raster/raster.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/raster/raster.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/raster/raster.c > CMakeFiles/freetype.dir/src/raster/raster.c.i

CMakeFiles/freetype.dir/src/raster/raster.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/raster/raster.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/raster/raster.c -o CMakeFiles/freetype.dir/src/raster/raster.c.s

CMakeFiles/freetype.dir/src/sdf/sdf.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/sdf/sdf.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/sdf/sdf.c
CMakeFiles/freetype.dir/src/sdf/sdf.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object CMakeFiles/freetype.dir/src/sdf/sdf.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/sdf/sdf.c.o -MF CMakeFiles/freetype.dir/src/sdf/sdf.c.o.d -o CMakeFiles/freetype.dir/src/sdf/sdf.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/sdf/sdf.c

CMakeFiles/freetype.dir/src/sdf/sdf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/sdf/sdf.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/sdf/sdf.c > CMakeFiles/freetype.dir/src/sdf/sdf.c.i

CMakeFiles/freetype.dir/src/sdf/sdf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/sdf/sdf.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/sdf/sdf.c -o CMakeFiles/freetype.dir/src/sdf/sdf.c.s

CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/sfnt/sfnt.c
CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building C object CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o -MF CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o.d -o CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/sfnt/sfnt.c

CMakeFiles/freetype.dir/src/sfnt/sfnt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/sfnt/sfnt.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/sfnt/sfnt.c > CMakeFiles/freetype.dir/src/sfnt/sfnt.c.i

CMakeFiles/freetype.dir/src/sfnt/sfnt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/sfnt/sfnt.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/sfnt/sfnt.c -o CMakeFiles/freetype.dir/src/sfnt/sfnt.c.s

CMakeFiles/freetype.dir/src/smooth/smooth.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/smooth/smooth.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/smooth/smooth.c
CMakeFiles/freetype.dir/src/smooth/smooth.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building C object CMakeFiles/freetype.dir/src/smooth/smooth.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/smooth/smooth.c.o -MF CMakeFiles/freetype.dir/src/smooth/smooth.c.o.d -o CMakeFiles/freetype.dir/src/smooth/smooth.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/smooth/smooth.c

CMakeFiles/freetype.dir/src/smooth/smooth.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/smooth/smooth.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/smooth/smooth.c > CMakeFiles/freetype.dir/src/smooth/smooth.c.i

CMakeFiles/freetype.dir/src/smooth/smooth.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/smooth/smooth.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/smooth/smooth.c -o CMakeFiles/freetype.dir/src/smooth/smooth.c.s

CMakeFiles/freetype.dir/src/svg/svg.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/svg/svg.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/svg/svg.c
CMakeFiles/freetype.dir/src/svg/svg.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building C object CMakeFiles/freetype.dir/src/svg/svg.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/svg/svg.c.o -MF CMakeFiles/freetype.dir/src/svg/svg.c.o.d -o CMakeFiles/freetype.dir/src/svg/svg.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/svg/svg.c

CMakeFiles/freetype.dir/src/svg/svg.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/svg/svg.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/svg/svg.c > CMakeFiles/freetype.dir/src/svg/svg.c.i

CMakeFiles/freetype.dir/src/svg/svg.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/svg/svg.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/svg/svg.c -o CMakeFiles/freetype.dir/src/svg/svg.c.s

CMakeFiles/freetype.dir/src/truetype/truetype.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/truetype/truetype.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/truetype/truetype.c
CMakeFiles/freetype.dir/src/truetype/truetype.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building C object CMakeFiles/freetype.dir/src/truetype/truetype.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/truetype/truetype.c.o -MF CMakeFiles/freetype.dir/src/truetype/truetype.c.o.d -o CMakeFiles/freetype.dir/src/truetype/truetype.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/truetype/truetype.c

CMakeFiles/freetype.dir/src/truetype/truetype.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/truetype/truetype.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/truetype/truetype.c > CMakeFiles/freetype.dir/src/truetype/truetype.c.i

CMakeFiles/freetype.dir/src/truetype/truetype.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/truetype/truetype.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/truetype/truetype.c -o CMakeFiles/freetype.dir/src/truetype/truetype.c.s

CMakeFiles/freetype.dir/src/type1/type1.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/type1/type1.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/type1/type1.c
CMakeFiles/freetype.dir/src/type1/type1.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building C object CMakeFiles/freetype.dir/src/type1/type1.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/type1/type1.c.o -MF CMakeFiles/freetype.dir/src/type1/type1.c.o.d -o CMakeFiles/freetype.dir/src/type1/type1.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/type1/type1.c

CMakeFiles/freetype.dir/src/type1/type1.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/type1/type1.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/type1/type1.c > CMakeFiles/freetype.dir/src/type1/type1.c.i

CMakeFiles/freetype.dir/src/type1/type1.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/type1/type1.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/type1/type1.c -o CMakeFiles/freetype.dir/src/type1/type1.c.s

CMakeFiles/freetype.dir/src/type42/type42.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/type42/type42.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/type42/type42.c
CMakeFiles/freetype.dir/src/type42/type42.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building C object CMakeFiles/freetype.dir/src/type42/type42.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/type42/type42.c.o -MF CMakeFiles/freetype.dir/src/type42/type42.c.o.d -o CMakeFiles/freetype.dir/src/type42/type42.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/type42/type42.c

CMakeFiles/freetype.dir/src/type42/type42.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/type42/type42.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/type42/type42.c > CMakeFiles/freetype.dir/src/type42/type42.c.i

CMakeFiles/freetype.dir/src/type42/type42.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/type42/type42.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/type42/type42.c -o CMakeFiles/freetype.dir/src/type42/type42.c.s

CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/winfonts/winfnt.c
CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building C object CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o -MF CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o.d -o CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/winfonts/winfnt.c

CMakeFiles/freetype.dir/src/winfonts/winfnt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/winfonts/winfnt.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/winfonts/winfnt.c > CMakeFiles/freetype.dir/src/winfonts/winfnt.c.i

CMakeFiles/freetype.dir/src/winfonts/winfnt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/winfonts/winfnt.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/winfonts/winfnt.c -o CMakeFiles/freetype.dir/src/winfonts/winfnt.c.s

CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/builds/unix/ftsystem.c
CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building C object CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o -MF CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o.d -o CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/builds/unix/ftsystem.c

CMakeFiles/freetype.dir/builds/unix/ftsystem.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/builds/unix/ftsystem.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/builds/unix/ftsystem.c > CMakeFiles/freetype.dir/builds/unix/ftsystem.c.i

CMakeFiles/freetype.dir/builds/unix/ftsystem.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/builds/unix/ftsystem.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/builds/unix/ftsystem.c -o CMakeFiles/freetype.dir/builds/unix/ftsystem.c.s

CMakeFiles/freetype.dir/src/base/ftdebug.c.o: CMakeFiles/freetype.dir/flags.make
CMakeFiles/freetype.dir/src/base/ftdebug.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftdebug.c
CMakeFiles/freetype.dir/src/base/ftdebug.c.o: CMakeFiles/freetype.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building C object CMakeFiles/freetype.dir/src/base/ftdebug.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/freetype.dir/src/base/ftdebug.c.o -MF CMakeFiles/freetype.dir/src/base/ftdebug.c.o.d -o CMakeFiles/freetype.dir/src/base/ftdebug.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftdebug.c

CMakeFiles/freetype.dir/src/base/ftdebug.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/freetype.dir/src/base/ftdebug.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftdebug.c > CMakeFiles/freetype.dir/src/base/ftdebug.c.i

CMakeFiles/freetype.dir/src/base/ftdebug.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/freetype.dir/src/base/ftdebug.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftdebug.c -o CMakeFiles/freetype.dir/src/base/ftdebug.c.s

# Object files for target freetype
freetype_OBJECTS = \
"CMakeFiles/freetype.dir/src/autofit/autofit.c.o" \
"CMakeFiles/freetype.dir/src/base/ftbase.c.o" \
"CMakeFiles/freetype.dir/src/base/ftbbox.c.o" \
"CMakeFiles/freetype.dir/src/base/ftbdf.c.o" \
"CMakeFiles/freetype.dir/src/base/ftbitmap.c.o" \
"CMakeFiles/freetype.dir/src/base/ftcid.c.o" \
"CMakeFiles/freetype.dir/src/base/ftfstype.c.o" \
"CMakeFiles/freetype.dir/src/base/ftgasp.c.o" \
"CMakeFiles/freetype.dir/src/base/ftglyph.c.o" \
"CMakeFiles/freetype.dir/src/base/ftgxval.c.o" \
"CMakeFiles/freetype.dir/src/base/ftinit.c.o" \
"CMakeFiles/freetype.dir/src/base/ftmm.c.o" \
"CMakeFiles/freetype.dir/src/base/ftotval.c.o" \
"CMakeFiles/freetype.dir/src/base/ftpatent.c.o" \
"CMakeFiles/freetype.dir/src/base/ftpfr.c.o" \
"CMakeFiles/freetype.dir/src/base/ftstroke.c.o" \
"CMakeFiles/freetype.dir/src/base/ftsynth.c.o" \
"CMakeFiles/freetype.dir/src/base/fttype1.c.o" \
"CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o" \
"CMakeFiles/freetype.dir/src/bdf/bdf.c.o" \
"CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o" \
"CMakeFiles/freetype.dir/src/cache/ftcache.c.o" \
"CMakeFiles/freetype.dir/src/cff/cff.c.o" \
"CMakeFiles/freetype.dir/src/cid/type1cid.c.o" \
"CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o" \
"CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o" \
"CMakeFiles/freetype.dir/src/pcf/pcf.c.o" \
"CMakeFiles/freetype.dir/src/pfr/pfr.c.o" \
"CMakeFiles/freetype.dir/src/psaux/psaux.c.o" \
"CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o" \
"CMakeFiles/freetype.dir/src/psnames/psnames.c.o" \
"CMakeFiles/freetype.dir/src/raster/raster.c.o" \
"CMakeFiles/freetype.dir/src/sdf/sdf.c.o" \
"CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o" \
"CMakeFiles/freetype.dir/src/smooth/smooth.c.o" \
"CMakeFiles/freetype.dir/src/svg/svg.c.o" \
"CMakeFiles/freetype.dir/src/truetype/truetype.c.o" \
"CMakeFiles/freetype.dir/src/type1/type1.c.o" \
"CMakeFiles/freetype.dir/src/type42/type42.c.o" \
"CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o" \
"CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o" \
"CMakeFiles/freetype.dir/src/base/ftdebug.c.o"

# External object files for target freetype
freetype_EXTERNAL_OBJECTS =

libfreetype.a: CMakeFiles/freetype.dir/src/autofit/autofit.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftbase.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftbbox.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftbdf.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftbitmap.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftcid.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftfstype.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftgasp.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftglyph.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftgxval.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftinit.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftmm.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftotval.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftpatent.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftpfr.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftstroke.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftsynth.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/fttype1.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/bdf/bdf.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/cache/ftcache.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/cff/cff.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/cid/type1cid.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/pcf/pcf.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/pfr/pfr.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/psaux/psaux.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/psnames/psnames.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/raster/raster.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/sdf/sdf.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/smooth/smooth.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/svg/svg.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/truetype/truetype.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/type1/type1.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/type42/type42.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o
libfreetype.a: CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o
libfreetype.a: CMakeFiles/freetype.dir/src/base/ftdebug.c.o
libfreetype.a: CMakeFiles/freetype.dir/build.make
libfreetype.a: CMakeFiles/freetype.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Linking C static library libfreetype.a"
	$(CMAKE_COMMAND) -P CMakeFiles/freetype.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/freetype.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/freetype.dir/build: libfreetype.a
.PHONY : CMakeFiles/freetype.dir/build

CMakeFiles/freetype.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/freetype.dir/cmake_clean.cmake
.PHONY : CMakeFiles/freetype.dir/clean

CMakeFiles/freetype.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE-build/CMakeFiles/freetype.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/freetype.dir/depend

