file(REMOVE_RECURSE
  "CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o"
  "CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o.d"
  "CMakeFiles/freetype.dir/src/autofit/autofit.c.o"
  "CMakeFiles/freetype.dir/src/autofit/autofit.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftbase.c.o"
  "CMakeFiles/freetype.dir/src/base/ftbase.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftbbox.c.o"
  "CMakeFiles/freetype.dir/src/base/ftbbox.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftbdf.c.o"
  "CMakeFiles/freetype.dir/src/base/ftbdf.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftbitmap.c.o"
  "CMakeFiles/freetype.dir/src/base/ftbitmap.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftcid.c.o"
  "CMakeFiles/freetype.dir/src/base/ftcid.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftdebug.c.o"
  "CMakeFiles/freetype.dir/src/base/ftdebug.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftfstype.c.o"
  "CMakeFiles/freetype.dir/src/base/ftfstype.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftgasp.c.o"
  "CMakeFiles/freetype.dir/src/base/ftgasp.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftglyph.c.o"
  "CMakeFiles/freetype.dir/src/base/ftglyph.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftgxval.c.o"
  "CMakeFiles/freetype.dir/src/base/ftgxval.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftinit.c.o"
  "CMakeFiles/freetype.dir/src/base/ftinit.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftmm.c.o"
  "CMakeFiles/freetype.dir/src/base/ftmm.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftotval.c.o"
  "CMakeFiles/freetype.dir/src/base/ftotval.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftpatent.c.o"
  "CMakeFiles/freetype.dir/src/base/ftpatent.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftpfr.c.o"
  "CMakeFiles/freetype.dir/src/base/ftpfr.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftstroke.c.o"
  "CMakeFiles/freetype.dir/src/base/ftstroke.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftsynth.c.o"
  "CMakeFiles/freetype.dir/src/base/ftsynth.c.o.d"
  "CMakeFiles/freetype.dir/src/base/fttype1.c.o"
  "CMakeFiles/freetype.dir/src/base/fttype1.c.o.d"
  "CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o"
  "CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o.d"
  "CMakeFiles/freetype.dir/src/bdf/bdf.c.o"
  "CMakeFiles/freetype.dir/src/bdf/bdf.c.o.d"
  "CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o"
  "CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o.d"
  "CMakeFiles/freetype.dir/src/cache/ftcache.c.o"
  "CMakeFiles/freetype.dir/src/cache/ftcache.c.o.d"
  "CMakeFiles/freetype.dir/src/cff/cff.c.o"
  "CMakeFiles/freetype.dir/src/cff/cff.c.o.d"
  "CMakeFiles/freetype.dir/src/cid/type1cid.c.o"
  "CMakeFiles/freetype.dir/src/cid/type1cid.c.o.d"
  "CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o"
  "CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o.d"
  "CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o"
  "CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o.d"
  "CMakeFiles/freetype.dir/src/pcf/pcf.c.o"
  "CMakeFiles/freetype.dir/src/pcf/pcf.c.o.d"
  "CMakeFiles/freetype.dir/src/pfr/pfr.c.o"
  "CMakeFiles/freetype.dir/src/pfr/pfr.c.o.d"
  "CMakeFiles/freetype.dir/src/psaux/psaux.c.o"
  "CMakeFiles/freetype.dir/src/psaux/psaux.c.o.d"
  "CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o"
  "CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o.d"
  "CMakeFiles/freetype.dir/src/psnames/psnames.c.o"
  "CMakeFiles/freetype.dir/src/psnames/psnames.c.o.d"
  "CMakeFiles/freetype.dir/src/raster/raster.c.o"
  "CMakeFiles/freetype.dir/src/raster/raster.c.o.d"
  "CMakeFiles/freetype.dir/src/sdf/sdf.c.o"
  "CMakeFiles/freetype.dir/src/sdf/sdf.c.o.d"
  "CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o"
  "CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o.d"
  "CMakeFiles/freetype.dir/src/smooth/smooth.c.o"
  "CMakeFiles/freetype.dir/src/smooth/smooth.c.o.d"
  "CMakeFiles/freetype.dir/src/svg/svg.c.o"
  "CMakeFiles/freetype.dir/src/svg/svg.c.o.d"
  "CMakeFiles/freetype.dir/src/truetype/truetype.c.o"
  "CMakeFiles/freetype.dir/src/truetype/truetype.c.o.d"
  "CMakeFiles/freetype.dir/src/type1/type1.c.o"
  "CMakeFiles/freetype.dir/src/type1/type1.c.o.d"
  "CMakeFiles/freetype.dir/src/type42/type42.c.o"
  "CMakeFiles/freetype.dir/src/type42/type42.c.o.d"
  "CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o"
  "CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o.d"
  "libfreetype.a"
  "libfreetype.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang C)
  include(CMakeFiles/freetype.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
