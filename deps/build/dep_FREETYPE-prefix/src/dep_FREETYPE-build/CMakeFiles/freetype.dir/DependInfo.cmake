
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/builds/unix/ftsystem.c" "CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o" "gcc" "CMakeFiles/freetype.dir/builds/unix/ftsystem.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/autofit/autofit.c" "CMakeFiles/freetype.dir/src/autofit/autofit.c.o" "gcc" "CMakeFiles/freetype.dir/src/autofit/autofit.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbase.c" "CMakeFiles/freetype.dir/src/base/ftbase.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftbase.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbbox.c" "CMakeFiles/freetype.dir/src/base/ftbbox.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftbbox.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbdf.c" "CMakeFiles/freetype.dir/src/base/ftbdf.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftbdf.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftbitmap.c" "CMakeFiles/freetype.dir/src/base/ftbitmap.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftbitmap.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftcid.c" "CMakeFiles/freetype.dir/src/base/ftcid.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftcid.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftdebug.c" "CMakeFiles/freetype.dir/src/base/ftdebug.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftdebug.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftfstype.c" "CMakeFiles/freetype.dir/src/base/ftfstype.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftfstype.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftgasp.c" "CMakeFiles/freetype.dir/src/base/ftgasp.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftgasp.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftglyph.c" "CMakeFiles/freetype.dir/src/base/ftglyph.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftglyph.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftgxval.c" "CMakeFiles/freetype.dir/src/base/ftgxval.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftgxval.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftinit.c" "CMakeFiles/freetype.dir/src/base/ftinit.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftinit.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftmm.c" "CMakeFiles/freetype.dir/src/base/ftmm.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftmm.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftotval.c" "CMakeFiles/freetype.dir/src/base/ftotval.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftotval.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftpatent.c" "CMakeFiles/freetype.dir/src/base/ftpatent.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftpatent.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftpfr.c" "CMakeFiles/freetype.dir/src/base/ftpfr.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftpfr.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftstroke.c" "CMakeFiles/freetype.dir/src/base/ftstroke.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftstroke.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftsynth.c" "CMakeFiles/freetype.dir/src/base/ftsynth.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftsynth.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/fttype1.c" "CMakeFiles/freetype.dir/src/base/fttype1.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/fttype1.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/base/ftwinfnt.c" "CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o" "gcc" "CMakeFiles/freetype.dir/src/base/ftwinfnt.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/bdf/bdf.c" "CMakeFiles/freetype.dir/src/bdf/bdf.c.o" "gcc" "CMakeFiles/freetype.dir/src/bdf/bdf.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/bzip2/ftbzip2.c" "CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o" "gcc" "CMakeFiles/freetype.dir/src/bzip2/ftbzip2.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cache/ftcache.c" "CMakeFiles/freetype.dir/src/cache/ftcache.c.o" "gcc" "CMakeFiles/freetype.dir/src/cache/ftcache.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cff/cff.c" "CMakeFiles/freetype.dir/src/cff/cff.c.o" "gcc" "CMakeFiles/freetype.dir/src/cff/cff.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/cid/type1cid.c" "CMakeFiles/freetype.dir/src/cid/type1cid.c.o" "gcc" "CMakeFiles/freetype.dir/src/cid/type1cid.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/gzip/ftgzip.c" "CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o" "gcc" "CMakeFiles/freetype.dir/src/gzip/ftgzip.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/lzw/ftlzw.c" "CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o" "gcc" "CMakeFiles/freetype.dir/src/lzw/ftlzw.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pcf/pcf.c" "CMakeFiles/freetype.dir/src/pcf/pcf.c.o" "gcc" "CMakeFiles/freetype.dir/src/pcf/pcf.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pfr/pfr.c" "CMakeFiles/freetype.dir/src/pfr/pfr.c.o" "gcc" "CMakeFiles/freetype.dir/src/pfr/pfr.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/psaux/psaux.c" "CMakeFiles/freetype.dir/src/psaux/psaux.c.o" "gcc" "CMakeFiles/freetype.dir/src/psaux/psaux.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/pshinter/pshinter.c" "CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o" "gcc" "CMakeFiles/freetype.dir/src/pshinter/pshinter.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/psnames/psnames.c" "CMakeFiles/freetype.dir/src/psnames/psnames.c.o" "gcc" "CMakeFiles/freetype.dir/src/psnames/psnames.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/raster/raster.c" "CMakeFiles/freetype.dir/src/raster/raster.c.o" "gcc" "CMakeFiles/freetype.dir/src/raster/raster.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/sdf/sdf.c" "CMakeFiles/freetype.dir/src/sdf/sdf.c.o" "gcc" "CMakeFiles/freetype.dir/src/sdf/sdf.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/sfnt/sfnt.c" "CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o" "gcc" "CMakeFiles/freetype.dir/src/sfnt/sfnt.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/smooth/smooth.c" "CMakeFiles/freetype.dir/src/smooth/smooth.c.o" "gcc" "CMakeFiles/freetype.dir/src/smooth/smooth.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/svg/svg.c" "CMakeFiles/freetype.dir/src/svg/svg.c.o" "gcc" "CMakeFiles/freetype.dir/src/svg/svg.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/truetype/truetype.c" "CMakeFiles/freetype.dir/src/truetype/truetype.c.o" "gcc" "CMakeFiles/freetype.dir/src/truetype/truetype.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/type1/type1.c" "CMakeFiles/freetype.dir/src/type1/type1.c.o" "gcc" "CMakeFiles/freetype.dir/src/type1/type1.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/type42/type42.c" "CMakeFiles/freetype.dir/src/type42/type42.c.o" "gcc" "CMakeFiles/freetype.dir/src/type42/type42.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_FREETYPE-prefix/src/dep_FREETYPE/src/winfonts/winfnt.c" "CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o" "gcc" "CMakeFiles/freetype.dir/src/winfonts/winfnt.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
