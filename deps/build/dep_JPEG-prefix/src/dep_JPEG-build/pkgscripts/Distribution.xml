<?xml version="1.0" encoding="utf-8"?>
<installer-gui-script minSpecVersion="1">
	<title>libjpeg-turbo</title>
	<welcome file="Welcome.rtf" />
	<readme file="ReadMe.txt" />
	<license file="License.rtf" />
	<domains
		enable_anywhere="false"
		enable_currentUserHome="false"
		enable_localSystem="true"
	/>
	<options customize="never" />
	<choices-outline>
		<line choice="default">
			<line choice="com.libjpeg-turbo.libjpeg-turbo"/>
		</line>
	</choices-outline>
	<choice id="default"/>
	<choice id="com.libjpeg-turbo.libjpeg-turbo" visible="false">
		<pkg-ref id="com.libjpeg-turbo.libjpeg-turbo"/>
	</choice>
	<pkg-ref auth="root"
		id="com.libjpeg-turbo.libjpeg-turbo">libjpeg-turbo.pkg</pkg-ref>
</installer-gui-script>
