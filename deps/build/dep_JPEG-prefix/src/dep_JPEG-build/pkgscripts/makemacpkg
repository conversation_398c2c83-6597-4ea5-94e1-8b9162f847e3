#!/bin/sh

set -u
set -e
trap onexit INT
trap onexit TERM
trap onexit EXIT

TMPDIR=

onexit()
{
	if [ ! "$TMPDIR" = "" ]; then
		rm -rf $TMPDIR
	fi
}

safedirmove ()
{
	if [ "$1" = "$2" ]; then
		return 0
	fi
	if [ "$1" = "" -o ! -d "$1" ]; then
		echo safedirmove: source dir $1 is not valid
		return 1
	fi
	if [ "$2" = "" -o -e "$2" ]; then
		echo safedirmove: dest dir $2 is not valid
		return 1
	fi
	if [ "$3" = "" -o -e "$3" ]; then
		echo safedirmove: tmp dir $3 is not valid
		return 1
	fi
	mkdir -p $3
	mv $1/* $3/
	rmdir $1
	mkdir -p $2
	mv $3/* $2/
	rmdir $3
	return 0
}

usage()
{
	echo "$0 [-lipo [path to lipo]]"
	exit 1
}

PKGNAME=libjpeg-turbo
VERSION=3.0.1
BUILD=20250916
SRCDIR=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG
BUILDDIRARMV8=
WITH_JAVA=0
MACOS_APP_CERT_NAME=""
MACOS_INST_CERT_NAME=""
LIPO=lipo

PREFIX=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local
BINDIR=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/bin
DOCDIR=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/share/doc/libjpeg-turbo
LIBDIR=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib

LIBJPEG_DSO_NAME=libjpeg.62.4.0.dylib
TURBOJPEG_DSO_NAME=libturbojpeg.0.3.0.dylib

while [ $# -gt 0 ]; do
	case $1 in
	-h*)
		usage 0
		;;
	-lipo)
		if [ $# -gt 1 ]; then
			if [[ ! "$2" =~ -.* ]]; then
				LIPO=$2;  shift
			fi
		fi
		;;
	esac
	shift
done

if [ -f $PKGNAME-$VERSION.dmg ]; then
	rm -f $PKGNAME-$VERSION.dmg
fi

umask 022
TMPDIR=`mktemp -d /tmp/$PKGNAME-build.XXXXXX`
PKGROOT=$TMPDIR/pkg/Package_Root
mkdir -p $PKGROOT

DESTDIR=$PKGROOT /usr/bin/make install

if [ "$PREFIX" = "/opt/libjpeg-turbo" -a "$DOCDIR" = "/opt/libjpeg-turbo/doc" ]; then
	mkdir -p $PKGROOT/Library/Documentation
	safedirmove $PKGROOT$DOCDIR $PKGROOT/Library/Documentation/$PKGNAME $TMPDIR/__tmpdoc
	ln -fs /Library/Documentation/$PKGNAME $PKGROOT$DOCDIR
fi

install_subbuild()
{
	BUILDDIR=$1
	ARCHNAME=$2
	DIRNAME=$3
	LIPOARCH=$4

	if [ ! -d $BUILDDIR ]; then
		echo ERROR: $ARCHNAME build directory $BUILDDIR does not exist
		exit 1
	fi
	if [ ! -f $BUILDDIR/Makefile -a ! -f $BUILDDIR/build.ninja ]; then
		echo ERROR: $ARCHNAME build directory $BUILDDIR is not configured
		exit 1
	fi
	mkdir -p $TMPDIR/dist.$DIRNAME
	pushd $BUILDDIR
	DESTDIR=$TMPDIR/dist.$DIRNAME /usr/bin/make install
	popd
	$LIPO -create \
		$PKGROOT/$LIBDIR/$LIBJPEG_DSO_NAME \
		-arch $LIPOARCH $TMPDIR/dist.$DIRNAME/$LIBDIR/$LIBJPEG_DSO_NAME \
		-output $PKGROOT/$LIBDIR/$LIBJPEG_DSO_NAME
	$LIPO -create \
		$PKGROOT/$LIBDIR/libjpeg.a \
		-arch $LIPOARCH $TMPDIR/dist.$DIRNAME/$LIBDIR/libjpeg.a \
		-output $PKGROOT/$LIBDIR/libjpeg.a
	$LIPO -create \
		$PKGROOT/$LIBDIR/$TURBOJPEG_DSO_NAME \
		-arch $LIPOARCH $TMPDIR/dist.$DIRNAME/$LIBDIR/$TURBOJPEG_DSO_NAME \
		-output $PKGROOT/$LIBDIR/$TURBOJPEG_DSO_NAME
	$LIPO -create \
		$PKGROOT/$LIBDIR/libturbojpeg.a \
		-arch $LIPOARCH $TMPDIR/dist.$DIRNAME/$LIBDIR/libturbojpeg.a \
		-output $PKGROOT/$LIBDIR/libturbojpeg.a
	$LIPO -create \
		$PKGROOT/$BINDIR/cjpeg \
		-arch $LIPOARCH $TMPDIR/dist.$DIRNAME/$BINDIR/cjpeg \
		-output $PKGROOT/$BINDIR/cjpeg
	$LIPO -create \
		$PKGROOT/$BINDIR/djpeg \
		-arch $LIPOARCH $TMPDIR/dist.$DIRNAME/$BINDIR/djpeg \
		-output $PKGROOT/$BINDIR/djpeg
	$LIPO -create \
		$PKGROOT/$BINDIR/jpegtran \
		-arch $LIPOARCH $TMPDIR/dist.$DIRNAME/$BINDIR/jpegtran \
		-output $PKGROOT/$BINDIR/jpegtran
	$LIPO -create \
		$PKGROOT/$BINDIR/tjbench \
		-arch $LIPOARCH $TMPDIR/dist.$DIRNAME/$BINDIR/tjbench \
		-output $PKGROOT/$BINDIR/tjbench
	$LIPO -create \
		$PKGROOT/$BINDIR/rdjpgcom \
		-arch $LIPOARCH $TMPDIR/dist.$DIRNAME/$BINDIR/rdjpgcom \
		-output $PKGROOT/$BINDIR/rdjpgcom
	$LIPO -create \
		$PKGROOT/$BINDIR/wrjpgcom \
		-arch $LIPOARCH $TMPDIR/dist.$DIRNAME/$BINDIR/wrjpgcom \
		-output $PKGROOT/$BINDIR/wrjpgcom
}

if [ "$BUILDDIRARMV8" != "" ]; then
	install_subbuild $BUILDDIRARMV8 Armv8 armv8 arm64
fi

install_name_tool -id $LIBDIR/$LIBJPEG_DSO_NAME $PKGROOT/$LIBDIR/$LIBJPEG_DSO_NAME
install_name_tool -id $LIBDIR/$TURBOJPEG_DSO_NAME $PKGROOT/$LIBDIR/$TURBOJPEG_DSO_NAME

if [ "$PREFIX" = "/opt/libjpeg-turbo" -a "$LIBDIR" = "/opt/libjpeg-turbo/lib" ]; then
	if [ ! -h $PKGROOT/$PREFIX/lib64 ]; then
		ln -fs lib $PKGROOT/$PREFIX/lib64
	fi
fi

mkdir -p $TMPDIR/pkg

install -m 755 pkgscripts/uninstall $PKGROOT/$BINDIR/

find $PKGROOT -type f | while read file; do xattr -c $file; done

cp $SRCDIR/release/License.rtf pkgscripts/Welcome.rtf $SRCDIR/release/ReadMe.txt $TMPDIR/pkg/

mkdir $TMPDIR/dmg
pkgbuild --root $PKGROOT --version $VERSION.$BUILD --identifier com.libjpeg-turbo.libjpeg-turbo \
	$TMPDIR/pkg/$PKGNAME.pkg
SUFFIX=
if [ "$MACOS_INST_CERT_NAME" != "" ]; then
	SUFFIX=-unsigned
fi
productbuild --distribution pkgscripts/Distribution.xml \
	--package-path $TMPDIR/pkg/ --resources $TMPDIR/pkg/ \
	$TMPDIR/dmg/$PKGNAME$SUFFIX.pkg
if [ "$MACOS_INST_CERT_NAME" != "" ]; then
	productsign --sign "$MACOS_INST_CERT_NAME" --timestamp \
		$TMPDIR/dmg/$PKGNAME$SUFFIX.pkg $TMPDIR/dmg/$PKGNAME.pkg
	rm -r $TMPDIR/dmg/$PKGNAME$SUFFIX.pkg
	pkgutil --check-signature $TMPDIR/dmg/$PKGNAME.pkg
fi
hdiutil create -fs HFS+ -volname $PKGNAME-$VERSION \
	-srcfolder "$TMPDIR/dmg" $TMPDIR/$PKGNAME-$VERSION.dmg
if [ "$MACOS_APP_CERT_NAME" != "" ]; then
	codesign -s "$MACOS_APP_CERT_NAME" --timestamp $TMPDIR/$PKGNAME-$VERSION.dmg
	codesign -vv $TMPDIR/$PKGNAME-$VERSION.dmg
fi
cp $TMPDIR/$PKGNAME-$VERSION.dmg .

exit
