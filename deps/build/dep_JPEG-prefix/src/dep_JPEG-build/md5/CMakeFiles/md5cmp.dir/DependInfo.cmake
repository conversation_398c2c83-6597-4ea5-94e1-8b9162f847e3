
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/md5/md5.c" "md5/CMakeFiles/md5cmp.dir/md5.c.o" "gcc" "md5/CMakeFiles/md5cmp.dir/md5.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/md5/md5cmp.c" "md5/CMakeFiles/md5cmp.dir/md5cmp.c.o" "gcc" "md5/CMakeFiles/md5cmp.dir/md5cmp.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/md5/md5hl.c" "md5/CMakeFiles/md5cmp.dir/md5hl.c.o" "gcc" "md5/CMakeFiles/md5cmp.dir/md5hl.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
