# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/Applications/CMake.app/Contents/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Applications/CMake.app/Contents/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Applications/CMake.app/Contents/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/md5//CMakeFiles/progress.marks
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 md5/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 md5/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 md5/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 md5/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
md5/CMakeFiles/md5cmp.dir/rule:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 md5/CMakeFiles/md5cmp.dir/rule
.PHONY : md5/CMakeFiles/md5cmp.dir/rule

# Convenience name for target.
md5cmp: md5/CMakeFiles/md5cmp.dir/rule
.PHONY : md5cmp

# fast build rule for target.
md5cmp/fast:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f md5/CMakeFiles/md5cmp.dir/build.make md5/CMakeFiles/md5cmp.dir/build
.PHONY : md5cmp/fast

md5.o: md5.c.o
.PHONY : md5.o

# target to build an object file
md5.c.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f md5/CMakeFiles/md5cmp.dir/build.make md5/CMakeFiles/md5cmp.dir/md5.c.o
.PHONY : md5.c.o

md5.i: md5.c.i
.PHONY : md5.i

# target to preprocess a source file
md5.c.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f md5/CMakeFiles/md5cmp.dir/build.make md5/CMakeFiles/md5cmp.dir/md5.c.i
.PHONY : md5.c.i

md5.s: md5.c.s
.PHONY : md5.s

# target to generate assembly for a file
md5.c.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f md5/CMakeFiles/md5cmp.dir/build.make md5/CMakeFiles/md5cmp.dir/md5.c.s
.PHONY : md5.c.s

md5cmp.o: md5cmp.c.o
.PHONY : md5cmp.o

# target to build an object file
md5cmp.c.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f md5/CMakeFiles/md5cmp.dir/build.make md5/CMakeFiles/md5cmp.dir/md5cmp.c.o
.PHONY : md5cmp.c.o

md5cmp.i: md5cmp.c.i
.PHONY : md5cmp.i

# target to preprocess a source file
md5cmp.c.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f md5/CMakeFiles/md5cmp.dir/build.make md5/CMakeFiles/md5cmp.dir/md5cmp.c.i
.PHONY : md5cmp.c.i

md5cmp.s: md5cmp.c.s
.PHONY : md5cmp.s

# target to generate assembly for a file
md5cmp.c.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f md5/CMakeFiles/md5cmp.dir/build.make md5/CMakeFiles/md5cmp.dir/md5cmp.c.s
.PHONY : md5cmp.c.s

md5hl.o: md5hl.c.o
.PHONY : md5hl.o

# target to build an object file
md5hl.c.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f md5/CMakeFiles/md5cmp.dir/build.make md5/CMakeFiles/md5cmp.dir/md5hl.c.o
.PHONY : md5hl.c.o

md5hl.i: md5hl.c.i
.PHONY : md5hl.i

# target to preprocess a source file
md5hl.c.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f md5/CMakeFiles/md5cmp.dir/build.make md5/CMakeFiles/md5cmp.dir/md5hl.c.i
.PHONY : md5hl.c.i

md5hl.s: md5hl.c.s
.PHONY : md5hl.s

# target to generate assembly for a file
md5hl.c.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(MAKE) $(MAKESILENT) -f md5/CMakeFiles/md5cmp.dir/build.make md5/CMakeFiles/md5cmp.dir/md5hl.c.s
.PHONY : md5hl.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... md5cmp"
	@echo "... md5.o"
	@echo "... md5.i"
	@echo "... md5.s"
	@echo "... md5cmp.o"
	@echo "... md5cmp.i"
	@echo "... md5cmp.s"
	@echo "... md5hl.o"
	@echo "... md5hl.i"
	@echo "... md5hl.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

