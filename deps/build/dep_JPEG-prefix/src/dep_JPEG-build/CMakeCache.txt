# This is the CMakeCache file.
# For build in directory: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build
# It was generated by CMake: /Applications/CMake.app/Contents/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Directory containing Armv8 iOS or macOS build to include in universal
// binaries
ARMV8_BUILD:PATH=

//Build string (default: 20250916)
BUILD:STRING=20250916

//No help, variable specified on the command line.
BUILD_SHARED_LIBS:BOOL=OFF

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:STRING=Release

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//No help, variable specified on the command line.
CMAKE_CXX_COMPILER:STRING=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++

//No help, variable specified on the command line.
CMAKE_CXX_FLAGS:UNINITIALIZED=-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new

//C compiler
CMAKE_C_COMPILER:STRING=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//No help, variable specified on the command line.
CMAKE_DEBUG_POSTFIX:STRING=d

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//No help, variable specified on the command line.
CMAKE_FIND_APPBUNDLE:UNINITIALIZED=LAST

//No help, variable specified on the command line.
CMAKE_FIND_FRAMEWORK:UNINITIALIZED=LAST

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/pkgRedirects

//Directory into which user executables should be installed (Default:
// bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//The directory under which read-only architecture-independent
// data files should be installed (Default: <CMAKE_INSTALL_DATAROOTDIR>)
CMAKE_INSTALL_DATADIR:PATH=<CMAKE_INSTALL_DATAROOTDIR>

//The root of the directory tree for read-only architecture-independent
// data files (Default: share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//The directory into which documentation files (other than info
// files) should be installed (Default: <CMAKE_INSTALL_DATAROOTDIR>/doc/libjpeg-turbo)
CMAKE_INSTALL_DOCDIR:PATH=<CMAKE_INSTALL_DATAROOTDIR>/doc/libjpeg-turbo

//Directory into which C header files should be installed (Default:
// include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//The directory into which info documentation files should be installed
// (Default: <CMAKE_INSTALL_DATAROOTDIR>/info)
CMAKE_INSTALL_INFODIR:PATH=<CMAKE_INSTALL_DATAROOTDIR>/info

//Directory into which object files and object code libraries should
// be installed (Default: lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Directory under which executables run by other programs should
// be installed (Default: libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//The directory under which locale-specific message catalogs should
// be installed (Default: <CMAKE_INSTALL_DATAROOTDIR>/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=<CMAKE_INSTALL_DATAROOTDIR>/locale

//Directory into which machine-specific run-time-modifiable data
// files should be installed (Default: var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//The directory under which man pages should be installed (Default:
// <CMAKE_INSTALL_DATAROOTDIR>/man)
CMAKE_INSTALL_MANDIR:PATH=<CMAKE_INSTALL_DATAROOTDIR>/man

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//PATH (Default: /usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//No help, variable specified on the command line.
CMAKE_INSTALL_PREFIX:STRING=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local

//Directory into which system admin executables should be installed
// (Default: sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Directory into which architecture-independent run-time-modifiable
// data files should be installed (Default: com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Directory into which machine-specific read-only ASCII data and
// configuration files should be installed (Default: etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//No help, variable specified on the command line.
CMAKE_MODULE_PATH:STRING=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules

//Path to a program.
CMAKE_NM:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=CMAKE_OBJCOPY-NOTFOUND

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=

//Minimum OS X version to target for deployment (at runtime); newer
// APIs weak linked. Set to empty string for default value.
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=10.15

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk

//No help, variable specified on the command line.
CMAKE_POSITION_INDEPENDENT_CODE:UNINITIALIZED=ON

//No help, variable specified on the command line.
CMAKE_PREFIX_PATH:STRING=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=libjpeg-turbo

//Path to a program.
CMAKE_RANLIB:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi

//No help, variable specified on the command line.
CMAKE_TOOLCHAIN_FILE:STRING=

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Build shared libraries
ENABLE_SHARED:BOOL=OFF

//Build static libraries
ENABLE_STATIC:BOOL=ON

//The type of floating point math used by the 12-bit-per-sample
// floating point DCT/IDCT algorithms.  This tells the testing
// system which numerical results it should expect from those tests.
//  [sse = libjpeg-turbo x86/x86-64 SIMD extensions, no-fp-contract
// = generic FPU with floating point expression contraction disabled,
// fp-contract = generic FPU with floating point expression contraction
// enabled, 387 = 387 FPU, msvc = 32-bit Visual Studio] (default
// = fp-contract)
FLOATTEST12:STRING=fp-contract

//The type of floating point math used by the 8-bit-per-sample
// floating point DCT/IDCT algorithms.  This tells the testing
// system which numerical results it should expect from those tests.
//  [sse = libjpeg-turbo x86/x86-64 SIMD extensions, no-fp-contract
// = generic FPU with floating point expression contraction disabled,
// fp-contract = generic FPU with floating point expression contraction
// enabled, 387 = 387 FPU, msvc = 32-bit Visual Studio] (default
// = fp-contract)
FLOATTEST8:STRING=fp-contract

//Force function inlining
FORCE_INLINE:BOOL=ON

//Name of the Developer ID Application certificate (in the macOS
// keychain) that should be used to sign the libjpeg-turbo DMG.
//  Leave this blank to generate an unsigned DMG.
MACOS_APP_CERT_NAME:STRING=

//Name of the Developer ID Installer certificate (in the macOS
// keychain) that should be used to sign the libjpeg-turbo installer
// package.  Leave this blank to generate an unsigned package.
MACOS_INST_CERT_NAME:STRING=

//Because GCC (as of this writing) and some older versions of Clang
// do not have a full or optimal set of Neon intrinsics, for performance
// reasons, the default when building libjpeg-turbo with those
// compilers is to continue using the older GAS implementation
// of the Neon SIMD extensions for certain algorithms.  Setting
// this option forces the full Neon intrinsics implementation to
// be used with all compilers.  Unsetting this option forces the
// hybrid GAS/intrinsics implementation to be used with all compilers.
NEON_INTRINSICS:BOOL=ON

//E-mail of project maintainer to be included in distribution package
// descriptions (default: <EMAIL>
PKGEMAIL:STRING=<EMAIL>

//Globally unique package identifier (reverse DNS notation) (default:
// com.libjpeg-turbo.libjpeg-turbo)
PKGID:STRING=com.libjpeg-turbo.libjpeg-turbo

//Distribution package name (default: libjpeg-turbo)
PKGNAME:STRING=libjpeg-turbo

//URL of project web site to be included in distribution package
// descriptions (default: http://www.libjpeg-turbo.org)
PKGURL:STRING=http://www.libjpeg-turbo.org

//Vendor name to be included in distribution package descriptions
// (default: The libjpeg-turbo Project)
PKGVENDOR:STRING=The libjpeg-turbo Project

//Generate a fatal error if SIMD extensions are not available for
// this platform (default is to fall back to a non-SIMD build)
REQUIRE_SIMD:BOOL=OFF

//Major version of the libjpeg API shared library (default: 62)
SO_MAJOR_VERSION:STRING=62

//Minor version of the libjpeg API shared library (default: 0)
SO_MINOR_VERSION:STRING=0

//Include arithmetic decoding support when emulating the libjpeg
// v6b API/ABI
WITH_ARITH_DEC:BOOL=ON

//Include arithmetic encoding support when emulating the libjpeg
// v6b API/ABI
WITH_ARITH_ENC:BOOL=ON

//Build fuzz targets
WITH_FUZZ:BOOL=OFF

//Build Java wrapper for the TurboJPEG API library (implies ENABLE_SHARED=1)
WITH_JAVA:BOOL=OFF

//Emulate libjpeg v7 API/ABI (this makes libjpeg-turbo backward-incompatible
// with libjpeg v6b)
WITH_JPEG7:BOOL=OFF

//Emulate libjpeg v8 API/ABI (this makes libjpeg-turbo backward-incompatible
// with libjpeg v6b)
WITH_JPEG8:BOOL=OFF

//Include SIMD extensions, if available for this platform
WITH_SIMD:BOOL=ON

//Include the TurboJPEG API library and associated test programs
WITH_TURBOJPEG:BOOL=ON

//Value Computed by CMake
libjpeg-turbo_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build

//Value Computed by CMake
libjpeg-turbo_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
libjpeg-turbo_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=27
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=9
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/Applications/CMake.app/Contents/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/Applications/CMake.app/Contents/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/Applications/CMake.app/Contents/bin/ctest
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/Applications/CMake.app/Contents/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=MACHO
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=0
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=0
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=0
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=0
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=0
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=0
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=3
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/Applications/CMake.app/Contents/share/cmake-3.27
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Test HAVE_BUILTIN_CTZL
HAVE_BUILTIN_CTZL:INTERNAL=1
//Result of TRY_COMPILE
HAVE_SIZE_T:INTERNAL=TRUE
//Have include stddef.h
HAVE_STDDEF_H:INTERNAL=1
//Have include stdint.h
HAVE_STDINT_H:INTERNAL=1
//Have include sys/types.h
HAVE_SYS_TYPES_H:INTERNAL=1
//Test HAVE_THREAD_LOCAL
HAVE_THREAD_LOCAL:INTERNAL=1
//Result of TRY_COMPILE
HAVE_UNSIGNED_LONG:INTERNAL=TRUE
//Test HAVE_VLD1Q_U8_X4
HAVE_VLD1Q_U8_X4:INTERNAL=1
//Test HAVE_VLD1_S16_X3
HAVE_VLD1_S16_X3:INTERNAL=1
//Test HAVE_VLD1_U16_X2
HAVE_VLD1_U16_X2:INTERNAL=1
//Test INLINE_WORKS
INLINE_WORKS:INTERNAL=1
//Test RIGHT_SHIFT_IS_UNSIGNED
RIGHT_SHIFT_IS_UNSIGNED:INTERNAL=
//Result of TRY_COMPILE
RIGHT_SHIFT_IS_UNSIGNED_COMPILED:INTERNAL=TRUE
//Result of try_run()
RIGHT_SHIFT_IS_UNSIGNED_EXITCODE:INTERNAL=1
//CHECK_TYPE_SIZE: sizeof(size_t)
SIZE_T:INTERNAL=8
//CHECK_TYPE_SIZE: sizeof(unsigned long)
UNSIGNED_LONG:INTERNAL=8
WITH_JPEG7_INT:INTERNAL=0
WITH_JPEG8_INT:INTERNAL=0
WITH_SIMD_INT:INTERNAL=1
//CMAKE_INSTALL_BINDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_BINDIR:INTERNAL=1
//CMAKE_INSTALL_DATADIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_DATADIR:INTERNAL=1
//CMAKE_INSTALL_DATAROOTDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_DATAROOTDIR:INTERNAL=1
//CMAKE_INSTALL_DOCDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_DOCDIR:INTERNAL=1
//CMAKE_INSTALL_INCLUDEDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_INCLUDEDIR:INTERNAL=1
//CMAKE_INSTALL_INFODIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_INFODIR:INTERNAL=1
//CMAKE_INSTALL_LIBDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_LIBDIR:INTERNAL=1
//CMAKE_INSTALL_LIBEXECDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_LIBEXECDIR:INTERNAL=1
//CMAKE_INSTALL_LOCALEDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_LOCALEDIR:INTERNAL=1
//CMAKE_INSTALL_LOCALSTATEDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_LOCALSTATEDIR:INTERNAL=1
//CMAKE_INSTALL_MANDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_MANDIR:INTERNAL=1
//CMAKE_INSTALL_OLDINCLUDEDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_OLDINCLUDEDIR:INTERNAL=1
//CMAKE_INSTALL_SBINDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_SBINDIR:INTERNAL=1
//CMAKE_INSTALL_SHAREDSTATEDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_SHAREDSTATEDIR:INTERNAL=1
//CMAKE_INSTALL_SYSCONFDIR has default value
_GNUInstallDirs_CMAKE_INSTALL_DEFAULT_SYSCONFDIR:INTERNAL=1
//CMAKE_INSTALL_BINDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_BINDIR:INTERNAL=bin
//CMAKE_INSTALL_DATADIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DATADIR:INTERNAL=<CMAKE_INSTALL_DATAROOTDIR>
//CMAKE_INSTALL_DATAROOTDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DATAROOTDIR:INTERNAL=share
//CMAKE_INSTALL_DEFAULT_BINDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_BINDIR:INTERNAL=bin
//CMAKE_INSTALL_DEFAULT_DATADIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_DATADIR:INTERNAL=<CMAKE_INSTALL_DATAROOTDIR>
//CMAKE_INSTALL_DEFAULT_DATAROOTDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_DATAROOTDIR:INTERNAL=share
//CMAKE_INSTALL_DEFAULT_DOCDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_DOCDIR:INTERNAL=<CMAKE_INSTALL_DATAROOTDIR>/doc/libjpeg-turbo
//CMAKE_INSTALL_DEFAULT_INCLUDEDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_INCLUDEDIR:INTERNAL=include
//CMAKE_INSTALL_DEFAULT_INFODIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_INFODIR:INTERNAL=<CMAKE_INSTALL_DATAROOTDIR>/info
//CMAKE_INSTALL_DEFAULT_LIBDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_LIBDIR:INTERNAL=lib
//CMAKE_INSTALL_DEFAULT_LIBEXECDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_LIBEXECDIR:INTERNAL=libexec
//CMAKE_INSTALL_DEFAULT_LOCALEDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_LOCALEDIR:INTERNAL=<CMAKE_INSTALL_DATAROOTDIR>/locale
//CMAKE_INSTALL_DEFAULT_LOCALSTATEDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_LOCALSTATEDIR:INTERNAL=var
//CMAKE_INSTALL_DEFAULT_MANDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_MANDIR:INTERNAL=<CMAKE_INSTALL_DATAROOTDIR>/man
//CMAKE_INSTALL_DEFAULT_OLDINCLUDEDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_OLDINCLUDEDIR:INTERNAL=/usr/include
//CMAKE_INSTALL_DEFAULT_SBINDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_SBINDIR:INTERNAL=sbin
//CMAKE_INSTALL_DEFAULT_SHAREDSTATEDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_SHAREDSTATEDIR:INTERNAL=com
//CMAKE_INSTALL_DEFAULT_SYSCONFDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DEFAULT_SYSCONFDIR:INTERNAL=etc
//CMAKE_INSTALL_DOCDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_DOCDIR:INTERNAL=<CMAKE_INSTALL_DATAROOTDIR>/doc/libjpeg-turbo
//CMAKE_INSTALL_INCLUDEDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_INCLUDEDIR:INTERNAL=include
//CMAKE_INSTALL_INFODIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_INFODIR:INTERNAL=<CMAKE_INSTALL_DATAROOTDIR>/info
//CMAKE_INSTALL_LIBDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_LIBDIR:INTERNAL=lib
//CMAKE_INSTALL_LIBEXECDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_LIBEXECDIR:INTERNAL=libexec
//CMAKE_INSTALL_LOCALEDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_LOCALEDIR:INTERNAL=<CMAKE_INSTALL_DATAROOTDIR>/locale
//CMAKE_INSTALL_LOCALSTATEDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_LOCALSTATEDIR:INTERNAL=var
//CMAKE_INSTALL_MANDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_MANDIR:INTERNAL=<CMAKE_INSTALL_DATAROOTDIR>/man
//CMAKE_INSTALL_OLDINCLUDEDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_OLDINCLUDEDIR:INTERNAL=/usr/include
//CMAKE_INSTALL_SBINDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_SBINDIR:INTERNAL=sbin
//CMAKE_INSTALL_SHAREDSTATEDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_SHAREDSTATEDIR:INTERNAL=com
//CMAKE_INSTALL_SYSCONFDIR during last run
_GNUInstallDirs_CMAKE_INSTALL_LAST_SYSCONFDIR:INTERNAL=etc

