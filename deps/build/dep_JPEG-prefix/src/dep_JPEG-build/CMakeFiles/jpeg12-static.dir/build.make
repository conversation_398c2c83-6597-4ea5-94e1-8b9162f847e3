# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build

# Include any dependencies generated for this target.
include CMakeFiles/jpeg12-static.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/jpeg12-static.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/jpeg12-static.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/jpeg12-static.dir/flags.make

CMakeFiles/jpeg12-static.dir/jcapistd.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jcapistd.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c
CMakeFiles/jpeg12-static.dir/jcapistd.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/jpeg12-static.dir/jcapistd.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jcapistd.c.o -MF CMakeFiles/jpeg12-static.dir/jcapistd.c.o.d -o CMakeFiles/jpeg12-static.dir/jcapistd.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c

CMakeFiles/jpeg12-static.dir/jcapistd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jcapistd.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c > CMakeFiles/jpeg12-static.dir/jcapistd.c.i

CMakeFiles/jpeg12-static.dir/jcapistd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jcapistd.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c -o CMakeFiles/jpeg12-static.dir/jcapistd.c.s

CMakeFiles/jpeg12-static.dir/jccolor.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jccolor.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c
CMakeFiles/jpeg12-static.dir/jccolor.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/jpeg12-static.dir/jccolor.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jccolor.c.o -MF CMakeFiles/jpeg12-static.dir/jccolor.c.o.d -o CMakeFiles/jpeg12-static.dir/jccolor.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c

CMakeFiles/jpeg12-static.dir/jccolor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jccolor.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c > CMakeFiles/jpeg12-static.dir/jccolor.c.i

CMakeFiles/jpeg12-static.dir/jccolor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jccolor.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c -o CMakeFiles/jpeg12-static.dir/jccolor.c.s

CMakeFiles/jpeg12-static.dir/jcdiffct.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jcdiffct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c
CMakeFiles/jpeg12-static.dir/jcdiffct.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/jpeg12-static.dir/jcdiffct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jcdiffct.c.o -MF CMakeFiles/jpeg12-static.dir/jcdiffct.c.o.d -o CMakeFiles/jpeg12-static.dir/jcdiffct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c

CMakeFiles/jpeg12-static.dir/jcdiffct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jcdiffct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c > CMakeFiles/jpeg12-static.dir/jcdiffct.c.i

CMakeFiles/jpeg12-static.dir/jcdiffct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jcdiffct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c -o CMakeFiles/jpeg12-static.dir/jcdiffct.c.s

CMakeFiles/jpeg12-static.dir/jclossls.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jclossls.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c
CMakeFiles/jpeg12-static.dir/jclossls.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/jpeg12-static.dir/jclossls.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jclossls.c.o -MF CMakeFiles/jpeg12-static.dir/jclossls.c.o.d -o CMakeFiles/jpeg12-static.dir/jclossls.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c

CMakeFiles/jpeg12-static.dir/jclossls.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jclossls.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c > CMakeFiles/jpeg12-static.dir/jclossls.c.i

CMakeFiles/jpeg12-static.dir/jclossls.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jclossls.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c -o CMakeFiles/jpeg12-static.dir/jclossls.c.s

CMakeFiles/jpeg12-static.dir/jcmainct.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jcmainct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c
CMakeFiles/jpeg12-static.dir/jcmainct.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/jpeg12-static.dir/jcmainct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jcmainct.c.o -MF CMakeFiles/jpeg12-static.dir/jcmainct.c.o.d -o CMakeFiles/jpeg12-static.dir/jcmainct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c

CMakeFiles/jpeg12-static.dir/jcmainct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jcmainct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c > CMakeFiles/jpeg12-static.dir/jcmainct.c.i

CMakeFiles/jpeg12-static.dir/jcmainct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jcmainct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c -o CMakeFiles/jpeg12-static.dir/jcmainct.c.s

CMakeFiles/jpeg12-static.dir/jcprepct.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jcprepct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c
CMakeFiles/jpeg12-static.dir/jcprepct.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/jpeg12-static.dir/jcprepct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jcprepct.c.o -MF CMakeFiles/jpeg12-static.dir/jcprepct.c.o.d -o CMakeFiles/jpeg12-static.dir/jcprepct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c

CMakeFiles/jpeg12-static.dir/jcprepct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jcprepct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c > CMakeFiles/jpeg12-static.dir/jcprepct.c.i

CMakeFiles/jpeg12-static.dir/jcprepct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jcprepct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c -o CMakeFiles/jpeg12-static.dir/jcprepct.c.s

CMakeFiles/jpeg12-static.dir/jcsample.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jcsample.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c
CMakeFiles/jpeg12-static.dir/jcsample.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/jpeg12-static.dir/jcsample.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jcsample.c.o -MF CMakeFiles/jpeg12-static.dir/jcsample.c.o.d -o CMakeFiles/jpeg12-static.dir/jcsample.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c

CMakeFiles/jpeg12-static.dir/jcsample.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jcsample.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c > CMakeFiles/jpeg12-static.dir/jcsample.c.i

CMakeFiles/jpeg12-static.dir/jcsample.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jcsample.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c -o CMakeFiles/jpeg12-static.dir/jcsample.c.s

CMakeFiles/jpeg12-static.dir/jdapistd.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jdapistd.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c
CMakeFiles/jpeg12-static.dir/jdapistd.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/jpeg12-static.dir/jdapistd.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jdapistd.c.o -MF CMakeFiles/jpeg12-static.dir/jdapistd.c.o.d -o CMakeFiles/jpeg12-static.dir/jdapistd.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c

CMakeFiles/jpeg12-static.dir/jdapistd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jdapistd.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c > CMakeFiles/jpeg12-static.dir/jdapistd.c.i

CMakeFiles/jpeg12-static.dir/jdapistd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jdapistd.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c -o CMakeFiles/jpeg12-static.dir/jdapistd.c.s

CMakeFiles/jpeg12-static.dir/jdcolor.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jdcolor.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c
CMakeFiles/jpeg12-static.dir/jdcolor.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/jpeg12-static.dir/jdcolor.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jdcolor.c.o -MF CMakeFiles/jpeg12-static.dir/jdcolor.c.o.d -o CMakeFiles/jpeg12-static.dir/jdcolor.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c

CMakeFiles/jpeg12-static.dir/jdcolor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jdcolor.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c > CMakeFiles/jpeg12-static.dir/jdcolor.c.i

CMakeFiles/jpeg12-static.dir/jdcolor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jdcolor.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c -o CMakeFiles/jpeg12-static.dir/jdcolor.c.s

CMakeFiles/jpeg12-static.dir/jddiffct.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jddiffct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c
CMakeFiles/jpeg12-static.dir/jddiffct.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/jpeg12-static.dir/jddiffct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jddiffct.c.o -MF CMakeFiles/jpeg12-static.dir/jddiffct.c.o.d -o CMakeFiles/jpeg12-static.dir/jddiffct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c

CMakeFiles/jpeg12-static.dir/jddiffct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jddiffct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c > CMakeFiles/jpeg12-static.dir/jddiffct.c.i

CMakeFiles/jpeg12-static.dir/jddiffct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jddiffct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c -o CMakeFiles/jpeg12-static.dir/jddiffct.c.s

CMakeFiles/jpeg12-static.dir/jdlossls.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jdlossls.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c
CMakeFiles/jpeg12-static.dir/jdlossls.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/jpeg12-static.dir/jdlossls.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jdlossls.c.o -MF CMakeFiles/jpeg12-static.dir/jdlossls.c.o.d -o CMakeFiles/jpeg12-static.dir/jdlossls.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c

CMakeFiles/jpeg12-static.dir/jdlossls.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jdlossls.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c > CMakeFiles/jpeg12-static.dir/jdlossls.c.i

CMakeFiles/jpeg12-static.dir/jdlossls.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jdlossls.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c -o CMakeFiles/jpeg12-static.dir/jdlossls.c.s

CMakeFiles/jpeg12-static.dir/jdmainct.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jdmainct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c
CMakeFiles/jpeg12-static.dir/jdmainct.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/jpeg12-static.dir/jdmainct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jdmainct.c.o -MF CMakeFiles/jpeg12-static.dir/jdmainct.c.o.d -o CMakeFiles/jpeg12-static.dir/jdmainct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c

CMakeFiles/jpeg12-static.dir/jdmainct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jdmainct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c > CMakeFiles/jpeg12-static.dir/jdmainct.c.i

CMakeFiles/jpeg12-static.dir/jdmainct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jdmainct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c -o CMakeFiles/jpeg12-static.dir/jdmainct.c.s

CMakeFiles/jpeg12-static.dir/jdpostct.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jdpostct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c
CMakeFiles/jpeg12-static.dir/jdpostct.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/jpeg12-static.dir/jdpostct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jdpostct.c.o -MF CMakeFiles/jpeg12-static.dir/jdpostct.c.o.d -o CMakeFiles/jpeg12-static.dir/jdpostct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c

CMakeFiles/jpeg12-static.dir/jdpostct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jdpostct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c > CMakeFiles/jpeg12-static.dir/jdpostct.c.i

CMakeFiles/jpeg12-static.dir/jdpostct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jdpostct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c -o CMakeFiles/jpeg12-static.dir/jdpostct.c.s

CMakeFiles/jpeg12-static.dir/jdsample.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jdsample.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c
CMakeFiles/jpeg12-static.dir/jdsample.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/jpeg12-static.dir/jdsample.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jdsample.c.o -MF CMakeFiles/jpeg12-static.dir/jdsample.c.o.d -o CMakeFiles/jpeg12-static.dir/jdsample.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c

CMakeFiles/jpeg12-static.dir/jdsample.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jdsample.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c > CMakeFiles/jpeg12-static.dir/jdsample.c.i

CMakeFiles/jpeg12-static.dir/jdsample.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jdsample.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c -o CMakeFiles/jpeg12-static.dir/jdsample.c.s

CMakeFiles/jpeg12-static.dir/jutils.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jutils.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c
CMakeFiles/jpeg12-static.dir/jutils.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/jpeg12-static.dir/jutils.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jutils.c.o -MF CMakeFiles/jpeg12-static.dir/jutils.c.o.d -o CMakeFiles/jpeg12-static.dir/jutils.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c

CMakeFiles/jpeg12-static.dir/jutils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jutils.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c > CMakeFiles/jpeg12-static.dir/jutils.c.i

CMakeFiles/jpeg12-static.dir/jutils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jutils.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c -o CMakeFiles/jpeg12-static.dir/jutils.c.s

CMakeFiles/jpeg12-static.dir/jccoefct.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jccoefct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccoefct.c
CMakeFiles/jpeg12-static.dir/jccoefct.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/jpeg12-static.dir/jccoefct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jccoefct.c.o -MF CMakeFiles/jpeg12-static.dir/jccoefct.c.o.d -o CMakeFiles/jpeg12-static.dir/jccoefct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccoefct.c

CMakeFiles/jpeg12-static.dir/jccoefct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jccoefct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccoefct.c > CMakeFiles/jpeg12-static.dir/jccoefct.c.i

CMakeFiles/jpeg12-static.dir/jccoefct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jccoefct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccoefct.c -o CMakeFiles/jpeg12-static.dir/jccoefct.c.s

CMakeFiles/jpeg12-static.dir/jcdctmgr.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jcdctmgr.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdctmgr.c
CMakeFiles/jpeg12-static.dir/jcdctmgr.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object CMakeFiles/jpeg12-static.dir/jcdctmgr.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jcdctmgr.c.o -MF CMakeFiles/jpeg12-static.dir/jcdctmgr.c.o.d -o CMakeFiles/jpeg12-static.dir/jcdctmgr.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdctmgr.c

CMakeFiles/jpeg12-static.dir/jcdctmgr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jcdctmgr.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdctmgr.c > CMakeFiles/jpeg12-static.dir/jcdctmgr.c.i

CMakeFiles/jpeg12-static.dir/jcdctmgr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jcdctmgr.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdctmgr.c -o CMakeFiles/jpeg12-static.dir/jcdctmgr.c.s

CMakeFiles/jpeg12-static.dir/jdcoefct.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jdcoefct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcoefct.c
CMakeFiles/jpeg12-static.dir/jdcoefct.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object CMakeFiles/jpeg12-static.dir/jdcoefct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jdcoefct.c.o -MF CMakeFiles/jpeg12-static.dir/jdcoefct.c.o.d -o CMakeFiles/jpeg12-static.dir/jdcoefct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcoefct.c

CMakeFiles/jpeg12-static.dir/jdcoefct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jdcoefct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcoefct.c > CMakeFiles/jpeg12-static.dir/jdcoefct.c.i

CMakeFiles/jpeg12-static.dir/jdcoefct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jdcoefct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcoefct.c -o CMakeFiles/jpeg12-static.dir/jdcoefct.c.s

CMakeFiles/jpeg12-static.dir/jddctmgr.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jddctmgr.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddctmgr.c
CMakeFiles/jpeg12-static.dir/jddctmgr.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object CMakeFiles/jpeg12-static.dir/jddctmgr.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jddctmgr.c.o -MF CMakeFiles/jpeg12-static.dir/jddctmgr.c.o.d -o CMakeFiles/jpeg12-static.dir/jddctmgr.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddctmgr.c

CMakeFiles/jpeg12-static.dir/jddctmgr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jddctmgr.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddctmgr.c > CMakeFiles/jpeg12-static.dir/jddctmgr.c.i

CMakeFiles/jpeg12-static.dir/jddctmgr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jddctmgr.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddctmgr.c -o CMakeFiles/jpeg12-static.dir/jddctmgr.c.s

CMakeFiles/jpeg12-static.dir/jdmerge.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jdmerge.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmerge.c
CMakeFiles/jpeg12-static.dir/jdmerge.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object CMakeFiles/jpeg12-static.dir/jdmerge.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jdmerge.c.o -MF CMakeFiles/jpeg12-static.dir/jdmerge.c.o.d -o CMakeFiles/jpeg12-static.dir/jdmerge.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmerge.c

CMakeFiles/jpeg12-static.dir/jdmerge.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jdmerge.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmerge.c > CMakeFiles/jpeg12-static.dir/jdmerge.c.i

CMakeFiles/jpeg12-static.dir/jdmerge.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jdmerge.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmerge.c -o CMakeFiles/jpeg12-static.dir/jdmerge.c.s

CMakeFiles/jpeg12-static.dir/jfdctfst.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jfdctfst.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctfst.c
CMakeFiles/jpeg12-static.dir/jfdctfst.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object CMakeFiles/jpeg12-static.dir/jfdctfst.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jfdctfst.c.o -MF CMakeFiles/jpeg12-static.dir/jfdctfst.c.o.d -o CMakeFiles/jpeg12-static.dir/jfdctfst.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctfst.c

CMakeFiles/jpeg12-static.dir/jfdctfst.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jfdctfst.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctfst.c > CMakeFiles/jpeg12-static.dir/jfdctfst.c.i

CMakeFiles/jpeg12-static.dir/jfdctfst.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jfdctfst.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctfst.c -o CMakeFiles/jpeg12-static.dir/jfdctfst.c.s

CMakeFiles/jpeg12-static.dir/jfdctint.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jfdctint.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctint.c
CMakeFiles/jpeg12-static.dir/jfdctint.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object CMakeFiles/jpeg12-static.dir/jfdctint.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jfdctint.c.o -MF CMakeFiles/jpeg12-static.dir/jfdctint.c.o.d -o CMakeFiles/jpeg12-static.dir/jfdctint.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctint.c

CMakeFiles/jpeg12-static.dir/jfdctint.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jfdctint.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctint.c > CMakeFiles/jpeg12-static.dir/jfdctint.c.i

CMakeFiles/jpeg12-static.dir/jfdctint.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jfdctint.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctint.c -o CMakeFiles/jpeg12-static.dir/jfdctint.c.s

CMakeFiles/jpeg12-static.dir/jidctflt.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jidctflt.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctflt.c
CMakeFiles/jpeg12-static.dir/jidctflt.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object CMakeFiles/jpeg12-static.dir/jidctflt.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jidctflt.c.o -MF CMakeFiles/jpeg12-static.dir/jidctflt.c.o.d -o CMakeFiles/jpeg12-static.dir/jidctflt.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctflt.c

CMakeFiles/jpeg12-static.dir/jidctflt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jidctflt.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctflt.c > CMakeFiles/jpeg12-static.dir/jidctflt.c.i

CMakeFiles/jpeg12-static.dir/jidctflt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jidctflt.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctflt.c -o CMakeFiles/jpeg12-static.dir/jidctflt.c.s

CMakeFiles/jpeg12-static.dir/jidctfst.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jidctfst.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctfst.c
CMakeFiles/jpeg12-static.dir/jidctfst.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object CMakeFiles/jpeg12-static.dir/jidctfst.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jidctfst.c.o -MF CMakeFiles/jpeg12-static.dir/jidctfst.c.o.d -o CMakeFiles/jpeg12-static.dir/jidctfst.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctfst.c

CMakeFiles/jpeg12-static.dir/jidctfst.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jidctfst.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctfst.c > CMakeFiles/jpeg12-static.dir/jidctfst.c.i

CMakeFiles/jpeg12-static.dir/jidctfst.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jidctfst.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctfst.c -o CMakeFiles/jpeg12-static.dir/jidctfst.c.s

CMakeFiles/jpeg12-static.dir/jidctint.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jidctint.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctint.c
CMakeFiles/jpeg12-static.dir/jidctint.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object CMakeFiles/jpeg12-static.dir/jidctint.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jidctint.c.o -MF CMakeFiles/jpeg12-static.dir/jidctint.c.o.d -o CMakeFiles/jpeg12-static.dir/jidctint.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctint.c

CMakeFiles/jpeg12-static.dir/jidctint.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jidctint.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctint.c > CMakeFiles/jpeg12-static.dir/jidctint.c.i

CMakeFiles/jpeg12-static.dir/jidctint.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jidctint.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctint.c -o CMakeFiles/jpeg12-static.dir/jidctint.c.s

CMakeFiles/jpeg12-static.dir/jidctred.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jidctred.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctred.c
CMakeFiles/jpeg12-static.dir/jidctred.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object CMakeFiles/jpeg12-static.dir/jidctred.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jidctred.c.o -MF CMakeFiles/jpeg12-static.dir/jidctred.c.o.d -o CMakeFiles/jpeg12-static.dir/jidctred.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctred.c

CMakeFiles/jpeg12-static.dir/jidctred.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jidctred.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctred.c > CMakeFiles/jpeg12-static.dir/jidctred.c.i

CMakeFiles/jpeg12-static.dir/jidctred.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jidctred.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctred.c -o CMakeFiles/jpeg12-static.dir/jidctred.c.s

CMakeFiles/jpeg12-static.dir/jquant1.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jquant1.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant1.c
CMakeFiles/jpeg12-static.dir/jquant1.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object CMakeFiles/jpeg12-static.dir/jquant1.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jquant1.c.o -MF CMakeFiles/jpeg12-static.dir/jquant1.c.o.d -o CMakeFiles/jpeg12-static.dir/jquant1.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant1.c

CMakeFiles/jpeg12-static.dir/jquant1.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jquant1.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant1.c > CMakeFiles/jpeg12-static.dir/jquant1.c.i

CMakeFiles/jpeg12-static.dir/jquant1.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jquant1.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant1.c -o CMakeFiles/jpeg12-static.dir/jquant1.c.s

CMakeFiles/jpeg12-static.dir/jquant2.c.o: CMakeFiles/jpeg12-static.dir/flags.make
CMakeFiles/jpeg12-static.dir/jquant2.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant2.c
CMakeFiles/jpeg12-static.dir/jquant2.c.o: CMakeFiles/jpeg12-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object CMakeFiles/jpeg12-static.dir/jquant2.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg12-static.dir/jquant2.c.o -MF CMakeFiles/jpeg12-static.dir/jquant2.c.o.d -o CMakeFiles/jpeg12-static.dir/jquant2.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant2.c

CMakeFiles/jpeg12-static.dir/jquant2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg12-static.dir/jquant2.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant2.c > CMakeFiles/jpeg12-static.dir/jquant2.c.i

CMakeFiles/jpeg12-static.dir/jquant2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg12-static.dir/jquant2.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant2.c -o CMakeFiles/jpeg12-static.dir/jquant2.c.s

jpeg12-static: CMakeFiles/jpeg12-static.dir/jcapistd.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jccolor.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jcdiffct.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jclossls.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jcmainct.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jcprepct.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jcsample.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jdapistd.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jdcolor.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jddiffct.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jdlossls.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jdmainct.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jdpostct.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jdsample.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jutils.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jccoefct.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jcdctmgr.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jdcoefct.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jddctmgr.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jdmerge.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jfdctfst.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jfdctint.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jidctflt.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jidctfst.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jidctint.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jidctred.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jquant1.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/jquant2.c.o
jpeg12-static: CMakeFiles/jpeg12-static.dir/build.make
.PHONY : jpeg12-static

# Rule to build all files generated by this target.
CMakeFiles/jpeg12-static.dir/build: jpeg12-static
.PHONY : CMakeFiles/jpeg12-static.dir/build

CMakeFiles/jpeg12-static.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/jpeg12-static.dir/cmake_clean.cmake
.PHONY : CMakeFiles/jpeg12-static.dir/clean

CMakeFiles/jpeg12-static.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/jpeg12-static.dir/depend

