
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c" "CMakeFiles/jpeg12-static.dir/jcapistd.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jcapistd.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccoefct.c" "CMakeFiles/jpeg12-static.dir/jccoefct.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jccoefct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c" "CMakeFiles/jpeg12-static.dir/jccolor.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jccolor.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdctmgr.c" "CMakeFiles/jpeg12-static.dir/jcdctmgr.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jcdctmgr.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c" "CMakeFiles/jpeg12-static.dir/jcdiffct.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jcdiffct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c" "CMakeFiles/jpeg12-static.dir/jclossls.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jclossls.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c" "CMakeFiles/jpeg12-static.dir/jcmainct.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jcmainct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c" "CMakeFiles/jpeg12-static.dir/jcprepct.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jcprepct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c" "CMakeFiles/jpeg12-static.dir/jcsample.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jcsample.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c" "CMakeFiles/jpeg12-static.dir/jdapistd.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jdapistd.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcoefct.c" "CMakeFiles/jpeg12-static.dir/jdcoefct.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jdcoefct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c" "CMakeFiles/jpeg12-static.dir/jdcolor.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jdcolor.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddctmgr.c" "CMakeFiles/jpeg12-static.dir/jddctmgr.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jddctmgr.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c" "CMakeFiles/jpeg12-static.dir/jddiffct.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jddiffct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c" "CMakeFiles/jpeg12-static.dir/jdlossls.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jdlossls.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c" "CMakeFiles/jpeg12-static.dir/jdmainct.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jdmainct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmerge.c" "CMakeFiles/jpeg12-static.dir/jdmerge.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jdmerge.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c" "CMakeFiles/jpeg12-static.dir/jdpostct.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jdpostct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c" "CMakeFiles/jpeg12-static.dir/jdsample.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jdsample.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctfst.c" "CMakeFiles/jpeg12-static.dir/jfdctfst.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jfdctfst.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctint.c" "CMakeFiles/jpeg12-static.dir/jfdctint.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jfdctint.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctflt.c" "CMakeFiles/jpeg12-static.dir/jidctflt.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jidctflt.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctfst.c" "CMakeFiles/jpeg12-static.dir/jidctfst.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jidctfst.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctint.c" "CMakeFiles/jpeg12-static.dir/jidctint.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jidctint.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctred.c" "CMakeFiles/jpeg12-static.dir/jidctred.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jidctred.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant1.c" "CMakeFiles/jpeg12-static.dir/jquant1.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jquant1.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant2.c" "CMakeFiles/jpeg12-static.dir/jquant2.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jquant2.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c" "CMakeFiles/jpeg12-static.dir/jutils.c.o" "gcc" "CMakeFiles/jpeg12-static.dir/jutils.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
