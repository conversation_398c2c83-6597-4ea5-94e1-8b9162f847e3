# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build

# Include any dependencies generated for this target.
include CMakeFiles/turbojpeg16-static.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/turbojpeg16-static.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/turbojpeg16-static.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/turbojpeg16-static.dir/flags.make

CMakeFiles/turbojpeg16-static.dir/rdppm.c.o: CMakeFiles/turbojpeg16-static.dir/flags.make
CMakeFiles/turbojpeg16-static.dir/rdppm.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/rdppm.c
CMakeFiles/turbojpeg16-static.dir/rdppm.c.o: CMakeFiles/turbojpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/turbojpeg16-static.dir/rdppm.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/turbojpeg16-static.dir/rdppm.c.o -MF CMakeFiles/turbojpeg16-static.dir/rdppm.c.o.d -o CMakeFiles/turbojpeg16-static.dir/rdppm.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/rdppm.c

CMakeFiles/turbojpeg16-static.dir/rdppm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/turbojpeg16-static.dir/rdppm.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/rdppm.c > CMakeFiles/turbojpeg16-static.dir/rdppm.c.i

CMakeFiles/turbojpeg16-static.dir/rdppm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/turbojpeg16-static.dir/rdppm.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/rdppm.c -o CMakeFiles/turbojpeg16-static.dir/rdppm.c.s

CMakeFiles/turbojpeg16-static.dir/wrppm.c.o: CMakeFiles/turbojpeg16-static.dir/flags.make
CMakeFiles/turbojpeg16-static.dir/wrppm.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/wrppm.c
CMakeFiles/turbojpeg16-static.dir/wrppm.c.o: CMakeFiles/turbojpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/turbojpeg16-static.dir/wrppm.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/turbojpeg16-static.dir/wrppm.c.o -MF CMakeFiles/turbojpeg16-static.dir/wrppm.c.o.d -o CMakeFiles/turbojpeg16-static.dir/wrppm.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/wrppm.c

CMakeFiles/turbojpeg16-static.dir/wrppm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/turbojpeg16-static.dir/wrppm.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/wrppm.c > CMakeFiles/turbojpeg16-static.dir/wrppm.c.i

CMakeFiles/turbojpeg16-static.dir/wrppm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/turbojpeg16-static.dir/wrppm.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/wrppm.c -o CMakeFiles/turbojpeg16-static.dir/wrppm.c.s

turbojpeg16-static: CMakeFiles/turbojpeg16-static.dir/rdppm.c.o
turbojpeg16-static: CMakeFiles/turbojpeg16-static.dir/wrppm.c.o
turbojpeg16-static: CMakeFiles/turbojpeg16-static.dir/build.make
.PHONY : turbojpeg16-static

# Rule to build all files generated by this target.
CMakeFiles/turbojpeg16-static.dir/build: turbojpeg16-static
.PHONY : CMakeFiles/turbojpeg16-static.dir/build

CMakeFiles/turbojpeg16-static.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/turbojpeg16-static.dir/cmake_clean.cmake
.PHONY : CMakeFiles/turbojpeg16-static.dir/clean

CMakeFiles/turbojpeg16-static.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/turbojpeg16-static.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/turbojpeg16-static.dir/depend

