
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeDetermineSystem.cmake:211 (message)"
      - "CMakeLists.txt:12 (project)"
    message: |
      The system is: Darwin - 24.6.0 - arm64
  -
    kind: "message-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:12 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc 
      Build flags: -Werror=partial-availability;-Werror=unguarded-availability;-Werror=unguarded-availability-new
      Id flags:  
      
      The output was:
      1
      ld: library 'System' not found
      cc: error: linker command failed with exit code 1 (use -v to see invocation)
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:12 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc 
      Build flags: -Werror=partial-availability;-Werror=unguarded-availability;-Werror=unguarded-availability-new
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"
      
      The C compiler identification is AppleClang, found in:
        /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/3.27.9/CompilerIdC/CMakeCCompilerId.o
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:12 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-ig3WPR"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-ig3WPR"
    cmakeVariables:
      CMAKE_C_FLAGS: "   "
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-ig3WPR'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_887f2/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_887f2.dir/build.make CMakeFiles/cmTC_887f2.dir/build
        Building C object CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc   -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE   -v -Wl,-v -MD -MT CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o -c /Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeCCompilerABI.c
        Apple clang version 17.0.0 (clang-1700.0.13.5)
        Target: arm64-apple-darwin24.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
        cc: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx10.15.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.5 -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-assume-unique-vtables -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +zcm -target-feature +zcz -target-feature +v8.5a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-ig3WPR -target-linker-version 1167.5 -v -fcoverage-compilation-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-ig3WPR -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17 -dependency-file CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o -x c /Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeCCompilerABI.c
        clang -cc1 version 17.0.0 (clang-1700.0.13.5) default target arm64-apple-darwin24.6.0
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/local/include"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/SubFrameworks"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking C executable cmTC_887f2
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_887f2.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o -o cmTC_887f2 
        Apple clang version 17.0.0 (clang-1700.0.13.5)
        Target: arm64-apple-darwin24.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 11.0.0 15.5 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_887f2 -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1167.5
        BUILD 01:45:05 Apr 30 2025
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 17.0.0 (static support for 29, runtime is 29)
        TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
        Library search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift
        Framework search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:12 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        end of search list found
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        implicit include dirs: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:152 (message)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:12 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-ig3WPR']
        ignore line: []
        ignore line: [Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_887f2/fast]
        ignore line: [/Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_887f2.dir/build.make CMakeFiles/cmTC_887f2.dir/build]
        ignore line: [Building C object CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o]
        ignore line: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc   -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE   -v -Wl -v -MD -MT CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o -c /Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeCCompilerABI.c]
        ignore line: [Apple clang version 17.0.0 (clang-1700.0.13.5)]
        ignore line: [Target: arm64-apple-darwin24.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        ignore line: [cc: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx10.15.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.5 -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-assume-unique-vtables -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +zcm -target-feature +zcz -target-feature +v8.5a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-ig3WPR -target-linker-version 1167.5 -v -fcoverage-compilation-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-ig3WPR -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17 -dependency-file CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o -x c /Applications/CMake.app/Contents/share/cmake-3.27/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 17.0.0 (clang-1700.0.13.5) default target arm64-apple-darwin24.6.0]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/SubFrameworks"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking C executable cmTC_887f2]
        ignore line: [/Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_887f2.dir/link.txt --verbose=1]
        ignore line: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl -search_paths_first -Wl -headerpad_max_install_names  -v -Wl -v CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o -o cmTC_887f2 ]
        ignore line: [Apple clang version 17.0.0 (clang-1700.0.13.5)]
        ignore line: [Target: arm64-apple-darwin24.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        link line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 11.0.0 15.5 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_887f2 -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [11.0.0] ==> ignore
          arg [15.5] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_887f2] ==> ignore
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_887f2.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a] ==> lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
        Library search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift]
        Framework search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift]
        collapse framework dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift]
        implicit fwks: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks]
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckIncludeFile.cmake:90 (try_compile)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckTypeSize.cmake:250 (check_include_file)"
      - "CMakeLists.txt:433 (check_type_size)"
    checks:
      - "Looking for sys/types.h"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-Gs5la2"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-Gs5la2"
    cmakeVariables:
      CMAKE_C_FLAGS: "-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new"
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_SYS_TYPES_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-Gs5la2'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_c6325/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_c6325.dir/build.make CMakeFiles/cmTC_c6325.dir/build
        Building C object CMakeFiles/cmTC_c6325.dir/CheckIncludeFile.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc   -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE -MD -MT CMakeFiles/cmTC_c6325.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_c6325.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_c6325.dir/CheckIncludeFile.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-Gs5la2/CheckIncludeFile.c
        Linking C executable cmTC_c6325
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c6325.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_c6325.dir/CheckIncludeFile.c.o -o cmTC_c6325 
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckIncludeFile.cmake:90 (try_compile)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckTypeSize.cmake:251 (check_include_file)"
      - "CMakeLists.txt:433 (check_type_size)"
    checks:
      - "Looking for stdint.h"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-IhdwRV"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-IhdwRV"
    cmakeVariables:
      CMAKE_C_FLAGS: "-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new"
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_STDINT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-IhdwRV'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_aee44/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_aee44.dir/build.make CMakeFiles/cmTC_aee44.dir/build
        Building C object CMakeFiles/cmTC_aee44.dir/CheckIncludeFile.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc   -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE -MD -MT CMakeFiles/cmTC_aee44.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_aee44.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_aee44.dir/CheckIncludeFile.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-IhdwRV/CheckIncludeFile.c
        Linking C executable cmTC_aee44
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_aee44.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_aee44.dir/CheckIncludeFile.c.o -o cmTC_aee44 
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckIncludeFile.cmake:90 (try_compile)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckTypeSize.cmake:252 (check_include_file)"
      - "CMakeLists.txt:433 (check_type_size)"
    checks:
      - "Looking for stddef.h"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-xgMys3"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-xgMys3"
    cmakeVariables:
      CMAKE_C_FLAGS: "-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new"
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_STDDEF_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-xgMys3'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_0fb3c/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_0fb3c.dir/build.make CMakeFiles/cmTC_0fb3c.dir/build
        Building C object CMakeFiles/cmTC_0fb3c.dir/CheckIncludeFile.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc   -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE -MD -MT CMakeFiles/cmTC_0fb3c.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_0fb3c.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_0fb3c.dir/CheckIncludeFile.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-xgMys3/CheckIncludeFile.c
        Linking C executable cmTC_0fb3c
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_0fb3c.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_0fb3c.dir/CheckIncludeFile.c.o -o cmTC_0fb3c 
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckTypeSize.cmake:146 (try_compile)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckTypeSize.cmake:271 (__check_type_size_impl)"
      - "CMakeLists.txt:433 (check_type_size)"
    checks:
      - "Check size of size_t"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-oG9Btp"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-oG9Btp"
    cmakeVariables:
      CMAKE_C_FLAGS: "-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new"
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_SIZE_T"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-oG9Btp'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_4f599/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_4f599.dir/build.make CMakeFiles/cmTC_4f599.dir/build
        Building C object CMakeFiles/cmTC_4f599.dir/SIZE_T.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc   -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE -MD -MT CMakeFiles/cmTC_4f599.dir/SIZE_T.c.o -MF CMakeFiles/cmTC_4f599.dir/SIZE_T.c.o.d -o CMakeFiles/cmTC_4f599.dir/SIZE_T.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-oG9Btp/SIZE_T.c
        Linking C executable cmTC_4f599
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_4f599.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_4f599.dir/SIZE_T.c.o -o cmTC_4f599 
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckTypeSize.cmake:146 (try_compile)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckTypeSize.cmake:271 (__check_type_size_impl)"
      - "CMakeLists.txt:434 (check_type_size)"
    checks:
      - "Check size of unsigned long"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-IzJYpi"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-IzJYpi"
    cmakeVariables:
      CMAKE_C_FLAGS: "-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new"
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_UNSIGNED_LONG"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-IzJYpi'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_acc0c/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_acc0c.dir/build.make CMakeFiles/cmTC_acc0c.dir/build
        Building C object CMakeFiles/cmTC_acc0c.dir/UNSIGNED_LONG.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc   -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE -MD -MT CMakeFiles/cmTC_acc0c.dir/UNSIGNED_LONG.c.o -MF CMakeFiles/cmTC_acc0c.dir/UNSIGNED_LONG.c.o.d -o CMakeFiles/cmTC_acc0c.dir/UNSIGNED_LONG.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-IzJYpi/UNSIGNED_LONG.c
        Linking C executable cmTC_acc0c
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_acc0c.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_acc0c.dir/UNSIGNED_LONG.c.o -o cmTC_acc0c 
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "CMakeLists.txt:437 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_BUILTIN_CTZL"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-8Kr2Yz"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-8Kr2Yz"
    cmakeVariables:
      CMAKE_C_FLAGS: "-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new"
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_BUILTIN_CTZL"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-8Kr2Yz'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_83a97/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_83a97.dir/build.make CMakeFiles/cmTC_83a97.dir/build
        Building C object CMakeFiles/cmTC_83a97.dir/src.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -DHAVE_BUILTIN_CTZL  -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE -MD -MT CMakeFiles/cmTC_83a97.dir/src.c.o -MF CMakeFiles/cmTC_83a97.dir/src.c.o.d -o CMakeFiles/cmTC_83a97.dir/src.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-8Kr2Yz/src.c
        Linking C executable cmTC_83a97
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_83a97.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_83a97.dir/src.c.o -o cmTC_83a97 
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/Internal/CheckSourceRuns.cmake:93 (try_run)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "CMakeLists.txt:449 (check_c_source_runs)"
    checks:
      - "Performing Test RIGHT_SHIFT_IS_UNSIGNED"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-MtR93L"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-MtR93L"
    cmakeVariables:
      CMAKE_C_FLAGS: "-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new"
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "RIGHT_SHIFT_IS_UNSIGNED_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-MtR93L'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_93e18/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_93e18.dir/build.make CMakeFiles/cmTC_93e18.dir/build
        Building C object CMakeFiles/cmTC_93e18.dir/src.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -DRIGHT_SHIFT_IS_UNSIGNED  -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE -MD -MT CMakeFiles/cmTC_93e18.dir/src.c.o -MF CMakeFiles/cmTC_93e18.dir/src.c.o.d -o CMakeFiles/cmTC_93e18.dir/src.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-MtR93L/src.c
        /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-MtR93L/src.c:10:22: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
           10 |         res |= (~0L) << (32-4);
              |                ~~~~~ ^
        1 warning generated.
        Linking C executable cmTC_93e18
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_93e18.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_93e18.dir/src.c.o -o cmTC_93e18 
        
      exitCode: 0
    runResult:
      variable: "RIGHT_SHIFT_IS_UNSIGNED_EXITCODE"
      cached: true
      stdout: |
      stderr: |
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "CMakeLists.txt:487 (check_c_source_compiles)"
    checks:
      - "Performing Test INLINE_WORKS"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-khxAwD"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-khxAwD"
    cmakeVariables:
      CMAKE_C_FLAGS: "-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new"
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "INLINE_WORKS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-khxAwD'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_62696/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_62696.dir/build.make CMakeFiles/cmTC_62696.dir/build
        Building C object CMakeFiles/cmTC_62696.dir/src.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -DINLINE_WORKS  -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE -MD -MT CMakeFiles/cmTC_62696.dir/src.c.o -MF CMakeFiles/cmTC_62696.dir/src.c.o.d -o CMakeFiles/cmTC_62696.dir/src.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-khxAwD/src.c
        Linking C executable cmTC_62696
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_62696.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_62696.dir/src.c.o -o cmTC_62696 
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "CMakeLists.txt:504 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_THREAD_LOCAL"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-SqdpwC"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-SqdpwC"
    cmakeVariables:
      CMAKE_C_FLAGS: "-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new"
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_THREAD_LOCAL"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-SqdpwC'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_81142/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_81142.dir/build.make CMakeFiles/cmTC_81142.dir/build
        Building C object CMakeFiles/cmTC_81142.dir/src.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -DHAVE_THREAD_LOCAL  -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE -MD -MT CMakeFiles/cmTC_81142.dir/src.c.o -MF CMakeFiles/cmTC_81142.dir/src.c.o.d -o CMakeFiles/cmTC_81142.dir/src.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-SqdpwC/src.c
        Linking C executable cmTC_81142
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_81142.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_81142.dir/src.c.o -o cmTC_81142 
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "simd/CMakeLists.txt:258 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_VLD1_S16_X3"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-9dEu69"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-9dEu69"
    cmakeVariables:
      CMAKE_C_FLAGS: "-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new"
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_VLD1_S16_X3"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-9dEu69'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_62d80/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_62d80.dir/build.make CMakeFiles/cmTC_62d80.dir/build
        Building C object CMakeFiles/cmTC_62d80.dir/src.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -DHAVE_VLD1_S16_X3  -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE -MD -MT CMakeFiles/cmTC_62d80.dir/src.c.o -MF CMakeFiles/cmTC_62d80.dir/src.c.o.d -o CMakeFiles/cmTC_62d80.dir/src.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-9dEu69/src.c
        Linking C executable cmTC_62d80
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_62d80.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_62d80.dir/src.c.o -o cmTC_62d80 
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "simd/CMakeLists.txt:270 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_VLD1_U16_X2"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-SC2PEF"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-SC2PEF"
    cmakeVariables:
      CMAKE_C_FLAGS: "-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new"
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_VLD1_U16_X2"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-SC2PEF'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_b6e58/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_b6e58.dir/build.make CMakeFiles/cmTC_b6e58.dir/build
        Building C object CMakeFiles/cmTC_b6e58.dir/src.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -DHAVE_VLD1_U16_X2  -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE -MD -MT CMakeFiles/cmTC_b6e58.dir/src.c.o -MF CMakeFiles/cmTC_b6e58.dir/src.c.o.d -o CMakeFiles/cmTC_b6e58.dir/src.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-SC2PEF/src.c
        Linking C executable cmTC_b6e58
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b6e58.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_b6e58.dir/src.c.o -o cmTC_b6e58 
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/Applications/CMake.app/Contents/share/cmake-3.27/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "simd/CMakeLists.txt:281 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_VLD1Q_U8_X4"
    directories:
      source: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-NMbwbC"
      binary: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-NMbwbC"
    cmakeVariables:
      CMAKE_C_FLAGS: "-Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new"
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/../cmake/modules"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_VLD1Q_U8_X4"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-NMbwbC'
        
        Run Build Command(s): /Applications/CMake.app/Contents/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_94d08/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_94d08.dir/build.make CMakeFiles/cmTC_94d08.dir/build
        Building C object CMakeFiles/cmTC_94d08.dir/src.c.o
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -DHAVE_VLD1Q_U8_X4  -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIE -MD -MT CMakeFiles/cmTC_94d08.dir/src.c.o -MF CMakeFiles/cmTC_94d08.dir/src.c.o.d -o CMakeFiles/cmTC_94d08.dir/src.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/CMakeScratch/TryCompile-NMbwbC/src.c
        Linking C executable cmTC_94d08
        /Applications/CMake.app/Contents/bin/cmake -E cmake_link_script CMakeFiles/cmTC_94d08.dir/link.txt --verbose=1
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/cmTC_94d08.dir/src.c.o -o cmTC_94d08 
        
      exitCode: 0
...
