# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# compile C with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc
C_DEFINES = -DNEON_INTRINSICS

C_INCLUDES = -I/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build -I/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG

C_FLAGSarm64 = -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIC -DBITS_IN_JSAMPLE=12 -DPPM_SUPPORTED

C_FLAGS = -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=10.15 -fPIC -DBITS_IN_JSAMPLE=12 -DPPM_SUPPORTED

