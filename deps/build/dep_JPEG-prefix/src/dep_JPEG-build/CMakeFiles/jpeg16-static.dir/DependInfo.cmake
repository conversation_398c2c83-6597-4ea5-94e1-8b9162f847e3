
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c" "CMakeFiles/jpeg16-static.dir/jcapistd.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jcapistd.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c" "CMakeFiles/jpeg16-static.dir/jccolor.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jccolor.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c" "CMakeFiles/jpeg16-static.dir/jcdiffct.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jcdiffct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c" "CMakeFiles/jpeg16-static.dir/jclossls.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jclossls.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c" "CMakeFiles/jpeg16-static.dir/jcmainct.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jcmainct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c" "CMakeFiles/jpeg16-static.dir/jcprepct.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jcprepct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c" "CMakeFiles/jpeg16-static.dir/jcsample.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jcsample.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c" "CMakeFiles/jpeg16-static.dir/jdapistd.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jdapistd.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c" "CMakeFiles/jpeg16-static.dir/jdcolor.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jdcolor.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c" "CMakeFiles/jpeg16-static.dir/jddiffct.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jddiffct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c" "CMakeFiles/jpeg16-static.dir/jdlossls.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jdlossls.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c" "CMakeFiles/jpeg16-static.dir/jdmainct.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jdmainct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c" "CMakeFiles/jpeg16-static.dir/jdpostct.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jdpostct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c" "CMakeFiles/jpeg16-static.dir/jdsample.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jdsample.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c" "CMakeFiles/jpeg16-static.dir/jutils.c.o" "gcc" "CMakeFiles/jpeg16-static.dir/jutils.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
