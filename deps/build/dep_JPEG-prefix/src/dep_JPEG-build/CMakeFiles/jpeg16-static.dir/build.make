# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build

# Include any dependencies generated for this target.
include CMakeFiles/jpeg16-static.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/jpeg16-static.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/jpeg16-static.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/jpeg16-static.dir/flags.make

CMakeFiles/jpeg16-static.dir/jcapistd.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jcapistd.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c
CMakeFiles/jpeg16-static.dir/jcapistd.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/jpeg16-static.dir/jcapistd.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jcapistd.c.o -MF CMakeFiles/jpeg16-static.dir/jcapistd.c.o.d -o CMakeFiles/jpeg16-static.dir/jcapistd.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c

CMakeFiles/jpeg16-static.dir/jcapistd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jcapistd.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c > CMakeFiles/jpeg16-static.dir/jcapistd.c.i

CMakeFiles/jpeg16-static.dir/jcapistd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jcapistd.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c -o CMakeFiles/jpeg16-static.dir/jcapistd.c.s

CMakeFiles/jpeg16-static.dir/jccolor.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jccolor.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c
CMakeFiles/jpeg16-static.dir/jccolor.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/jpeg16-static.dir/jccolor.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jccolor.c.o -MF CMakeFiles/jpeg16-static.dir/jccolor.c.o.d -o CMakeFiles/jpeg16-static.dir/jccolor.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c

CMakeFiles/jpeg16-static.dir/jccolor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jccolor.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c > CMakeFiles/jpeg16-static.dir/jccolor.c.i

CMakeFiles/jpeg16-static.dir/jccolor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jccolor.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c -o CMakeFiles/jpeg16-static.dir/jccolor.c.s

CMakeFiles/jpeg16-static.dir/jcdiffct.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jcdiffct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c
CMakeFiles/jpeg16-static.dir/jcdiffct.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/jpeg16-static.dir/jcdiffct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jcdiffct.c.o -MF CMakeFiles/jpeg16-static.dir/jcdiffct.c.o.d -o CMakeFiles/jpeg16-static.dir/jcdiffct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c

CMakeFiles/jpeg16-static.dir/jcdiffct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jcdiffct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c > CMakeFiles/jpeg16-static.dir/jcdiffct.c.i

CMakeFiles/jpeg16-static.dir/jcdiffct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jcdiffct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c -o CMakeFiles/jpeg16-static.dir/jcdiffct.c.s

CMakeFiles/jpeg16-static.dir/jclossls.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jclossls.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c
CMakeFiles/jpeg16-static.dir/jclossls.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/jpeg16-static.dir/jclossls.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jclossls.c.o -MF CMakeFiles/jpeg16-static.dir/jclossls.c.o.d -o CMakeFiles/jpeg16-static.dir/jclossls.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c

CMakeFiles/jpeg16-static.dir/jclossls.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jclossls.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c > CMakeFiles/jpeg16-static.dir/jclossls.c.i

CMakeFiles/jpeg16-static.dir/jclossls.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jclossls.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c -o CMakeFiles/jpeg16-static.dir/jclossls.c.s

CMakeFiles/jpeg16-static.dir/jcmainct.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jcmainct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c
CMakeFiles/jpeg16-static.dir/jcmainct.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/jpeg16-static.dir/jcmainct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jcmainct.c.o -MF CMakeFiles/jpeg16-static.dir/jcmainct.c.o.d -o CMakeFiles/jpeg16-static.dir/jcmainct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c

CMakeFiles/jpeg16-static.dir/jcmainct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jcmainct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c > CMakeFiles/jpeg16-static.dir/jcmainct.c.i

CMakeFiles/jpeg16-static.dir/jcmainct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jcmainct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c -o CMakeFiles/jpeg16-static.dir/jcmainct.c.s

CMakeFiles/jpeg16-static.dir/jcprepct.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jcprepct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c
CMakeFiles/jpeg16-static.dir/jcprepct.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/jpeg16-static.dir/jcprepct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jcprepct.c.o -MF CMakeFiles/jpeg16-static.dir/jcprepct.c.o.d -o CMakeFiles/jpeg16-static.dir/jcprepct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c

CMakeFiles/jpeg16-static.dir/jcprepct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jcprepct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c > CMakeFiles/jpeg16-static.dir/jcprepct.c.i

CMakeFiles/jpeg16-static.dir/jcprepct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jcprepct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c -o CMakeFiles/jpeg16-static.dir/jcprepct.c.s

CMakeFiles/jpeg16-static.dir/jcsample.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jcsample.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c
CMakeFiles/jpeg16-static.dir/jcsample.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/jpeg16-static.dir/jcsample.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jcsample.c.o -MF CMakeFiles/jpeg16-static.dir/jcsample.c.o.d -o CMakeFiles/jpeg16-static.dir/jcsample.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c

CMakeFiles/jpeg16-static.dir/jcsample.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jcsample.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c > CMakeFiles/jpeg16-static.dir/jcsample.c.i

CMakeFiles/jpeg16-static.dir/jcsample.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jcsample.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c -o CMakeFiles/jpeg16-static.dir/jcsample.c.s

CMakeFiles/jpeg16-static.dir/jdapistd.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jdapistd.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c
CMakeFiles/jpeg16-static.dir/jdapistd.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/jpeg16-static.dir/jdapistd.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jdapistd.c.o -MF CMakeFiles/jpeg16-static.dir/jdapistd.c.o.d -o CMakeFiles/jpeg16-static.dir/jdapistd.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c

CMakeFiles/jpeg16-static.dir/jdapistd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jdapistd.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c > CMakeFiles/jpeg16-static.dir/jdapistd.c.i

CMakeFiles/jpeg16-static.dir/jdapistd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jdapistd.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c -o CMakeFiles/jpeg16-static.dir/jdapistd.c.s

CMakeFiles/jpeg16-static.dir/jdcolor.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jdcolor.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c
CMakeFiles/jpeg16-static.dir/jdcolor.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/jpeg16-static.dir/jdcolor.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jdcolor.c.o -MF CMakeFiles/jpeg16-static.dir/jdcolor.c.o.d -o CMakeFiles/jpeg16-static.dir/jdcolor.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c

CMakeFiles/jpeg16-static.dir/jdcolor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jdcolor.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c > CMakeFiles/jpeg16-static.dir/jdcolor.c.i

CMakeFiles/jpeg16-static.dir/jdcolor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jdcolor.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c -o CMakeFiles/jpeg16-static.dir/jdcolor.c.s

CMakeFiles/jpeg16-static.dir/jddiffct.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jddiffct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c
CMakeFiles/jpeg16-static.dir/jddiffct.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/jpeg16-static.dir/jddiffct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jddiffct.c.o -MF CMakeFiles/jpeg16-static.dir/jddiffct.c.o.d -o CMakeFiles/jpeg16-static.dir/jddiffct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c

CMakeFiles/jpeg16-static.dir/jddiffct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jddiffct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c > CMakeFiles/jpeg16-static.dir/jddiffct.c.i

CMakeFiles/jpeg16-static.dir/jddiffct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jddiffct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c -o CMakeFiles/jpeg16-static.dir/jddiffct.c.s

CMakeFiles/jpeg16-static.dir/jdlossls.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jdlossls.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c
CMakeFiles/jpeg16-static.dir/jdlossls.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/jpeg16-static.dir/jdlossls.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jdlossls.c.o -MF CMakeFiles/jpeg16-static.dir/jdlossls.c.o.d -o CMakeFiles/jpeg16-static.dir/jdlossls.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c

CMakeFiles/jpeg16-static.dir/jdlossls.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jdlossls.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c > CMakeFiles/jpeg16-static.dir/jdlossls.c.i

CMakeFiles/jpeg16-static.dir/jdlossls.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jdlossls.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c -o CMakeFiles/jpeg16-static.dir/jdlossls.c.s

CMakeFiles/jpeg16-static.dir/jdmainct.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jdmainct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c
CMakeFiles/jpeg16-static.dir/jdmainct.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/jpeg16-static.dir/jdmainct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jdmainct.c.o -MF CMakeFiles/jpeg16-static.dir/jdmainct.c.o.d -o CMakeFiles/jpeg16-static.dir/jdmainct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c

CMakeFiles/jpeg16-static.dir/jdmainct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jdmainct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c > CMakeFiles/jpeg16-static.dir/jdmainct.c.i

CMakeFiles/jpeg16-static.dir/jdmainct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jdmainct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c -o CMakeFiles/jpeg16-static.dir/jdmainct.c.s

CMakeFiles/jpeg16-static.dir/jdpostct.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jdpostct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c
CMakeFiles/jpeg16-static.dir/jdpostct.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/jpeg16-static.dir/jdpostct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jdpostct.c.o -MF CMakeFiles/jpeg16-static.dir/jdpostct.c.o.d -o CMakeFiles/jpeg16-static.dir/jdpostct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c

CMakeFiles/jpeg16-static.dir/jdpostct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jdpostct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c > CMakeFiles/jpeg16-static.dir/jdpostct.c.i

CMakeFiles/jpeg16-static.dir/jdpostct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jdpostct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c -o CMakeFiles/jpeg16-static.dir/jdpostct.c.s

CMakeFiles/jpeg16-static.dir/jdsample.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jdsample.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c
CMakeFiles/jpeg16-static.dir/jdsample.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/jpeg16-static.dir/jdsample.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jdsample.c.o -MF CMakeFiles/jpeg16-static.dir/jdsample.c.o.d -o CMakeFiles/jpeg16-static.dir/jdsample.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c

CMakeFiles/jpeg16-static.dir/jdsample.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jdsample.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c > CMakeFiles/jpeg16-static.dir/jdsample.c.i

CMakeFiles/jpeg16-static.dir/jdsample.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jdsample.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c -o CMakeFiles/jpeg16-static.dir/jdsample.c.s

CMakeFiles/jpeg16-static.dir/jutils.c.o: CMakeFiles/jpeg16-static.dir/flags.make
CMakeFiles/jpeg16-static.dir/jutils.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c
CMakeFiles/jpeg16-static.dir/jutils.c.o: CMakeFiles/jpeg16-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/jpeg16-static.dir/jutils.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg16-static.dir/jutils.c.o -MF CMakeFiles/jpeg16-static.dir/jutils.c.o.d -o CMakeFiles/jpeg16-static.dir/jutils.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c

CMakeFiles/jpeg16-static.dir/jutils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg16-static.dir/jutils.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c > CMakeFiles/jpeg16-static.dir/jutils.c.i

CMakeFiles/jpeg16-static.dir/jutils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg16-static.dir/jutils.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c -o CMakeFiles/jpeg16-static.dir/jutils.c.s

jpeg16-static: CMakeFiles/jpeg16-static.dir/jcapistd.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jccolor.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jcdiffct.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jclossls.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jcmainct.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jcprepct.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jcsample.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jdapistd.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jdcolor.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jddiffct.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jdlossls.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jdmainct.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jdpostct.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jdsample.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/jutils.c.o
jpeg16-static: CMakeFiles/jpeg16-static.dir/build.make
.PHONY : jpeg16-static

# Rule to build all files generated by this target.
CMakeFiles/jpeg16-static.dir/build: jpeg16-static
.PHONY : CMakeFiles/jpeg16-static.dir/build

CMakeFiles/jpeg16-static.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/jpeg16-static.dir/cmake_clean.cmake
.PHONY : CMakeFiles/jpeg16-static.dir/clean

CMakeFiles/jpeg16-static.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/jpeg16-static.dir/depend

