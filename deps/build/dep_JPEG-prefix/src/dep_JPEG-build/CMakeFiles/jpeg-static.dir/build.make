# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build

# Include any dependencies generated for this target.
include CMakeFiles/jpeg-static.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/jpeg-static.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/jpeg-static.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/jpeg-static.dir/flags.make

CMakeFiles/jpeg-static.dir/jcapistd.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcapistd.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c
CMakeFiles/jpeg-static.dir/jcapistd.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/jpeg-static.dir/jcapistd.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcapistd.c.o -MF CMakeFiles/jpeg-static.dir/jcapistd.c.o.d -o CMakeFiles/jpeg-static.dir/jcapistd.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c

CMakeFiles/jpeg-static.dir/jcapistd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcapistd.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c > CMakeFiles/jpeg-static.dir/jcapistd.c.i

CMakeFiles/jpeg-static.dir/jcapistd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcapistd.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c -o CMakeFiles/jpeg-static.dir/jcapistd.c.s

CMakeFiles/jpeg-static.dir/jccolor.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jccolor.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c
CMakeFiles/jpeg-static.dir/jccolor.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/jpeg-static.dir/jccolor.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jccolor.c.o -MF CMakeFiles/jpeg-static.dir/jccolor.c.o.d -o CMakeFiles/jpeg-static.dir/jccolor.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c

CMakeFiles/jpeg-static.dir/jccolor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jccolor.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c > CMakeFiles/jpeg-static.dir/jccolor.c.i

CMakeFiles/jpeg-static.dir/jccolor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jccolor.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c -o CMakeFiles/jpeg-static.dir/jccolor.c.s

CMakeFiles/jpeg-static.dir/jcdiffct.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcdiffct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c
CMakeFiles/jpeg-static.dir/jcdiffct.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/jpeg-static.dir/jcdiffct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcdiffct.c.o -MF CMakeFiles/jpeg-static.dir/jcdiffct.c.o.d -o CMakeFiles/jpeg-static.dir/jcdiffct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c

CMakeFiles/jpeg-static.dir/jcdiffct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcdiffct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c > CMakeFiles/jpeg-static.dir/jcdiffct.c.i

CMakeFiles/jpeg-static.dir/jcdiffct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcdiffct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c -o CMakeFiles/jpeg-static.dir/jcdiffct.c.s

CMakeFiles/jpeg-static.dir/jclossls.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jclossls.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c
CMakeFiles/jpeg-static.dir/jclossls.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/jpeg-static.dir/jclossls.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jclossls.c.o -MF CMakeFiles/jpeg-static.dir/jclossls.c.o.d -o CMakeFiles/jpeg-static.dir/jclossls.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c

CMakeFiles/jpeg-static.dir/jclossls.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jclossls.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c > CMakeFiles/jpeg-static.dir/jclossls.c.i

CMakeFiles/jpeg-static.dir/jclossls.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jclossls.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c -o CMakeFiles/jpeg-static.dir/jclossls.c.s

CMakeFiles/jpeg-static.dir/jcmainct.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcmainct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c
CMakeFiles/jpeg-static.dir/jcmainct.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/jpeg-static.dir/jcmainct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcmainct.c.o -MF CMakeFiles/jpeg-static.dir/jcmainct.c.o.d -o CMakeFiles/jpeg-static.dir/jcmainct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c

CMakeFiles/jpeg-static.dir/jcmainct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcmainct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c > CMakeFiles/jpeg-static.dir/jcmainct.c.i

CMakeFiles/jpeg-static.dir/jcmainct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcmainct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c -o CMakeFiles/jpeg-static.dir/jcmainct.c.s

CMakeFiles/jpeg-static.dir/jcprepct.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcprepct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c
CMakeFiles/jpeg-static.dir/jcprepct.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/jpeg-static.dir/jcprepct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcprepct.c.o -MF CMakeFiles/jpeg-static.dir/jcprepct.c.o.d -o CMakeFiles/jpeg-static.dir/jcprepct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c

CMakeFiles/jpeg-static.dir/jcprepct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcprepct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c > CMakeFiles/jpeg-static.dir/jcprepct.c.i

CMakeFiles/jpeg-static.dir/jcprepct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcprepct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c -o CMakeFiles/jpeg-static.dir/jcprepct.c.s

CMakeFiles/jpeg-static.dir/jcsample.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcsample.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c
CMakeFiles/jpeg-static.dir/jcsample.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/jpeg-static.dir/jcsample.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcsample.c.o -MF CMakeFiles/jpeg-static.dir/jcsample.c.o.d -o CMakeFiles/jpeg-static.dir/jcsample.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c

CMakeFiles/jpeg-static.dir/jcsample.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcsample.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c > CMakeFiles/jpeg-static.dir/jcsample.c.i

CMakeFiles/jpeg-static.dir/jcsample.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcsample.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c -o CMakeFiles/jpeg-static.dir/jcsample.c.s

CMakeFiles/jpeg-static.dir/jdapistd.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdapistd.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c
CMakeFiles/jpeg-static.dir/jdapistd.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/jpeg-static.dir/jdapistd.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdapistd.c.o -MF CMakeFiles/jpeg-static.dir/jdapistd.c.o.d -o CMakeFiles/jpeg-static.dir/jdapistd.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c

CMakeFiles/jpeg-static.dir/jdapistd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdapistd.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c > CMakeFiles/jpeg-static.dir/jdapistd.c.i

CMakeFiles/jpeg-static.dir/jdapistd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdapistd.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c -o CMakeFiles/jpeg-static.dir/jdapistd.c.s

CMakeFiles/jpeg-static.dir/jdcolor.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdcolor.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c
CMakeFiles/jpeg-static.dir/jdcolor.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/jpeg-static.dir/jdcolor.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdcolor.c.o -MF CMakeFiles/jpeg-static.dir/jdcolor.c.o.d -o CMakeFiles/jpeg-static.dir/jdcolor.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c

CMakeFiles/jpeg-static.dir/jdcolor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdcolor.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c > CMakeFiles/jpeg-static.dir/jdcolor.c.i

CMakeFiles/jpeg-static.dir/jdcolor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdcolor.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c -o CMakeFiles/jpeg-static.dir/jdcolor.c.s

CMakeFiles/jpeg-static.dir/jddiffct.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jddiffct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c
CMakeFiles/jpeg-static.dir/jddiffct.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/jpeg-static.dir/jddiffct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jddiffct.c.o -MF CMakeFiles/jpeg-static.dir/jddiffct.c.o.d -o CMakeFiles/jpeg-static.dir/jddiffct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c

CMakeFiles/jpeg-static.dir/jddiffct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jddiffct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c > CMakeFiles/jpeg-static.dir/jddiffct.c.i

CMakeFiles/jpeg-static.dir/jddiffct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jddiffct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c -o CMakeFiles/jpeg-static.dir/jddiffct.c.s

CMakeFiles/jpeg-static.dir/jdlossls.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdlossls.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c
CMakeFiles/jpeg-static.dir/jdlossls.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/jpeg-static.dir/jdlossls.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdlossls.c.o -MF CMakeFiles/jpeg-static.dir/jdlossls.c.o.d -o CMakeFiles/jpeg-static.dir/jdlossls.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c

CMakeFiles/jpeg-static.dir/jdlossls.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdlossls.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c > CMakeFiles/jpeg-static.dir/jdlossls.c.i

CMakeFiles/jpeg-static.dir/jdlossls.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdlossls.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c -o CMakeFiles/jpeg-static.dir/jdlossls.c.s

CMakeFiles/jpeg-static.dir/jdmainct.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdmainct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c
CMakeFiles/jpeg-static.dir/jdmainct.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/jpeg-static.dir/jdmainct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdmainct.c.o -MF CMakeFiles/jpeg-static.dir/jdmainct.c.o.d -o CMakeFiles/jpeg-static.dir/jdmainct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c

CMakeFiles/jpeg-static.dir/jdmainct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdmainct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c > CMakeFiles/jpeg-static.dir/jdmainct.c.i

CMakeFiles/jpeg-static.dir/jdmainct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdmainct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c -o CMakeFiles/jpeg-static.dir/jdmainct.c.s

CMakeFiles/jpeg-static.dir/jdpostct.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdpostct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c
CMakeFiles/jpeg-static.dir/jdpostct.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/jpeg-static.dir/jdpostct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdpostct.c.o -MF CMakeFiles/jpeg-static.dir/jdpostct.c.o.d -o CMakeFiles/jpeg-static.dir/jdpostct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c

CMakeFiles/jpeg-static.dir/jdpostct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdpostct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c > CMakeFiles/jpeg-static.dir/jdpostct.c.i

CMakeFiles/jpeg-static.dir/jdpostct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdpostct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c -o CMakeFiles/jpeg-static.dir/jdpostct.c.s

CMakeFiles/jpeg-static.dir/jdsample.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdsample.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c
CMakeFiles/jpeg-static.dir/jdsample.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/jpeg-static.dir/jdsample.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdsample.c.o -MF CMakeFiles/jpeg-static.dir/jdsample.c.o.d -o CMakeFiles/jpeg-static.dir/jdsample.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c

CMakeFiles/jpeg-static.dir/jdsample.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdsample.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c > CMakeFiles/jpeg-static.dir/jdsample.c.i

CMakeFiles/jpeg-static.dir/jdsample.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdsample.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c -o CMakeFiles/jpeg-static.dir/jdsample.c.s

CMakeFiles/jpeg-static.dir/jutils.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jutils.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c
CMakeFiles/jpeg-static.dir/jutils.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/jpeg-static.dir/jutils.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jutils.c.o -MF CMakeFiles/jpeg-static.dir/jutils.c.o.d -o CMakeFiles/jpeg-static.dir/jutils.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c

CMakeFiles/jpeg-static.dir/jutils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jutils.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c > CMakeFiles/jpeg-static.dir/jutils.c.i

CMakeFiles/jpeg-static.dir/jutils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jutils.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c -o CMakeFiles/jpeg-static.dir/jutils.c.s

CMakeFiles/jpeg-static.dir/jccoefct.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jccoefct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccoefct.c
CMakeFiles/jpeg-static.dir/jccoefct.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/jpeg-static.dir/jccoefct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jccoefct.c.o -MF CMakeFiles/jpeg-static.dir/jccoefct.c.o.d -o CMakeFiles/jpeg-static.dir/jccoefct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccoefct.c

CMakeFiles/jpeg-static.dir/jccoefct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jccoefct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccoefct.c > CMakeFiles/jpeg-static.dir/jccoefct.c.i

CMakeFiles/jpeg-static.dir/jccoefct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jccoefct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccoefct.c -o CMakeFiles/jpeg-static.dir/jccoefct.c.s

CMakeFiles/jpeg-static.dir/jcdctmgr.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcdctmgr.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdctmgr.c
CMakeFiles/jpeg-static.dir/jcdctmgr.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object CMakeFiles/jpeg-static.dir/jcdctmgr.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcdctmgr.c.o -MF CMakeFiles/jpeg-static.dir/jcdctmgr.c.o.d -o CMakeFiles/jpeg-static.dir/jcdctmgr.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdctmgr.c

CMakeFiles/jpeg-static.dir/jcdctmgr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcdctmgr.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdctmgr.c > CMakeFiles/jpeg-static.dir/jcdctmgr.c.i

CMakeFiles/jpeg-static.dir/jcdctmgr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcdctmgr.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdctmgr.c -o CMakeFiles/jpeg-static.dir/jcdctmgr.c.s

CMakeFiles/jpeg-static.dir/jdcoefct.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdcoefct.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcoefct.c
CMakeFiles/jpeg-static.dir/jdcoefct.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object CMakeFiles/jpeg-static.dir/jdcoefct.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdcoefct.c.o -MF CMakeFiles/jpeg-static.dir/jdcoefct.c.o.d -o CMakeFiles/jpeg-static.dir/jdcoefct.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcoefct.c

CMakeFiles/jpeg-static.dir/jdcoefct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdcoefct.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcoefct.c > CMakeFiles/jpeg-static.dir/jdcoefct.c.i

CMakeFiles/jpeg-static.dir/jdcoefct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdcoefct.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcoefct.c -o CMakeFiles/jpeg-static.dir/jdcoefct.c.s

CMakeFiles/jpeg-static.dir/jddctmgr.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jddctmgr.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddctmgr.c
CMakeFiles/jpeg-static.dir/jddctmgr.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object CMakeFiles/jpeg-static.dir/jddctmgr.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jddctmgr.c.o -MF CMakeFiles/jpeg-static.dir/jddctmgr.c.o.d -o CMakeFiles/jpeg-static.dir/jddctmgr.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddctmgr.c

CMakeFiles/jpeg-static.dir/jddctmgr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jddctmgr.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddctmgr.c > CMakeFiles/jpeg-static.dir/jddctmgr.c.i

CMakeFiles/jpeg-static.dir/jddctmgr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jddctmgr.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddctmgr.c -o CMakeFiles/jpeg-static.dir/jddctmgr.c.s

CMakeFiles/jpeg-static.dir/jdmerge.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdmerge.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmerge.c
CMakeFiles/jpeg-static.dir/jdmerge.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object CMakeFiles/jpeg-static.dir/jdmerge.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdmerge.c.o -MF CMakeFiles/jpeg-static.dir/jdmerge.c.o.d -o CMakeFiles/jpeg-static.dir/jdmerge.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmerge.c

CMakeFiles/jpeg-static.dir/jdmerge.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdmerge.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmerge.c > CMakeFiles/jpeg-static.dir/jdmerge.c.i

CMakeFiles/jpeg-static.dir/jdmerge.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdmerge.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmerge.c -o CMakeFiles/jpeg-static.dir/jdmerge.c.s

CMakeFiles/jpeg-static.dir/jfdctfst.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jfdctfst.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctfst.c
CMakeFiles/jpeg-static.dir/jfdctfst.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object CMakeFiles/jpeg-static.dir/jfdctfst.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jfdctfst.c.o -MF CMakeFiles/jpeg-static.dir/jfdctfst.c.o.d -o CMakeFiles/jpeg-static.dir/jfdctfst.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctfst.c

CMakeFiles/jpeg-static.dir/jfdctfst.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jfdctfst.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctfst.c > CMakeFiles/jpeg-static.dir/jfdctfst.c.i

CMakeFiles/jpeg-static.dir/jfdctfst.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jfdctfst.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctfst.c -o CMakeFiles/jpeg-static.dir/jfdctfst.c.s

CMakeFiles/jpeg-static.dir/jfdctint.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jfdctint.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctint.c
CMakeFiles/jpeg-static.dir/jfdctint.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object CMakeFiles/jpeg-static.dir/jfdctint.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jfdctint.c.o -MF CMakeFiles/jpeg-static.dir/jfdctint.c.o.d -o CMakeFiles/jpeg-static.dir/jfdctint.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctint.c

CMakeFiles/jpeg-static.dir/jfdctint.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jfdctint.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctint.c > CMakeFiles/jpeg-static.dir/jfdctint.c.i

CMakeFiles/jpeg-static.dir/jfdctint.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jfdctint.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctint.c -o CMakeFiles/jpeg-static.dir/jfdctint.c.s

CMakeFiles/jpeg-static.dir/jidctflt.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jidctflt.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctflt.c
CMakeFiles/jpeg-static.dir/jidctflt.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object CMakeFiles/jpeg-static.dir/jidctflt.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jidctflt.c.o -MF CMakeFiles/jpeg-static.dir/jidctflt.c.o.d -o CMakeFiles/jpeg-static.dir/jidctflt.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctflt.c

CMakeFiles/jpeg-static.dir/jidctflt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jidctflt.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctflt.c > CMakeFiles/jpeg-static.dir/jidctflt.c.i

CMakeFiles/jpeg-static.dir/jidctflt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jidctflt.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctflt.c -o CMakeFiles/jpeg-static.dir/jidctflt.c.s

CMakeFiles/jpeg-static.dir/jidctfst.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jidctfst.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctfst.c
CMakeFiles/jpeg-static.dir/jidctfst.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object CMakeFiles/jpeg-static.dir/jidctfst.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jidctfst.c.o -MF CMakeFiles/jpeg-static.dir/jidctfst.c.o.d -o CMakeFiles/jpeg-static.dir/jidctfst.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctfst.c

CMakeFiles/jpeg-static.dir/jidctfst.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jidctfst.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctfst.c > CMakeFiles/jpeg-static.dir/jidctfst.c.i

CMakeFiles/jpeg-static.dir/jidctfst.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jidctfst.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctfst.c -o CMakeFiles/jpeg-static.dir/jidctfst.c.s

CMakeFiles/jpeg-static.dir/jidctint.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jidctint.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctint.c
CMakeFiles/jpeg-static.dir/jidctint.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object CMakeFiles/jpeg-static.dir/jidctint.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jidctint.c.o -MF CMakeFiles/jpeg-static.dir/jidctint.c.o.d -o CMakeFiles/jpeg-static.dir/jidctint.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctint.c

CMakeFiles/jpeg-static.dir/jidctint.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jidctint.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctint.c > CMakeFiles/jpeg-static.dir/jidctint.c.i

CMakeFiles/jpeg-static.dir/jidctint.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jidctint.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctint.c -o CMakeFiles/jpeg-static.dir/jidctint.c.s

CMakeFiles/jpeg-static.dir/jidctred.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jidctred.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctred.c
CMakeFiles/jpeg-static.dir/jidctred.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object CMakeFiles/jpeg-static.dir/jidctred.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jidctred.c.o -MF CMakeFiles/jpeg-static.dir/jidctred.c.o.d -o CMakeFiles/jpeg-static.dir/jidctred.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctred.c

CMakeFiles/jpeg-static.dir/jidctred.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jidctred.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctred.c > CMakeFiles/jpeg-static.dir/jidctred.c.i

CMakeFiles/jpeg-static.dir/jidctred.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jidctred.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctred.c -o CMakeFiles/jpeg-static.dir/jidctred.c.s

CMakeFiles/jpeg-static.dir/jquant1.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jquant1.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant1.c
CMakeFiles/jpeg-static.dir/jquant1.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object CMakeFiles/jpeg-static.dir/jquant1.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jquant1.c.o -MF CMakeFiles/jpeg-static.dir/jquant1.c.o.d -o CMakeFiles/jpeg-static.dir/jquant1.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant1.c

CMakeFiles/jpeg-static.dir/jquant1.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jquant1.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant1.c > CMakeFiles/jpeg-static.dir/jquant1.c.i

CMakeFiles/jpeg-static.dir/jquant1.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jquant1.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant1.c -o CMakeFiles/jpeg-static.dir/jquant1.c.s

CMakeFiles/jpeg-static.dir/jquant2.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jquant2.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant2.c
CMakeFiles/jpeg-static.dir/jquant2.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object CMakeFiles/jpeg-static.dir/jquant2.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jquant2.c.o -MF CMakeFiles/jpeg-static.dir/jquant2.c.o.d -o CMakeFiles/jpeg-static.dir/jquant2.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant2.c

CMakeFiles/jpeg-static.dir/jquant2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jquant2.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant2.c > CMakeFiles/jpeg-static.dir/jquant2.c.i

CMakeFiles/jpeg-static.dir/jquant2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jquant2.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant2.c -o CMakeFiles/jpeg-static.dir/jquant2.c.s

CMakeFiles/jpeg-static.dir/jcapimin.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcapimin.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapimin.c
CMakeFiles/jpeg-static.dir/jcapimin.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object CMakeFiles/jpeg-static.dir/jcapimin.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcapimin.c.o -MF CMakeFiles/jpeg-static.dir/jcapimin.c.o.d -o CMakeFiles/jpeg-static.dir/jcapimin.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapimin.c

CMakeFiles/jpeg-static.dir/jcapimin.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcapimin.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapimin.c > CMakeFiles/jpeg-static.dir/jcapimin.c.i

CMakeFiles/jpeg-static.dir/jcapimin.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcapimin.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapimin.c -o CMakeFiles/jpeg-static.dir/jcapimin.c.s

CMakeFiles/jpeg-static.dir/jchuff.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jchuff.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jchuff.c
CMakeFiles/jpeg-static.dir/jchuff.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object CMakeFiles/jpeg-static.dir/jchuff.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jchuff.c.o -MF CMakeFiles/jpeg-static.dir/jchuff.c.o.d -o CMakeFiles/jpeg-static.dir/jchuff.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jchuff.c

CMakeFiles/jpeg-static.dir/jchuff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jchuff.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jchuff.c > CMakeFiles/jpeg-static.dir/jchuff.c.i

CMakeFiles/jpeg-static.dir/jchuff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jchuff.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jchuff.c -o CMakeFiles/jpeg-static.dir/jchuff.c.s

CMakeFiles/jpeg-static.dir/jcicc.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcicc.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcicc.c
CMakeFiles/jpeg-static.dir/jcicc.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object CMakeFiles/jpeg-static.dir/jcicc.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcicc.c.o -MF CMakeFiles/jpeg-static.dir/jcicc.c.o.d -o CMakeFiles/jpeg-static.dir/jcicc.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcicc.c

CMakeFiles/jpeg-static.dir/jcicc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcicc.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcicc.c > CMakeFiles/jpeg-static.dir/jcicc.c.i

CMakeFiles/jpeg-static.dir/jcicc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcicc.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcicc.c -o CMakeFiles/jpeg-static.dir/jcicc.c.s

CMakeFiles/jpeg-static.dir/jcinit.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcinit.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcinit.c
CMakeFiles/jpeg-static.dir/jcinit.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building C object CMakeFiles/jpeg-static.dir/jcinit.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcinit.c.o -MF CMakeFiles/jpeg-static.dir/jcinit.c.o.d -o CMakeFiles/jpeg-static.dir/jcinit.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcinit.c

CMakeFiles/jpeg-static.dir/jcinit.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcinit.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcinit.c > CMakeFiles/jpeg-static.dir/jcinit.c.i

CMakeFiles/jpeg-static.dir/jcinit.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcinit.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcinit.c -o CMakeFiles/jpeg-static.dir/jcinit.c.s

CMakeFiles/jpeg-static.dir/jclhuff.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jclhuff.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclhuff.c
CMakeFiles/jpeg-static.dir/jclhuff.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object CMakeFiles/jpeg-static.dir/jclhuff.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jclhuff.c.o -MF CMakeFiles/jpeg-static.dir/jclhuff.c.o.d -o CMakeFiles/jpeg-static.dir/jclhuff.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclhuff.c

CMakeFiles/jpeg-static.dir/jclhuff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jclhuff.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclhuff.c > CMakeFiles/jpeg-static.dir/jclhuff.c.i

CMakeFiles/jpeg-static.dir/jclhuff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jclhuff.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclhuff.c -o CMakeFiles/jpeg-static.dir/jclhuff.c.s

CMakeFiles/jpeg-static.dir/jcmarker.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcmarker.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmarker.c
CMakeFiles/jpeg-static.dir/jcmarker.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building C object CMakeFiles/jpeg-static.dir/jcmarker.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcmarker.c.o -MF CMakeFiles/jpeg-static.dir/jcmarker.c.o.d -o CMakeFiles/jpeg-static.dir/jcmarker.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmarker.c

CMakeFiles/jpeg-static.dir/jcmarker.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcmarker.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmarker.c > CMakeFiles/jpeg-static.dir/jcmarker.c.i

CMakeFiles/jpeg-static.dir/jcmarker.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcmarker.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmarker.c -o CMakeFiles/jpeg-static.dir/jcmarker.c.s

CMakeFiles/jpeg-static.dir/jcmaster.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcmaster.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmaster.c
CMakeFiles/jpeg-static.dir/jcmaster.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building C object CMakeFiles/jpeg-static.dir/jcmaster.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcmaster.c.o -MF CMakeFiles/jpeg-static.dir/jcmaster.c.o.d -o CMakeFiles/jpeg-static.dir/jcmaster.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmaster.c

CMakeFiles/jpeg-static.dir/jcmaster.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcmaster.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmaster.c > CMakeFiles/jpeg-static.dir/jcmaster.c.i

CMakeFiles/jpeg-static.dir/jcmaster.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcmaster.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmaster.c -o CMakeFiles/jpeg-static.dir/jcmaster.c.s

CMakeFiles/jpeg-static.dir/jcomapi.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcomapi.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcomapi.c
CMakeFiles/jpeg-static.dir/jcomapi.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building C object CMakeFiles/jpeg-static.dir/jcomapi.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcomapi.c.o -MF CMakeFiles/jpeg-static.dir/jcomapi.c.o.d -o CMakeFiles/jpeg-static.dir/jcomapi.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcomapi.c

CMakeFiles/jpeg-static.dir/jcomapi.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcomapi.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcomapi.c > CMakeFiles/jpeg-static.dir/jcomapi.c.i

CMakeFiles/jpeg-static.dir/jcomapi.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcomapi.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcomapi.c -o CMakeFiles/jpeg-static.dir/jcomapi.c.s

CMakeFiles/jpeg-static.dir/jcparam.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcparam.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcparam.c
CMakeFiles/jpeg-static.dir/jcparam.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building C object CMakeFiles/jpeg-static.dir/jcparam.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcparam.c.o -MF CMakeFiles/jpeg-static.dir/jcparam.c.o.d -o CMakeFiles/jpeg-static.dir/jcparam.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcparam.c

CMakeFiles/jpeg-static.dir/jcparam.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcparam.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcparam.c > CMakeFiles/jpeg-static.dir/jcparam.c.i

CMakeFiles/jpeg-static.dir/jcparam.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcparam.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcparam.c -o CMakeFiles/jpeg-static.dir/jcparam.c.s

CMakeFiles/jpeg-static.dir/jcphuff.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcphuff.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcphuff.c
CMakeFiles/jpeg-static.dir/jcphuff.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building C object CMakeFiles/jpeg-static.dir/jcphuff.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcphuff.c.o -MF CMakeFiles/jpeg-static.dir/jcphuff.c.o.d -o CMakeFiles/jpeg-static.dir/jcphuff.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcphuff.c

CMakeFiles/jpeg-static.dir/jcphuff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcphuff.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcphuff.c > CMakeFiles/jpeg-static.dir/jcphuff.c.i

CMakeFiles/jpeg-static.dir/jcphuff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcphuff.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcphuff.c -o CMakeFiles/jpeg-static.dir/jcphuff.c.s

CMakeFiles/jpeg-static.dir/jctrans.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jctrans.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jctrans.c
CMakeFiles/jpeg-static.dir/jctrans.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building C object CMakeFiles/jpeg-static.dir/jctrans.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jctrans.c.o -MF CMakeFiles/jpeg-static.dir/jctrans.c.o.d -o CMakeFiles/jpeg-static.dir/jctrans.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jctrans.c

CMakeFiles/jpeg-static.dir/jctrans.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jctrans.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jctrans.c > CMakeFiles/jpeg-static.dir/jctrans.c.i

CMakeFiles/jpeg-static.dir/jctrans.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jctrans.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jctrans.c -o CMakeFiles/jpeg-static.dir/jctrans.c.s

CMakeFiles/jpeg-static.dir/jdapimin.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdapimin.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapimin.c
CMakeFiles/jpeg-static.dir/jdapimin.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building C object CMakeFiles/jpeg-static.dir/jdapimin.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdapimin.c.o -MF CMakeFiles/jpeg-static.dir/jdapimin.c.o.d -o CMakeFiles/jpeg-static.dir/jdapimin.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapimin.c

CMakeFiles/jpeg-static.dir/jdapimin.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdapimin.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapimin.c > CMakeFiles/jpeg-static.dir/jdapimin.c.i

CMakeFiles/jpeg-static.dir/jdapimin.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdapimin.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapimin.c -o CMakeFiles/jpeg-static.dir/jdapimin.c.s

CMakeFiles/jpeg-static.dir/jdatadst.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdatadst.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdatadst.c
CMakeFiles/jpeg-static.dir/jdatadst.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building C object CMakeFiles/jpeg-static.dir/jdatadst.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdatadst.c.o -MF CMakeFiles/jpeg-static.dir/jdatadst.c.o.d -o CMakeFiles/jpeg-static.dir/jdatadst.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdatadst.c

CMakeFiles/jpeg-static.dir/jdatadst.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdatadst.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdatadst.c > CMakeFiles/jpeg-static.dir/jdatadst.c.i

CMakeFiles/jpeg-static.dir/jdatadst.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdatadst.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdatadst.c -o CMakeFiles/jpeg-static.dir/jdatadst.c.s

CMakeFiles/jpeg-static.dir/jdatasrc.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdatasrc.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdatasrc.c
CMakeFiles/jpeg-static.dir/jdatasrc.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building C object CMakeFiles/jpeg-static.dir/jdatasrc.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdatasrc.c.o -MF CMakeFiles/jpeg-static.dir/jdatasrc.c.o.d -o CMakeFiles/jpeg-static.dir/jdatasrc.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdatasrc.c

CMakeFiles/jpeg-static.dir/jdatasrc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdatasrc.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdatasrc.c > CMakeFiles/jpeg-static.dir/jdatasrc.c.i

CMakeFiles/jpeg-static.dir/jdatasrc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdatasrc.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdatasrc.c -o CMakeFiles/jpeg-static.dir/jdatasrc.c.s

CMakeFiles/jpeg-static.dir/jdhuff.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdhuff.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdhuff.c
CMakeFiles/jpeg-static.dir/jdhuff.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building C object CMakeFiles/jpeg-static.dir/jdhuff.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdhuff.c.o -MF CMakeFiles/jpeg-static.dir/jdhuff.c.o.d -o CMakeFiles/jpeg-static.dir/jdhuff.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdhuff.c

CMakeFiles/jpeg-static.dir/jdhuff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdhuff.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdhuff.c > CMakeFiles/jpeg-static.dir/jdhuff.c.i

CMakeFiles/jpeg-static.dir/jdhuff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdhuff.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdhuff.c -o CMakeFiles/jpeg-static.dir/jdhuff.c.s

CMakeFiles/jpeg-static.dir/jdicc.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdicc.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdicc.c
CMakeFiles/jpeg-static.dir/jdicc.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building C object CMakeFiles/jpeg-static.dir/jdicc.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdicc.c.o -MF CMakeFiles/jpeg-static.dir/jdicc.c.o.d -o CMakeFiles/jpeg-static.dir/jdicc.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdicc.c

CMakeFiles/jpeg-static.dir/jdicc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdicc.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdicc.c > CMakeFiles/jpeg-static.dir/jdicc.c.i

CMakeFiles/jpeg-static.dir/jdicc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdicc.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdicc.c -o CMakeFiles/jpeg-static.dir/jdicc.c.s

CMakeFiles/jpeg-static.dir/jdinput.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdinput.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdinput.c
CMakeFiles/jpeg-static.dir/jdinput.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building C object CMakeFiles/jpeg-static.dir/jdinput.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdinput.c.o -MF CMakeFiles/jpeg-static.dir/jdinput.c.o.d -o CMakeFiles/jpeg-static.dir/jdinput.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdinput.c

CMakeFiles/jpeg-static.dir/jdinput.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdinput.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdinput.c > CMakeFiles/jpeg-static.dir/jdinput.c.i

CMakeFiles/jpeg-static.dir/jdinput.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdinput.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdinput.c -o CMakeFiles/jpeg-static.dir/jdinput.c.s

CMakeFiles/jpeg-static.dir/jdlhuff.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdlhuff.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlhuff.c
CMakeFiles/jpeg-static.dir/jdlhuff.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building C object CMakeFiles/jpeg-static.dir/jdlhuff.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdlhuff.c.o -MF CMakeFiles/jpeg-static.dir/jdlhuff.c.o.d -o CMakeFiles/jpeg-static.dir/jdlhuff.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlhuff.c

CMakeFiles/jpeg-static.dir/jdlhuff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdlhuff.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlhuff.c > CMakeFiles/jpeg-static.dir/jdlhuff.c.i

CMakeFiles/jpeg-static.dir/jdlhuff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdlhuff.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlhuff.c -o CMakeFiles/jpeg-static.dir/jdlhuff.c.s

CMakeFiles/jpeg-static.dir/jdmarker.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdmarker.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmarker.c
CMakeFiles/jpeg-static.dir/jdmarker.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building C object CMakeFiles/jpeg-static.dir/jdmarker.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdmarker.c.o -MF CMakeFiles/jpeg-static.dir/jdmarker.c.o.d -o CMakeFiles/jpeg-static.dir/jdmarker.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmarker.c

CMakeFiles/jpeg-static.dir/jdmarker.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdmarker.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmarker.c > CMakeFiles/jpeg-static.dir/jdmarker.c.i

CMakeFiles/jpeg-static.dir/jdmarker.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdmarker.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmarker.c -o CMakeFiles/jpeg-static.dir/jdmarker.c.s

CMakeFiles/jpeg-static.dir/jdmaster.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdmaster.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmaster.c
CMakeFiles/jpeg-static.dir/jdmaster.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Building C object CMakeFiles/jpeg-static.dir/jdmaster.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdmaster.c.o -MF CMakeFiles/jpeg-static.dir/jdmaster.c.o.d -o CMakeFiles/jpeg-static.dir/jdmaster.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmaster.c

CMakeFiles/jpeg-static.dir/jdmaster.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdmaster.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmaster.c > CMakeFiles/jpeg-static.dir/jdmaster.c.i

CMakeFiles/jpeg-static.dir/jdmaster.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdmaster.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmaster.c -o CMakeFiles/jpeg-static.dir/jdmaster.c.s

CMakeFiles/jpeg-static.dir/jdphuff.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdphuff.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdphuff.c
CMakeFiles/jpeg-static.dir/jdphuff.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Building C object CMakeFiles/jpeg-static.dir/jdphuff.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdphuff.c.o -MF CMakeFiles/jpeg-static.dir/jdphuff.c.o.d -o CMakeFiles/jpeg-static.dir/jdphuff.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdphuff.c

CMakeFiles/jpeg-static.dir/jdphuff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdphuff.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdphuff.c > CMakeFiles/jpeg-static.dir/jdphuff.c.i

CMakeFiles/jpeg-static.dir/jdphuff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdphuff.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdphuff.c -o CMakeFiles/jpeg-static.dir/jdphuff.c.s

CMakeFiles/jpeg-static.dir/jdtrans.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdtrans.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdtrans.c
CMakeFiles/jpeg-static.dir/jdtrans.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Building C object CMakeFiles/jpeg-static.dir/jdtrans.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdtrans.c.o -MF CMakeFiles/jpeg-static.dir/jdtrans.c.o.d -o CMakeFiles/jpeg-static.dir/jdtrans.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdtrans.c

CMakeFiles/jpeg-static.dir/jdtrans.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdtrans.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdtrans.c > CMakeFiles/jpeg-static.dir/jdtrans.c.i

CMakeFiles/jpeg-static.dir/jdtrans.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdtrans.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdtrans.c -o CMakeFiles/jpeg-static.dir/jdtrans.c.s

CMakeFiles/jpeg-static.dir/jerror.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jerror.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jerror.c
CMakeFiles/jpeg-static.dir/jerror.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Building C object CMakeFiles/jpeg-static.dir/jerror.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jerror.c.o -MF CMakeFiles/jpeg-static.dir/jerror.c.o.d -o CMakeFiles/jpeg-static.dir/jerror.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jerror.c

CMakeFiles/jpeg-static.dir/jerror.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jerror.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jerror.c > CMakeFiles/jpeg-static.dir/jerror.c.i

CMakeFiles/jpeg-static.dir/jerror.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jerror.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jerror.c -o CMakeFiles/jpeg-static.dir/jerror.c.s

CMakeFiles/jpeg-static.dir/jfdctflt.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jfdctflt.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctflt.c
CMakeFiles/jpeg-static.dir/jfdctflt.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Building C object CMakeFiles/jpeg-static.dir/jfdctflt.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jfdctflt.c.o -MF CMakeFiles/jpeg-static.dir/jfdctflt.c.o.d -o CMakeFiles/jpeg-static.dir/jfdctflt.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctflt.c

CMakeFiles/jpeg-static.dir/jfdctflt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jfdctflt.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctflt.c > CMakeFiles/jpeg-static.dir/jfdctflt.c.i

CMakeFiles/jpeg-static.dir/jfdctflt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jfdctflt.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctflt.c -o CMakeFiles/jpeg-static.dir/jfdctflt.c.s

CMakeFiles/jpeg-static.dir/jmemmgr.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jmemmgr.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jmemmgr.c
CMakeFiles/jpeg-static.dir/jmemmgr.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Building C object CMakeFiles/jpeg-static.dir/jmemmgr.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jmemmgr.c.o -MF CMakeFiles/jpeg-static.dir/jmemmgr.c.o.d -o CMakeFiles/jpeg-static.dir/jmemmgr.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jmemmgr.c

CMakeFiles/jpeg-static.dir/jmemmgr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jmemmgr.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jmemmgr.c > CMakeFiles/jpeg-static.dir/jmemmgr.c.i

CMakeFiles/jpeg-static.dir/jmemmgr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jmemmgr.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jmemmgr.c -o CMakeFiles/jpeg-static.dir/jmemmgr.c.s

CMakeFiles/jpeg-static.dir/jmemnobs.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jmemnobs.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jmemnobs.c
CMakeFiles/jpeg-static.dir/jmemnobs.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_54) "Building C object CMakeFiles/jpeg-static.dir/jmemnobs.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jmemnobs.c.o -MF CMakeFiles/jpeg-static.dir/jmemnobs.c.o.d -o CMakeFiles/jpeg-static.dir/jmemnobs.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jmemnobs.c

CMakeFiles/jpeg-static.dir/jmemnobs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jmemnobs.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jmemnobs.c > CMakeFiles/jpeg-static.dir/jmemnobs.c.i

CMakeFiles/jpeg-static.dir/jmemnobs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jmemnobs.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jmemnobs.c -o CMakeFiles/jpeg-static.dir/jmemnobs.c.s

CMakeFiles/jpeg-static.dir/jaricom.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jaricom.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jaricom.c
CMakeFiles/jpeg-static.dir/jaricom.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_55) "Building C object CMakeFiles/jpeg-static.dir/jaricom.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jaricom.c.o -MF CMakeFiles/jpeg-static.dir/jaricom.c.o.d -o CMakeFiles/jpeg-static.dir/jaricom.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jaricom.c

CMakeFiles/jpeg-static.dir/jaricom.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jaricom.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jaricom.c > CMakeFiles/jpeg-static.dir/jaricom.c.i

CMakeFiles/jpeg-static.dir/jaricom.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jaricom.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jaricom.c -o CMakeFiles/jpeg-static.dir/jaricom.c.s

CMakeFiles/jpeg-static.dir/jcarith.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jcarith.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcarith.c
CMakeFiles/jpeg-static.dir/jcarith.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_56) "Building C object CMakeFiles/jpeg-static.dir/jcarith.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jcarith.c.o -MF CMakeFiles/jpeg-static.dir/jcarith.c.o.d -o CMakeFiles/jpeg-static.dir/jcarith.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcarith.c

CMakeFiles/jpeg-static.dir/jcarith.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jcarith.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcarith.c > CMakeFiles/jpeg-static.dir/jcarith.c.i

CMakeFiles/jpeg-static.dir/jcarith.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jcarith.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcarith.c -o CMakeFiles/jpeg-static.dir/jcarith.c.s

CMakeFiles/jpeg-static.dir/jdarith.c.o: CMakeFiles/jpeg-static.dir/flags.make
CMakeFiles/jpeg-static.dir/jdarith.c.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdarith.c
CMakeFiles/jpeg-static.dir/jdarith.c.o: CMakeFiles/jpeg-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_57) "Building C object CMakeFiles/jpeg-static.dir/jdarith.c.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/jpeg-static.dir/jdarith.c.o -MF CMakeFiles/jpeg-static.dir/jdarith.c.o.d -o CMakeFiles/jpeg-static.dir/jdarith.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdarith.c

CMakeFiles/jpeg-static.dir/jdarith.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/jpeg-static.dir/jdarith.c.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdarith.c > CMakeFiles/jpeg-static.dir/jdarith.c.i

CMakeFiles/jpeg-static.dir/jdarith.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/jpeg-static.dir/jdarith.c.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdarith.c -o CMakeFiles/jpeg-static.dir/jdarith.c.s

# Object files for target jpeg-static
jpeg__static_OBJECTS = \
"CMakeFiles/jpeg-static.dir/jcapistd.c.o" \
"CMakeFiles/jpeg-static.dir/jccolor.c.o" \
"CMakeFiles/jpeg-static.dir/jcdiffct.c.o" \
"CMakeFiles/jpeg-static.dir/jclossls.c.o" \
"CMakeFiles/jpeg-static.dir/jcmainct.c.o" \
"CMakeFiles/jpeg-static.dir/jcprepct.c.o" \
"CMakeFiles/jpeg-static.dir/jcsample.c.o" \
"CMakeFiles/jpeg-static.dir/jdapistd.c.o" \
"CMakeFiles/jpeg-static.dir/jdcolor.c.o" \
"CMakeFiles/jpeg-static.dir/jddiffct.c.o" \
"CMakeFiles/jpeg-static.dir/jdlossls.c.o" \
"CMakeFiles/jpeg-static.dir/jdmainct.c.o" \
"CMakeFiles/jpeg-static.dir/jdpostct.c.o" \
"CMakeFiles/jpeg-static.dir/jdsample.c.o" \
"CMakeFiles/jpeg-static.dir/jutils.c.o" \
"CMakeFiles/jpeg-static.dir/jccoefct.c.o" \
"CMakeFiles/jpeg-static.dir/jcdctmgr.c.o" \
"CMakeFiles/jpeg-static.dir/jdcoefct.c.o" \
"CMakeFiles/jpeg-static.dir/jddctmgr.c.o" \
"CMakeFiles/jpeg-static.dir/jdmerge.c.o" \
"CMakeFiles/jpeg-static.dir/jfdctfst.c.o" \
"CMakeFiles/jpeg-static.dir/jfdctint.c.o" \
"CMakeFiles/jpeg-static.dir/jidctflt.c.o" \
"CMakeFiles/jpeg-static.dir/jidctfst.c.o" \
"CMakeFiles/jpeg-static.dir/jidctint.c.o" \
"CMakeFiles/jpeg-static.dir/jidctred.c.o" \
"CMakeFiles/jpeg-static.dir/jquant1.c.o" \
"CMakeFiles/jpeg-static.dir/jquant2.c.o" \
"CMakeFiles/jpeg-static.dir/jcapimin.c.o" \
"CMakeFiles/jpeg-static.dir/jchuff.c.o" \
"CMakeFiles/jpeg-static.dir/jcicc.c.o" \
"CMakeFiles/jpeg-static.dir/jcinit.c.o" \
"CMakeFiles/jpeg-static.dir/jclhuff.c.o" \
"CMakeFiles/jpeg-static.dir/jcmarker.c.o" \
"CMakeFiles/jpeg-static.dir/jcmaster.c.o" \
"CMakeFiles/jpeg-static.dir/jcomapi.c.o" \
"CMakeFiles/jpeg-static.dir/jcparam.c.o" \
"CMakeFiles/jpeg-static.dir/jcphuff.c.o" \
"CMakeFiles/jpeg-static.dir/jctrans.c.o" \
"CMakeFiles/jpeg-static.dir/jdapimin.c.o" \
"CMakeFiles/jpeg-static.dir/jdatadst.c.o" \
"CMakeFiles/jpeg-static.dir/jdatasrc.c.o" \
"CMakeFiles/jpeg-static.dir/jdhuff.c.o" \
"CMakeFiles/jpeg-static.dir/jdicc.c.o" \
"CMakeFiles/jpeg-static.dir/jdinput.c.o" \
"CMakeFiles/jpeg-static.dir/jdlhuff.c.o" \
"CMakeFiles/jpeg-static.dir/jdmarker.c.o" \
"CMakeFiles/jpeg-static.dir/jdmaster.c.o" \
"CMakeFiles/jpeg-static.dir/jdphuff.c.o" \
"CMakeFiles/jpeg-static.dir/jdtrans.c.o" \
"CMakeFiles/jpeg-static.dir/jerror.c.o" \
"CMakeFiles/jpeg-static.dir/jfdctflt.c.o" \
"CMakeFiles/jpeg-static.dir/jmemmgr.c.o" \
"CMakeFiles/jpeg-static.dir/jmemnobs.c.o" \
"CMakeFiles/jpeg-static.dir/jaricom.c.o" \
"CMakeFiles/jpeg-static.dir/jcarith.c.o" \
"CMakeFiles/jpeg-static.dir/jdarith.c.o"

# External object files for target jpeg-static
jpeg__static_EXTERNAL_OBJECTS = \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jcgray-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jcphuff-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jcsample-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jdmerge-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jdsample-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jfdctfst-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jidctred-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jquanti-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jccolor-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jidctint-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jidctfst-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/aarch64/jchuff-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jdcolor-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/jfdctint-neon.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/simd/CMakeFiles/simd.dir/arm/aarch64/jsimd.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jcapistd.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jccolor.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jcdiffct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jclossls.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jcmainct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jcprepct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jcsample.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jdapistd.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jdcolor.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jddiffct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jdlossls.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jdmainct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jdpostct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jdsample.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jutils.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jccoefct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jcdctmgr.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jdcoefct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jddctmgr.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jdmerge.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jfdctfst.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jfdctint.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jidctflt.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jidctfst.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jidctint.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jidctred.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jquant1.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg12-static.dir/jquant2.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jcapistd.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jccolor.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jcdiffct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jclossls.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jcmainct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jcprepct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jcsample.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jdapistd.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jdcolor.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jddiffct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jdlossls.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jdmainct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jdpostct.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jdsample.c.o" \
"/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg16-static.dir/jutils.c.o"

libjpeg.a: CMakeFiles/jpeg-static.dir/jcapistd.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jccolor.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcdiffct.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jclossls.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcmainct.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcprepct.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcsample.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdapistd.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdcolor.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jddiffct.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdlossls.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdmainct.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdpostct.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdsample.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jutils.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jccoefct.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcdctmgr.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdcoefct.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jddctmgr.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdmerge.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jfdctfst.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jfdctint.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jidctflt.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jidctfst.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jidctint.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jidctred.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jquant1.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jquant2.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcapimin.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jchuff.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcicc.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcinit.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jclhuff.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcmarker.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcmaster.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcomapi.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcparam.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcphuff.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jctrans.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdapimin.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdatadst.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdatasrc.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdhuff.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdicc.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdinput.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdlhuff.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdmarker.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdmaster.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdphuff.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdtrans.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jerror.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jfdctflt.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jmemmgr.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jmemnobs.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jaricom.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jcarith.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/jdarith.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jcgray-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jcphuff-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jcsample-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jdmerge-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jdsample-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jfdctfst-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jidctred-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jquanti-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jccolor-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jidctint-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jidctfst-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/aarch64/jchuff-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jdcolor-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/jfdctint-neon.c.o
libjpeg.a: simd/CMakeFiles/simd.dir/arm/aarch64/jsimd.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jcapistd.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jccolor.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jcdiffct.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jclossls.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jcmainct.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jcprepct.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jcsample.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jdapistd.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jdcolor.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jddiffct.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jdlossls.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jdmainct.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jdpostct.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jdsample.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jutils.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jccoefct.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jcdctmgr.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jdcoefct.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jddctmgr.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jdmerge.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jfdctfst.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jfdctint.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jidctflt.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jidctfst.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jidctint.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jidctred.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jquant1.c.o
libjpeg.a: CMakeFiles/jpeg12-static.dir/jquant2.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jcapistd.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jccolor.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jcdiffct.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jclossls.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jcmainct.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jcprepct.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jcsample.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jdapistd.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jdcolor.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jddiffct.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jdlossls.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jdmainct.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jdpostct.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jdsample.c.o
libjpeg.a: CMakeFiles/jpeg16-static.dir/jutils.c.o
libjpeg.a: CMakeFiles/jpeg-static.dir/build.make
libjpeg.a: CMakeFiles/jpeg-static.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_58) "Linking C static library libjpeg.a"
	$(CMAKE_COMMAND) -P CMakeFiles/jpeg-static.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/jpeg-static.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/jpeg-static.dir/build: libjpeg.a
.PHONY : CMakeFiles/jpeg-static.dir/build

CMakeFiles/jpeg-static.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/jpeg-static.dir/cmake_clean.cmake
.PHONY : CMakeFiles/jpeg-static.dir/clean

CMakeFiles/jpeg-static.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG-build/CMakeFiles/jpeg-static.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/jpeg-static.dir/depend

