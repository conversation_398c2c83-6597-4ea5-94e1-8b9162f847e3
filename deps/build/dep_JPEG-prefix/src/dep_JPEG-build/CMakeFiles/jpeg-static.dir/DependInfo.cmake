
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jaricom.c" "CMakeFiles/jpeg-static.dir/jaricom.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jaricom.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapimin.c" "CMakeFiles/jpeg-static.dir/jcapimin.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcapimin.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcapistd.c" "CMakeFiles/jpeg-static.dir/jcapistd.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcapistd.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcarith.c" "CMakeFiles/jpeg-static.dir/jcarith.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcarith.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccoefct.c" "CMakeFiles/jpeg-static.dir/jccoefct.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jccoefct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jccolor.c" "CMakeFiles/jpeg-static.dir/jccolor.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jccolor.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdctmgr.c" "CMakeFiles/jpeg-static.dir/jcdctmgr.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcdctmgr.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcdiffct.c" "CMakeFiles/jpeg-static.dir/jcdiffct.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcdiffct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jchuff.c" "CMakeFiles/jpeg-static.dir/jchuff.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jchuff.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcicc.c" "CMakeFiles/jpeg-static.dir/jcicc.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcicc.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcinit.c" "CMakeFiles/jpeg-static.dir/jcinit.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcinit.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclhuff.c" "CMakeFiles/jpeg-static.dir/jclhuff.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jclhuff.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jclossls.c" "CMakeFiles/jpeg-static.dir/jclossls.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jclossls.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmainct.c" "CMakeFiles/jpeg-static.dir/jcmainct.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcmainct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmarker.c" "CMakeFiles/jpeg-static.dir/jcmarker.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcmarker.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcmaster.c" "CMakeFiles/jpeg-static.dir/jcmaster.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcmaster.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcomapi.c" "CMakeFiles/jpeg-static.dir/jcomapi.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcomapi.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcparam.c" "CMakeFiles/jpeg-static.dir/jcparam.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcparam.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcphuff.c" "CMakeFiles/jpeg-static.dir/jcphuff.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcphuff.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcprepct.c" "CMakeFiles/jpeg-static.dir/jcprepct.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcprepct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jcsample.c" "CMakeFiles/jpeg-static.dir/jcsample.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jcsample.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jctrans.c" "CMakeFiles/jpeg-static.dir/jctrans.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jctrans.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapimin.c" "CMakeFiles/jpeg-static.dir/jdapimin.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdapimin.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdapistd.c" "CMakeFiles/jpeg-static.dir/jdapistd.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdapistd.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdarith.c" "CMakeFiles/jpeg-static.dir/jdarith.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdarith.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdatadst.c" "CMakeFiles/jpeg-static.dir/jdatadst.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdatadst.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdatasrc.c" "CMakeFiles/jpeg-static.dir/jdatasrc.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdatasrc.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcoefct.c" "CMakeFiles/jpeg-static.dir/jdcoefct.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdcoefct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdcolor.c" "CMakeFiles/jpeg-static.dir/jdcolor.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdcolor.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddctmgr.c" "CMakeFiles/jpeg-static.dir/jddctmgr.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jddctmgr.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jddiffct.c" "CMakeFiles/jpeg-static.dir/jddiffct.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jddiffct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdhuff.c" "CMakeFiles/jpeg-static.dir/jdhuff.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdhuff.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdicc.c" "CMakeFiles/jpeg-static.dir/jdicc.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdicc.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdinput.c" "CMakeFiles/jpeg-static.dir/jdinput.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdinput.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlhuff.c" "CMakeFiles/jpeg-static.dir/jdlhuff.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdlhuff.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdlossls.c" "CMakeFiles/jpeg-static.dir/jdlossls.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdlossls.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmainct.c" "CMakeFiles/jpeg-static.dir/jdmainct.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdmainct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmarker.c" "CMakeFiles/jpeg-static.dir/jdmarker.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdmarker.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmaster.c" "CMakeFiles/jpeg-static.dir/jdmaster.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdmaster.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdmerge.c" "CMakeFiles/jpeg-static.dir/jdmerge.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdmerge.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdphuff.c" "CMakeFiles/jpeg-static.dir/jdphuff.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdphuff.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdpostct.c" "CMakeFiles/jpeg-static.dir/jdpostct.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdpostct.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdsample.c" "CMakeFiles/jpeg-static.dir/jdsample.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdsample.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jdtrans.c" "CMakeFiles/jpeg-static.dir/jdtrans.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jdtrans.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jerror.c" "CMakeFiles/jpeg-static.dir/jerror.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jerror.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctflt.c" "CMakeFiles/jpeg-static.dir/jfdctflt.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jfdctflt.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctfst.c" "CMakeFiles/jpeg-static.dir/jfdctfst.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jfdctfst.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jfdctint.c" "CMakeFiles/jpeg-static.dir/jfdctint.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jfdctint.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctflt.c" "CMakeFiles/jpeg-static.dir/jidctflt.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jidctflt.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctfst.c" "CMakeFiles/jpeg-static.dir/jidctfst.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jidctfst.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctint.c" "CMakeFiles/jpeg-static.dir/jidctint.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jidctint.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jidctred.c" "CMakeFiles/jpeg-static.dir/jidctred.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jidctred.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jmemmgr.c" "CMakeFiles/jpeg-static.dir/jmemmgr.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jmemmgr.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jmemnobs.c" "CMakeFiles/jpeg-static.dir/jmemnobs.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jmemnobs.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant1.c" "CMakeFiles/jpeg-static.dir/jquant1.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jquant1.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jquant2.c" "CMakeFiles/jpeg-static.dir/jquant2.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jquant2.c.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_JPEG-prefix/src/dep_JPEG/jutils.c" "CMakeFiles/jpeg-static.dir/jutils.c.o" "gcc" "CMakeFiles/jpeg-static.dir/jutils.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
