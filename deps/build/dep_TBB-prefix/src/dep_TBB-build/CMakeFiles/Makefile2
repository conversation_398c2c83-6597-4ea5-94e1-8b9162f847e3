# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: src/tbb/all
all: src/tbbmalloc/all
all: src/tbbmalloc_proxy/all
all: cmake/post_install/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: src/tbb/preinstall
preinstall: src/tbbmalloc/preinstall
preinstall: src/tbbmalloc_proxy/preinstall
preinstall: cmake/post_install/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: src/tbb/clean
clean: src/tbbmalloc/clean
clean: src/tbbmalloc_proxy/clean
clean: cmake/post_install/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory cmake/post_install

# Recursive "all" directory target.
cmake/post_install/all:
.PHONY : cmake/post_install/all

# Recursive "preinstall" directory target.
cmake/post_install/preinstall:
.PHONY : cmake/post_install/preinstall

# Recursive "clean" directory target.
cmake/post_install/clean:
.PHONY : cmake/post_install/clean

#=============================================================================
# Directory level rules for directory src/tbb

# Recursive "all" directory target.
src/tbb/all: src/tbb/CMakeFiles/tbb.dir/all
.PHONY : src/tbb/all

# Recursive "preinstall" directory target.
src/tbb/preinstall:
.PHONY : src/tbb/preinstall

# Recursive "clean" directory target.
src/tbb/clean: src/tbb/CMakeFiles/tbb.dir/clean
.PHONY : src/tbb/clean

#=============================================================================
# Directory level rules for directory src/tbbmalloc

# Recursive "all" directory target.
src/tbbmalloc/all: src/tbbmalloc/CMakeFiles/tbbmalloc.dir/all
.PHONY : src/tbbmalloc/all

# Recursive "preinstall" directory target.
src/tbbmalloc/preinstall:
.PHONY : src/tbbmalloc/preinstall

# Recursive "clean" directory target.
src/tbbmalloc/clean: src/tbbmalloc/CMakeFiles/tbbmalloc.dir/clean
.PHONY : src/tbbmalloc/clean

#=============================================================================
# Directory level rules for directory src/tbbmalloc_proxy

# Recursive "all" directory target.
src/tbbmalloc_proxy/all:
.PHONY : src/tbbmalloc_proxy/all

# Recursive "preinstall" directory target.
src/tbbmalloc_proxy/preinstall:
.PHONY : src/tbbmalloc_proxy/preinstall

# Recursive "clean" directory target.
src/tbbmalloc_proxy/clean:
.PHONY : src/tbbmalloc_proxy/clean

#=============================================================================
# Target rules for target src/tbb/CMakeFiles/tbb.dir

# All Build rule for target.
src/tbb/CMakeFiles/tbb.dir/all:
	$(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/depend
	$(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29 "Built target tbb"
.PHONY : src/tbb/CMakeFiles/tbb.dir/all

# Build rule for subdir invocation for target.
src/tbb/CMakeFiles/tbb.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles 29
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/tbb/CMakeFiles/tbb.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles 0
.PHONY : src/tbb/CMakeFiles/tbb.dir/rule

# Convenience name for target.
tbb: src/tbb/CMakeFiles/tbb.dir/rule
.PHONY : tbb

# clean rule for target.
src/tbb/CMakeFiles/tbb.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/clean
.PHONY : src/tbb/CMakeFiles/tbb.dir/clean

#=============================================================================
# Target rules for target src/tbbmalloc/CMakeFiles/tbbmalloc.dir

# All Build rule for target.
src/tbbmalloc/CMakeFiles/tbbmalloc.dir/all:
	$(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/depend
	$(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=30,31,32,33,34,35,36 "Built target tbbmalloc"
.PHONY : src/tbbmalloc/CMakeFiles/tbbmalloc.dir/all

# Build rule for subdir invocation for target.
src/tbbmalloc/CMakeFiles/tbbmalloc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/tbbmalloc/CMakeFiles/tbbmalloc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles 0
.PHONY : src/tbbmalloc/CMakeFiles/tbbmalloc.dir/rule

# Convenience name for target.
tbbmalloc: src/tbbmalloc/CMakeFiles/tbbmalloc.dir/rule
.PHONY : tbbmalloc

# clean rule for target.
src/tbbmalloc/CMakeFiles/tbbmalloc.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/clean
.PHONY : src/tbbmalloc/CMakeFiles/tbbmalloc.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

