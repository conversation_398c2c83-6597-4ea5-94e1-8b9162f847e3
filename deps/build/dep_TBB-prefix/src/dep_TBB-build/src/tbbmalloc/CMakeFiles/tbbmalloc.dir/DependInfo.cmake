
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/itt_notify.cpp" "src/tbbmalloc/CMakeFiles/tbbmalloc.dir/__/tbb/itt_notify.cpp.o" "gcc" "src/tbbmalloc/CMakeFiles/tbbmalloc.dir/__/tbb/itt_notify.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbbmalloc/backend.cpp" "src/tbbmalloc/CMakeFiles/tbbmalloc.dir/backend.cpp.o" "gcc" "src/tbbmalloc/CMakeFiles/tbbmalloc.dir/backend.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbbmalloc/backref.cpp" "src/tbbmalloc/CMakeFiles/tbbmalloc.dir/backref.cpp.o" "gcc" "src/tbbmalloc/CMakeFiles/tbbmalloc.dir/backref.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbbmalloc/frontend.cpp" "src/tbbmalloc/CMakeFiles/tbbmalloc.dir/frontend.cpp.o" "gcc" "src/tbbmalloc/CMakeFiles/tbbmalloc.dir/frontend.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbbmalloc/large_objects.cpp" "src/tbbmalloc/CMakeFiles/tbbmalloc.dir/large_objects.cpp.o" "gcc" "src/tbbmalloc/CMakeFiles/tbbmalloc.dir/large_objects.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbbmalloc/tbbmalloc.cpp" "src/tbbmalloc/CMakeFiles/tbbmalloc.dir/tbbmalloc.cpp.o" "gcc" "src/tbbmalloc/CMakeFiles/tbbmalloc.dir/tbbmalloc.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
