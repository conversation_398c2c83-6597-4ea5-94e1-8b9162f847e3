# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool..."
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && /Applications/CMake.app/Contents/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool for source..."
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && /Applications/CMake.app/Contents/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Applications/CMake.app/Contents/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Applications/CMake.app/Contents/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"devel\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbbmalloc//CMakeFiles/progress.marks
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/tbbmalloc/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/tbbmalloc/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/tbbmalloc/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/tbbmalloc/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/tbbmalloc/CMakeFiles/tbbmalloc.dir/rule:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/tbbmalloc/CMakeFiles/tbbmalloc.dir/rule
.PHONY : src/tbbmalloc/CMakeFiles/tbbmalloc.dir/rule

# Convenience name for target.
tbbmalloc: src/tbbmalloc/CMakeFiles/tbbmalloc.dir/rule
.PHONY : tbbmalloc

# fast build rule for target.
tbbmalloc/fast:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build
.PHONY : tbbmalloc/fast

__/tbb/itt_notify.o: __/tbb/itt_notify.cpp.o
.PHONY : __/tbb/itt_notify.o

# target to build an object file
__/tbb/itt_notify.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/__/tbb/itt_notify.cpp.o
.PHONY : __/tbb/itt_notify.cpp.o

__/tbb/itt_notify.i: __/tbb/itt_notify.cpp.i
.PHONY : __/tbb/itt_notify.i

# target to preprocess a source file
__/tbb/itt_notify.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/__/tbb/itt_notify.cpp.i
.PHONY : __/tbb/itt_notify.cpp.i

__/tbb/itt_notify.s: __/tbb/itt_notify.cpp.s
.PHONY : __/tbb/itt_notify.s

# target to generate assembly for a file
__/tbb/itt_notify.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/__/tbb/itt_notify.cpp.s
.PHONY : __/tbb/itt_notify.cpp.s

backend.o: backend.cpp.o
.PHONY : backend.o

# target to build an object file
backend.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/backend.cpp.o
.PHONY : backend.cpp.o

backend.i: backend.cpp.i
.PHONY : backend.i

# target to preprocess a source file
backend.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/backend.cpp.i
.PHONY : backend.cpp.i

backend.s: backend.cpp.s
.PHONY : backend.s

# target to generate assembly for a file
backend.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/backend.cpp.s
.PHONY : backend.cpp.s

backref.o: backref.cpp.o
.PHONY : backref.o

# target to build an object file
backref.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/backref.cpp.o
.PHONY : backref.cpp.o

backref.i: backref.cpp.i
.PHONY : backref.i

# target to preprocess a source file
backref.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/backref.cpp.i
.PHONY : backref.cpp.i

backref.s: backref.cpp.s
.PHONY : backref.s

# target to generate assembly for a file
backref.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/backref.cpp.s
.PHONY : backref.cpp.s

frontend.o: frontend.cpp.o
.PHONY : frontend.o

# target to build an object file
frontend.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/frontend.cpp.o
.PHONY : frontend.cpp.o

frontend.i: frontend.cpp.i
.PHONY : frontend.i

# target to preprocess a source file
frontend.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/frontend.cpp.i
.PHONY : frontend.cpp.i

frontend.s: frontend.cpp.s
.PHONY : frontend.s

# target to generate assembly for a file
frontend.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/frontend.cpp.s
.PHONY : frontend.cpp.s

large_objects.o: large_objects.cpp.o
.PHONY : large_objects.o

# target to build an object file
large_objects.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/large_objects.cpp.o
.PHONY : large_objects.cpp.o

large_objects.i: large_objects.cpp.i
.PHONY : large_objects.i

# target to preprocess a source file
large_objects.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/large_objects.cpp.i
.PHONY : large_objects.cpp.i

large_objects.s: large_objects.cpp.s
.PHONY : large_objects.s

# target to generate assembly for a file
large_objects.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/large_objects.cpp.s
.PHONY : large_objects.cpp.s

tbbmalloc.o: tbbmalloc.cpp.o
.PHONY : tbbmalloc.o

# target to build an object file
tbbmalloc.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/tbbmalloc.cpp.o
.PHONY : tbbmalloc.cpp.o

tbbmalloc.i: tbbmalloc.cpp.i
.PHONY : tbbmalloc.i

# target to preprocess a source file
tbbmalloc.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/tbbmalloc.cpp.i
.PHONY : tbbmalloc.cpp.i

tbbmalloc.s: tbbmalloc.cpp.s
.PHONY : tbbmalloc.s

# target to generate assembly for a file
tbbmalloc.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbbmalloc/CMakeFiles/tbbmalloc.dir/build.make src/tbbmalloc/CMakeFiles/tbbmalloc.dir/tbbmalloc.cpp.s
.PHONY : tbbmalloc.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... tbbmalloc"
	@echo "... __/tbb/itt_notify.o"
	@echo "... __/tbb/itt_notify.i"
	@echo "... __/tbb/itt_notify.s"
	@echo "... backend.o"
	@echo "... backend.i"
	@echo "... backend.s"
	@echo "... backref.o"
	@echo "... backref.i"
	@echo "... backref.s"
	@echo "... frontend.o"
	@echo "... frontend.i"
	@echo "... frontend.s"
	@echo "... large_objects.o"
	@echo "... large_objects.i"
	@echo "... large_objects.s"
	@echo "... tbbmalloc.o"
	@echo "... tbbmalloc.i"
	@echo "... tbbmalloc.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

