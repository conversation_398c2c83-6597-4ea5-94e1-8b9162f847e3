# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool..."
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && /Applications/CMake.app/Contents/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool for source..."
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && /Applications/CMake.app/Contents/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Applications/CMake.app/Contents/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Applications/CMake.app/Contents/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"devel\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Applications/CMake.app/Contents/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Applications/CMake.app/Contents/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb//CMakeFiles/progress.marks
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/tbb/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/tbb/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/tbb/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/tbb/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/tbb/CMakeFiles/tbb.dir/rule:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/tbb/CMakeFiles/tbb.dir/rule
.PHONY : src/tbb/CMakeFiles/tbb.dir/rule

# Convenience name for target.
tbb: src/tbb/CMakeFiles/tbb.dir/rule
.PHONY : tbb

# fast build rule for target.
tbb/fast:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/build
.PHONY : tbb/fast

address_waiter.o: address_waiter.cpp.o
.PHONY : address_waiter.o

# target to build an object file
address_waiter.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.o
.PHONY : address_waiter.cpp.o

address_waiter.i: address_waiter.cpp.i
.PHONY : address_waiter.i

# target to preprocess a source file
address_waiter.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.i
.PHONY : address_waiter.cpp.i

address_waiter.s: address_waiter.cpp.s
.PHONY : address_waiter.s

# target to generate assembly for a file
address_waiter.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.s
.PHONY : address_waiter.cpp.s

allocator.o: allocator.cpp.o
.PHONY : allocator.o

# target to build an object file
allocator.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/allocator.cpp.o
.PHONY : allocator.cpp.o

allocator.i: allocator.cpp.i
.PHONY : allocator.i

# target to preprocess a source file
allocator.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/allocator.cpp.i
.PHONY : allocator.cpp.i

allocator.s: allocator.cpp.s
.PHONY : allocator.s

# target to generate assembly for a file
allocator.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/allocator.cpp.s
.PHONY : allocator.cpp.s

arena.o: arena.cpp.o
.PHONY : arena.o

# target to build an object file
arena.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/arena.cpp.o
.PHONY : arena.cpp.o

arena.i: arena.cpp.i
.PHONY : arena.i

# target to preprocess a source file
arena.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/arena.cpp.i
.PHONY : arena.cpp.i

arena.s: arena.cpp.s
.PHONY : arena.s

# target to generate assembly for a file
arena.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/arena.cpp.s
.PHONY : arena.cpp.s

arena_slot.o: arena_slot.cpp.o
.PHONY : arena_slot.o

# target to build an object file
arena_slot.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.o
.PHONY : arena_slot.cpp.o

arena_slot.i: arena_slot.cpp.i
.PHONY : arena_slot.i

# target to preprocess a source file
arena_slot.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.i
.PHONY : arena_slot.cpp.i

arena_slot.s: arena_slot.cpp.s
.PHONY : arena_slot.s

# target to generate assembly for a file
arena_slot.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.s
.PHONY : arena_slot.cpp.s

concurrent_bounded_queue.o: concurrent_bounded_queue.cpp.o
.PHONY : concurrent_bounded_queue.o

# target to build an object file
concurrent_bounded_queue.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o
.PHONY : concurrent_bounded_queue.cpp.o

concurrent_bounded_queue.i: concurrent_bounded_queue.cpp.i
.PHONY : concurrent_bounded_queue.i

# target to preprocess a source file
concurrent_bounded_queue.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.i
.PHONY : concurrent_bounded_queue.cpp.i

concurrent_bounded_queue.s: concurrent_bounded_queue.cpp.s
.PHONY : concurrent_bounded_queue.s

# target to generate assembly for a file
concurrent_bounded_queue.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.s
.PHONY : concurrent_bounded_queue.cpp.s

dynamic_link.o: dynamic_link.cpp.o
.PHONY : dynamic_link.o

# target to build an object file
dynamic_link.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.o
.PHONY : dynamic_link.cpp.o

dynamic_link.i: dynamic_link.cpp.i
.PHONY : dynamic_link.i

# target to preprocess a source file
dynamic_link.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.i
.PHONY : dynamic_link.cpp.i

dynamic_link.s: dynamic_link.cpp.s
.PHONY : dynamic_link.s

# target to generate assembly for a file
dynamic_link.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.s
.PHONY : dynamic_link.cpp.s

exception.o: exception.cpp.o
.PHONY : exception.o

# target to build an object file
exception.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/exception.cpp.o
.PHONY : exception.cpp.o

exception.i: exception.cpp.i
.PHONY : exception.i

# target to preprocess a source file
exception.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/exception.cpp.i
.PHONY : exception.cpp.i

exception.s: exception.cpp.s
.PHONY : exception.s

# target to generate assembly for a file
exception.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/exception.cpp.s
.PHONY : exception.cpp.s

global_control.o: global_control.cpp.o
.PHONY : global_control.o

# target to build an object file
global_control.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/global_control.cpp.o
.PHONY : global_control.cpp.o

global_control.i: global_control.cpp.i
.PHONY : global_control.i

# target to preprocess a source file
global_control.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/global_control.cpp.i
.PHONY : global_control.cpp.i

global_control.s: global_control.cpp.s
.PHONY : global_control.s

# target to generate assembly for a file
global_control.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/global_control.cpp.s
.PHONY : global_control.cpp.s

governor.o: governor.cpp.o
.PHONY : governor.o

# target to build an object file
governor.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/governor.cpp.o
.PHONY : governor.cpp.o

governor.i: governor.cpp.i
.PHONY : governor.i

# target to preprocess a source file
governor.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/governor.cpp.i
.PHONY : governor.cpp.i

governor.s: governor.cpp.s
.PHONY : governor.s

# target to generate assembly for a file
governor.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/governor.cpp.s
.PHONY : governor.cpp.s

itt_notify.o: itt_notify.cpp.o
.PHONY : itt_notify.o

# target to build an object file
itt_notify.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.o
.PHONY : itt_notify.cpp.o

itt_notify.i: itt_notify.cpp.i
.PHONY : itt_notify.i

# target to preprocess a source file
itt_notify.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.i
.PHONY : itt_notify.cpp.i

itt_notify.s: itt_notify.cpp.s
.PHONY : itt_notify.s

# target to generate assembly for a file
itt_notify.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.s
.PHONY : itt_notify.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/main.cpp.s
.PHONY : main.cpp.s

market.o: market.cpp.o
.PHONY : market.o

# target to build an object file
market.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/market.cpp.o
.PHONY : market.cpp.o

market.i: market.cpp.i
.PHONY : market.i

# target to preprocess a source file
market.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/market.cpp.i
.PHONY : market.cpp.i

market.s: market.cpp.s
.PHONY : market.s

# target to generate assembly for a file
market.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/market.cpp.s
.PHONY : market.cpp.s

misc.o: misc.cpp.o
.PHONY : misc.o

# target to build an object file
misc.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/misc.cpp.o
.PHONY : misc.cpp.o

misc.i: misc.cpp.i
.PHONY : misc.i

# target to preprocess a source file
misc.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/misc.cpp.i
.PHONY : misc.cpp.i

misc.s: misc.cpp.s
.PHONY : misc.s

# target to generate assembly for a file
misc.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/misc.cpp.s
.PHONY : misc.cpp.s

misc_ex.o: misc_ex.cpp.o
.PHONY : misc_ex.o

# target to build an object file
misc_ex.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.o
.PHONY : misc_ex.cpp.o

misc_ex.i: misc_ex.cpp.i
.PHONY : misc_ex.i

# target to preprocess a source file
misc_ex.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.i
.PHONY : misc_ex.cpp.i

misc_ex.s: misc_ex.cpp.s
.PHONY : misc_ex.s

# target to generate assembly for a file
misc_ex.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.s
.PHONY : misc_ex.cpp.s

observer_proxy.o: observer_proxy.cpp.o
.PHONY : observer_proxy.o

# target to build an object file
observer_proxy.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.o
.PHONY : observer_proxy.cpp.o

observer_proxy.i: observer_proxy.cpp.i
.PHONY : observer_proxy.i

# target to preprocess a source file
observer_proxy.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.i
.PHONY : observer_proxy.cpp.i

observer_proxy.s: observer_proxy.cpp.s
.PHONY : observer_proxy.s

# target to generate assembly for a file
observer_proxy.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.s
.PHONY : observer_proxy.cpp.s

parallel_pipeline.o: parallel_pipeline.cpp.o
.PHONY : parallel_pipeline.o

# target to build an object file
parallel_pipeline.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.o
.PHONY : parallel_pipeline.cpp.o

parallel_pipeline.i: parallel_pipeline.cpp.i
.PHONY : parallel_pipeline.i

# target to preprocess a source file
parallel_pipeline.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.i
.PHONY : parallel_pipeline.cpp.i

parallel_pipeline.s: parallel_pipeline.cpp.s
.PHONY : parallel_pipeline.s

# target to generate assembly for a file
parallel_pipeline.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.s
.PHONY : parallel_pipeline.cpp.s

private_server.o: private_server.cpp.o
.PHONY : private_server.o

# target to build an object file
private_server.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/private_server.cpp.o
.PHONY : private_server.cpp.o

private_server.i: private_server.cpp.i
.PHONY : private_server.i

# target to preprocess a source file
private_server.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/private_server.cpp.i
.PHONY : private_server.cpp.i

private_server.s: private_server.cpp.s
.PHONY : private_server.s

# target to generate assembly for a file
private_server.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/private_server.cpp.s
.PHONY : private_server.cpp.s

profiling.o: profiling.cpp.o
.PHONY : profiling.o

# target to build an object file
profiling.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/profiling.cpp.o
.PHONY : profiling.cpp.o

profiling.i: profiling.cpp.i
.PHONY : profiling.i

# target to preprocess a source file
profiling.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/profiling.cpp.i
.PHONY : profiling.cpp.i

profiling.s: profiling.cpp.s
.PHONY : profiling.s

# target to generate assembly for a file
profiling.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/profiling.cpp.s
.PHONY : profiling.cpp.s

queuing_rw_mutex.o: queuing_rw_mutex.cpp.o
.PHONY : queuing_rw_mutex.o

# target to build an object file
queuing_rw_mutex.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o
.PHONY : queuing_rw_mutex.cpp.o

queuing_rw_mutex.i: queuing_rw_mutex.cpp.i
.PHONY : queuing_rw_mutex.i

# target to preprocess a source file
queuing_rw_mutex.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.i
.PHONY : queuing_rw_mutex.cpp.i

queuing_rw_mutex.s: queuing_rw_mutex.cpp.s
.PHONY : queuing_rw_mutex.s

# target to generate assembly for a file
queuing_rw_mutex.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.s
.PHONY : queuing_rw_mutex.cpp.s

rml_tbb.o: rml_tbb.cpp.o
.PHONY : rml_tbb.o

# target to build an object file
rml_tbb.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.o
.PHONY : rml_tbb.cpp.o

rml_tbb.i: rml_tbb.cpp.i
.PHONY : rml_tbb.i

# target to preprocess a source file
rml_tbb.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.i
.PHONY : rml_tbb.cpp.i

rml_tbb.s: rml_tbb.cpp.s
.PHONY : rml_tbb.s

# target to generate assembly for a file
rml_tbb.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.s
.PHONY : rml_tbb.cpp.s

rtm_mutex.o: rtm_mutex.cpp.o
.PHONY : rtm_mutex.o

# target to build an object file
rtm_mutex.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.o
.PHONY : rtm_mutex.cpp.o

rtm_mutex.i: rtm_mutex.cpp.i
.PHONY : rtm_mutex.i

# target to preprocess a source file
rtm_mutex.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.i
.PHONY : rtm_mutex.cpp.i

rtm_mutex.s: rtm_mutex.cpp.s
.PHONY : rtm_mutex.s

# target to generate assembly for a file
rtm_mutex.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.s
.PHONY : rtm_mutex.cpp.s

rtm_rw_mutex.o: rtm_rw_mutex.cpp.o
.PHONY : rtm_rw_mutex.o

# target to build an object file
rtm_rw_mutex.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o
.PHONY : rtm_rw_mutex.cpp.o

rtm_rw_mutex.i: rtm_rw_mutex.cpp.i
.PHONY : rtm_rw_mutex.i

# target to preprocess a source file
rtm_rw_mutex.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.i
.PHONY : rtm_rw_mutex.cpp.i

rtm_rw_mutex.s: rtm_rw_mutex.cpp.s
.PHONY : rtm_rw_mutex.s

# target to generate assembly for a file
rtm_rw_mutex.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.s
.PHONY : rtm_rw_mutex.cpp.s

semaphore.o: semaphore.cpp.o
.PHONY : semaphore.o

# target to build an object file
semaphore.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.o
.PHONY : semaphore.cpp.o

semaphore.i: semaphore.cpp.i
.PHONY : semaphore.i

# target to preprocess a source file
semaphore.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.i
.PHONY : semaphore.cpp.i

semaphore.s: semaphore.cpp.s
.PHONY : semaphore.s

# target to generate assembly for a file
semaphore.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.s
.PHONY : semaphore.cpp.s

small_object_pool.o: small_object_pool.cpp.o
.PHONY : small_object_pool.o

# target to build an object file
small_object_pool.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.o
.PHONY : small_object_pool.cpp.o

small_object_pool.i: small_object_pool.cpp.i
.PHONY : small_object_pool.i

# target to preprocess a source file
small_object_pool.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.i
.PHONY : small_object_pool.cpp.i

small_object_pool.s: small_object_pool.cpp.s
.PHONY : small_object_pool.s

# target to generate assembly for a file
small_object_pool.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.s
.PHONY : small_object_pool.cpp.s

task.o: task.cpp.o
.PHONY : task.o

# target to build an object file
task.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/task.cpp.o
.PHONY : task.cpp.o

task.i: task.cpp.i
.PHONY : task.i

# target to preprocess a source file
task.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/task.cpp.i
.PHONY : task.cpp.i

task.s: task.cpp.s
.PHONY : task.s

# target to generate assembly for a file
task.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/task.cpp.s
.PHONY : task.cpp.s

task_dispatcher.o: task_dispatcher.cpp.o
.PHONY : task_dispatcher.o

# target to build an object file
task_dispatcher.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.o
.PHONY : task_dispatcher.cpp.o

task_dispatcher.i: task_dispatcher.cpp.i
.PHONY : task_dispatcher.i

# target to preprocess a source file
task_dispatcher.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.i
.PHONY : task_dispatcher.cpp.i

task_dispatcher.s: task_dispatcher.cpp.s
.PHONY : task_dispatcher.s

# target to generate assembly for a file
task_dispatcher.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.s
.PHONY : task_dispatcher.cpp.s

task_group_context.o: task_group_context.cpp.o
.PHONY : task_group_context.o

# target to build an object file
task_group_context.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.o
.PHONY : task_group_context.cpp.o

task_group_context.i: task_group_context.cpp.i
.PHONY : task_group_context.i

# target to preprocess a source file
task_group_context.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.i
.PHONY : task_group_context.cpp.i

task_group_context.s: task_group_context.cpp.s
.PHONY : task_group_context.s

# target to generate assembly for a file
task_group_context.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.s
.PHONY : task_group_context.cpp.s

version.o: version.cpp.o
.PHONY : version.o

# target to build an object file
version.cpp.o:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/version.cpp.o
.PHONY : version.cpp.o

version.i: version.cpp.i
.PHONY : version.i

# target to preprocess a source file
version.cpp.i:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/version.cpp.i
.PHONY : version.cpp.i

version.s: version.cpp.s
.PHONY : version.s

# target to generate assembly for a file
version.cpp.s:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(MAKE) $(MAKESILENT) -f src/tbb/CMakeFiles/tbb.dir/build.make src/tbb/CMakeFiles/tbb.dir/version.cpp.s
.PHONY : version.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... tbb"
	@echo "... address_waiter.o"
	@echo "... address_waiter.i"
	@echo "... address_waiter.s"
	@echo "... allocator.o"
	@echo "... allocator.i"
	@echo "... allocator.s"
	@echo "... arena.o"
	@echo "... arena.i"
	@echo "... arena.s"
	@echo "... arena_slot.o"
	@echo "... arena_slot.i"
	@echo "... arena_slot.s"
	@echo "... concurrent_bounded_queue.o"
	@echo "... concurrent_bounded_queue.i"
	@echo "... concurrent_bounded_queue.s"
	@echo "... dynamic_link.o"
	@echo "... dynamic_link.i"
	@echo "... dynamic_link.s"
	@echo "... exception.o"
	@echo "... exception.i"
	@echo "... exception.s"
	@echo "... global_control.o"
	@echo "... global_control.i"
	@echo "... global_control.s"
	@echo "... governor.o"
	@echo "... governor.i"
	@echo "... governor.s"
	@echo "... itt_notify.o"
	@echo "... itt_notify.i"
	@echo "... itt_notify.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... market.o"
	@echo "... market.i"
	@echo "... market.s"
	@echo "... misc.o"
	@echo "... misc.i"
	@echo "... misc.s"
	@echo "... misc_ex.o"
	@echo "... misc_ex.i"
	@echo "... misc_ex.s"
	@echo "... observer_proxy.o"
	@echo "... observer_proxy.i"
	@echo "... observer_proxy.s"
	@echo "... parallel_pipeline.o"
	@echo "... parallel_pipeline.i"
	@echo "... parallel_pipeline.s"
	@echo "... private_server.o"
	@echo "... private_server.i"
	@echo "... private_server.s"
	@echo "... profiling.o"
	@echo "... profiling.i"
	@echo "... profiling.s"
	@echo "... queuing_rw_mutex.o"
	@echo "... queuing_rw_mutex.i"
	@echo "... queuing_rw_mutex.s"
	@echo "... rml_tbb.o"
	@echo "... rml_tbb.i"
	@echo "... rml_tbb.s"
	@echo "... rtm_mutex.o"
	@echo "... rtm_mutex.i"
	@echo "... rtm_mutex.s"
	@echo "... rtm_rw_mutex.o"
	@echo "... rtm_rw_mutex.i"
	@echo "... rtm_rw_mutex.s"
	@echo "... semaphore.o"
	@echo "... semaphore.i"
	@echo "... semaphore.s"
	@echo "... small_object_pool.o"
	@echo "... small_object_pool.i"
	@echo "... small_object_pool.s"
	@echo "... task.o"
	@echo "... task.i"
	@echo "... task.s"
	@echo "... task_dispatcher.o"
	@echo "... task_dispatcher.i"
	@echo "... task_dispatcher.s"
	@echo "... task_group_context.o"
	@echo "... task_group_context.i"
	@echo "... task_group_context.s"
	@echo "... version.o"
	@echo "... version.i"
	@echo "... version.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

