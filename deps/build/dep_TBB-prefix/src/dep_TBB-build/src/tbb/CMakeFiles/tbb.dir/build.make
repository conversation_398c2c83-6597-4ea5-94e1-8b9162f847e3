# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Applications/CMake.app/Contents/bin/cmake

# The command to remove a file.
RM = /Applications/CMake.app/Contents/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build

# Include any dependencies generated for this target.
include src/tbb/CMakeFiles/tbb.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/tbb/CMakeFiles/tbb.dir/compiler_depend.make

# Include the progress variables for this target.
include src/tbb/CMakeFiles/tbb.dir/progress.make

# Include the compile flags for this target's objects.
include src/tbb/CMakeFiles/tbb.dir/flags.make

src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/address_waiter.cpp
src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.o -MF CMakeFiles/tbb.dir/address_waiter.cpp.o.d -o CMakeFiles/tbb.dir/address_waiter.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/address_waiter.cpp

src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/address_waiter.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/address_waiter.cpp > CMakeFiles/tbb.dir/address_waiter.cpp.i

src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/address_waiter.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/address_waiter.cpp -o CMakeFiles/tbb.dir/address_waiter.cpp.s

src/tbb/CMakeFiles/tbb.dir/allocator.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/allocator.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/allocator.cpp
src/tbb/CMakeFiles/tbb.dir/allocator.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/tbb/CMakeFiles/tbb.dir/allocator.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/allocator.cpp.o -MF CMakeFiles/tbb.dir/allocator.cpp.o.d -o CMakeFiles/tbb.dir/allocator.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/allocator.cpp

src/tbb/CMakeFiles/tbb.dir/allocator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/allocator.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/allocator.cpp > CMakeFiles/tbb.dir/allocator.cpp.i

src/tbb/CMakeFiles/tbb.dir/allocator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/allocator.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/allocator.cpp -o CMakeFiles/tbb.dir/allocator.cpp.s

src/tbb/CMakeFiles/tbb.dir/arena.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/arena.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/arena.cpp
src/tbb/CMakeFiles/tbb.dir/arena.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/tbb/CMakeFiles/tbb.dir/arena.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/arena.cpp.o -MF CMakeFiles/tbb.dir/arena.cpp.o.d -o CMakeFiles/tbb.dir/arena.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/arena.cpp

src/tbb/CMakeFiles/tbb.dir/arena.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/arena.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/arena.cpp > CMakeFiles/tbb.dir/arena.cpp.i

src/tbb/CMakeFiles/tbb.dir/arena.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/arena.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/arena.cpp -o CMakeFiles/tbb.dir/arena.cpp.s

src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/arena_slot.cpp
src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.o -MF CMakeFiles/tbb.dir/arena_slot.cpp.o.d -o CMakeFiles/tbb.dir/arena_slot.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/arena_slot.cpp

src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/arena_slot.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/arena_slot.cpp > CMakeFiles/tbb.dir/arena_slot.cpp.i

src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/arena_slot.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/arena_slot.cpp -o CMakeFiles/tbb.dir/arena_slot.cpp.s

src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/concurrent_bounded_queue.cpp
src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o -MF CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o.d -o CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/concurrent_bounded_queue.cpp

src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/concurrent_bounded_queue.cpp > CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.i

src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/concurrent_bounded_queue.cpp -o CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.s

src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/dynamic_link.cpp
src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.o -MF CMakeFiles/tbb.dir/dynamic_link.cpp.o.d -o CMakeFiles/tbb.dir/dynamic_link.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/dynamic_link.cpp

src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/dynamic_link.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/dynamic_link.cpp > CMakeFiles/tbb.dir/dynamic_link.cpp.i

src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/dynamic_link.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/dynamic_link.cpp -o CMakeFiles/tbb.dir/dynamic_link.cpp.s

src/tbb/CMakeFiles/tbb.dir/exception.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/exception.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/exception.cpp
src/tbb/CMakeFiles/tbb.dir/exception.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object src/tbb/CMakeFiles/tbb.dir/exception.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/exception.cpp.o -MF CMakeFiles/tbb.dir/exception.cpp.o.d -o CMakeFiles/tbb.dir/exception.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/exception.cpp

src/tbb/CMakeFiles/tbb.dir/exception.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/exception.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/exception.cpp > CMakeFiles/tbb.dir/exception.cpp.i

src/tbb/CMakeFiles/tbb.dir/exception.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/exception.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/exception.cpp -o CMakeFiles/tbb.dir/exception.cpp.s

src/tbb/CMakeFiles/tbb.dir/governor.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/governor.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/governor.cpp
src/tbb/CMakeFiles/tbb.dir/governor.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object src/tbb/CMakeFiles/tbb.dir/governor.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/governor.cpp.o -MF CMakeFiles/tbb.dir/governor.cpp.o.d -o CMakeFiles/tbb.dir/governor.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/governor.cpp

src/tbb/CMakeFiles/tbb.dir/governor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/governor.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/governor.cpp > CMakeFiles/tbb.dir/governor.cpp.i

src/tbb/CMakeFiles/tbb.dir/governor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/governor.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/governor.cpp -o CMakeFiles/tbb.dir/governor.cpp.s

src/tbb/CMakeFiles/tbb.dir/global_control.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/global_control.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/global_control.cpp
src/tbb/CMakeFiles/tbb.dir/global_control.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object src/tbb/CMakeFiles/tbb.dir/global_control.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/global_control.cpp.o -MF CMakeFiles/tbb.dir/global_control.cpp.o.d -o CMakeFiles/tbb.dir/global_control.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/global_control.cpp

src/tbb/CMakeFiles/tbb.dir/global_control.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/global_control.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/global_control.cpp > CMakeFiles/tbb.dir/global_control.cpp.i

src/tbb/CMakeFiles/tbb.dir/global_control.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/global_control.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/global_control.cpp -o CMakeFiles/tbb.dir/global_control.cpp.s

src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/itt_notify.cpp
src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.o -MF CMakeFiles/tbb.dir/itt_notify.cpp.o.d -o CMakeFiles/tbb.dir/itt_notify.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/itt_notify.cpp

src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/itt_notify.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/itt_notify.cpp > CMakeFiles/tbb.dir/itt_notify.cpp.i

src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/itt_notify.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/itt_notify.cpp -o CMakeFiles/tbb.dir/itt_notify.cpp.s

src/tbb/CMakeFiles/tbb.dir/main.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/main.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/main.cpp
src/tbb/CMakeFiles/tbb.dir/main.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object src/tbb/CMakeFiles/tbb.dir/main.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/main.cpp.o -MF CMakeFiles/tbb.dir/main.cpp.o.d -o CMakeFiles/tbb.dir/main.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/main.cpp

src/tbb/CMakeFiles/tbb.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/main.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/main.cpp > CMakeFiles/tbb.dir/main.cpp.i

src/tbb/CMakeFiles/tbb.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/main.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/main.cpp -o CMakeFiles/tbb.dir/main.cpp.s

src/tbb/CMakeFiles/tbb.dir/market.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/market.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/market.cpp
src/tbb/CMakeFiles/tbb.dir/market.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object src/tbb/CMakeFiles/tbb.dir/market.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/market.cpp.o -MF CMakeFiles/tbb.dir/market.cpp.o.d -o CMakeFiles/tbb.dir/market.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/market.cpp

src/tbb/CMakeFiles/tbb.dir/market.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/market.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/market.cpp > CMakeFiles/tbb.dir/market.cpp.i

src/tbb/CMakeFiles/tbb.dir/market.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/market.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/market.cpp -o CMakeFiles/tbb.dir/market.cpp.s

src/tbb/CMakeFiles/tbb.dir/misc.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/misc.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/misc.cpp
src/tbb/CMakeFiles/tbb.dir/misc.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object src/tbb/CMakeFiles/tbb.dir/misc.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/misc.cpp.o -MF CMakeFiles/tbb.dir/misc.cpp.o.d -o CMakeFiles/tbb.dir/misc.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/misc.cpp

src/tbb/CMakeFiles/tbb.dir/misc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/misc.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/misc.cpp > CMakeFiles/tbb.dir/misc.cpp.i

src/tbb/CMakeFiles/tbb.dir/misc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/misc.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/misc.cpp -o CMakeFiles/tbb.dir/misc.cpp.s

src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/misc_ex.cpp
src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.o -MF CMakeFiles/tbb.dir/misc_ex.cpp.o.d -o CMakeFiles/tbb.dir/misc_ex.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/misc_ex.cpp

src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/misc_ex.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/misc_ex.cpp > CMakeFiles/tbb.dir/misc_ex.cpp.i

src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/misc_ex.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/misc_ex.cpp -o CMakeFiles/tbb.dir/misc_ex.cpp.s

src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/observer_proxy.cpp
src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.o -MF CMakeFiles/tbb.dir/observer_proxy.cpp.o.d -o CMakeFiles/tbb.dir/observer_proxy.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/observer_proxy.cpp

src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/observer_proxy.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/observer_proxy.cpp > CMakeFiles/tbb.dir/observer_proxy.cpp.i

src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/observer_proxy.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/observer_proxy.cpp -o CMakeFiles/tbb.dir/observer_proxy.cpp.s

src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/parallel_pipeline.cpp
src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.o -MF CMakeFiles/tbb.dir/parallel_pipeline.cpp.o.d -o CMakeFiles/tbb.dir/parallel_pipeline.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/parallel_pipeline.cpp

src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/parallel_pipeline.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/parallel_pipeline.cpp > CMakeFiles/tbb.dir/parallel_pipeline.cpp.i

src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/parallel_pipeline.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/parallel_pipeline.cpp -o CMakeFiles/tbb.dir/parallel_pipeline.cpp.s

src/tbb/CMakeFiles/tbb.dir/private_server.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/private_server.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/private_server.cpp
src/tbb/CMakeFiles/tbb.dir/private_server.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object src/tbb/CMakeFiles/tbb.dir/private_server.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/private_server.cpp.o -MF CMakeFiles/tbb.dir/private_server.cpp.o.d -o CMakeFiles/tbb.dir/private_server.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/private_server.cpp

src/tbb/CMakeFiles/tbb.dir/private_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/private_server.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/private_server.cpp > CMakeFiles/tbb.dir/private_server.cpp.i

src/tbb/CMakeFiles/tbb.dir/private_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/private_server.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/private_server.cpp -o CMakeFiles/tbb.dir/private_server.cpp.s

src/tbb/CMakeFiles/tbb.dir/profiling.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/profiling.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/profiling.cpp
src/tbb/CMakeFiles/tbb.dir/profiling.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object src/tbb/CMakeFiles/tbb.dir/profiling.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/profiling.cpp.o -MF CMakeFiles/tbb.dir/profiling.cpp.o.d -o CMakeFiles/tbb.dir/profiling.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/profiling.cpp

src/tbb/CMakeFiles/tbb.dir/profiling.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/profiling.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/profiling.cpp > CMakeFiles/tbb.dir/profiling.cpp.i

src/tbb/CMakeFiles/tbb.dir/profiling.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/profiling.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/profiling.cpp -o CMakeFiles/tbb.dir/profiling.cpp.s

src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rml_tbb.cpp
src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.o -MF CMakeFiles/tbb.dir/rml_tbb.cpp.o.d -o CMakeFiles/tbb.dir/rml_tbb.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rml_tbb.cpp

src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/rml_tbb.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rml_tbb.cpp > CMakeFiles/tbb.dir/rml_tbb.cpp.i

src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/rml_tbb.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rml_tbb.cpp -o CMakeFiles/tbb.dir/rml_tbb.cpp.s

src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rtm_mutex.cpp
src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.o -MF CMakeFiles/tbb.dir/rtm_mutex.cpp.o.d -o CMakeFiles/tbb.dir/rtm_mutex.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rtm_mutex.cpp

src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/rtm_mutex.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rtm_mutex.cpp > CMakeFiles/tbb.dir/rtm_mutex.cpp.i

src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/rtm_mutex.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rtm_mutex.cpp -o CMakeFiles/tbb.dir/rtm_mutex.cpp.s

src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rtm_rw_mutex.cpp
src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o -MF CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o.d -o CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rtm_rw_mutex.cpp

src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rtm_rw_mutex.cpp > CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.i

src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rtm_rw_mutex.cpp -o CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.s

src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/semaphore.cpp
src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.o -MF CMakeFiles/tbb.dir/semaphore.cpp.o.d -o CMakeFiles/tbb.dir/semaphore.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/semaphore.cpp

src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/semaphore.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/semaphore.cpp > CMakeFiles/tbb.dir/semaphore.cpp.i

src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/semaphore.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/semaphore.cpp -o CMakeFiles/tbb.dir/semaphore.cpp.s

src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/small_object_pool.cpp
src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.o -MF CMakeFiles/tbb.dir/small_object_pool.cpp.o.d -o CMakeFiles/tbb.dir/small_object_pool.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/small_object_pool.cpp

src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/small_object_pool.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/small_object_pool.cpp > CMakeFiles/tbb.dir/small_object_pool.cpp.i

src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/small_object_pool.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/small_object_pool.cpp -o CMakeFiles/tbb.dir/small_object_pool.cpp.s

src/tbb/CMakeFiles/tbb.dir/task.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/task.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task.cpp
src/tbb/CMakeFiles/tbb.dir/task.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object src/tbb/CMakeFiles/tbb.dir/task.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/task.cpp.o -MF CMakeFiles/tbb.dir/task.cpp.o.d -o CMakeFiles/tbb.dir/task.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task.cpp

src/tbb/CMakeFiles/tbb.dir/task.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/task.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task.cpp > CMakeFiles/tbb.dir/task.cpp.i

src/tbb/CMakeFiles/tbb.dir/task.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/task.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task.cpp -o CMakeFiles/tbb.dir/task.cpp.s

src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task_dispatcher.cpp
src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.o -MF CMakeFiles/tbb.dir/task_dispatcher.cpp.o.d -o CMakeFiles/tbb.dir/task_dispatcher.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task_dispatcher.cpp

src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/task_dispatcher.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task_dispatcher.cpp > CMakeFiles/tbb.dir/task_dispatcher.cpp.i

src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/task_dispatcher.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task_dispatcher.cpp -o CMakeFiles/tbb.dir/task_dispatcher.cpp.s

src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task_group_context.cpp
src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.o -MF CMakeFiles/tbb.dir/task_group_context.cpp.o.d -o CMakeFiles/tbb.dir/task_group_context.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task_group_context.cpp

src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/task_group_context.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task_group_context.cpp > CMakeFiles/tbb.dir/task_group_context.cpp.i

src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/task_group_context.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task_group_context.cpp -o CMakeFiles/tbb.dir/task_group_context.cpp.s

src/tbb/CMakeFiles/tbb.dir/version.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/version.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/version.cpp
src/tbb/CMakeFiles/tbb.dir/version.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object src/tbb/CMakeFiles/tbb.dir/version.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/version.cpp.o -MF CMakeFiles/tbb.dir/version.cpp.o.d -o CMakeFiles/tbb.dir/version.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/version.cpp

src/tbb/CMakeFiles/tbb.dir/version.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/version.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/version.cpp > CMakeFiles/tbb.dir/version.cpp.i

src/tbb/CMakeFiles/tbb.dir/version.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/version.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/version.cpp -o CMakeFiles/tbb.dir/version.cpp.s

src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o: src/tbb/CMakeFiles/tbb.dir/flags.make
src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/queuing_rw_mutex.cpp
src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o: src/tbb/CMakeFiles/tbb.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o -MF CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o.d -o CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/queuing_rw_mutex.cpp

src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.i"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/queuing_rw_mutex.cpp > CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.i

src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.s"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/queuing_rw_mutex.cpp -o CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.s

# Object files for target tbb
tbb_OBJECTS = \
"CMakeFiles/tbb.dir/address_waiter.cpp.o" \
"CMakeFiles/tbb.dir/allocator.cpp.o" \
"CMakeFiles/tbb.dir/arena.cpp.o" \
"CMakeFiles/tbb.dir/arena_slot.cpp.o" \
"CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o" \
"CMakeFiles/tbb.dir/dynamic_link.cpp.o" \
"CMakeFiles/tbb.dir/exception.cpp.o" \
"CMakeFiles/tbb.dir/governor.cpp.o" \
"CMakeFiles/tbb.dir/global_control.cpp.o" \
"CMakeFiles/tbb.dir/itt_notify.cpp.o" \
"CMakeFiles/tbb.dir/main.cpp.o" \
"CMakeFiles/tbb.dir/market.cpp.o" \
"CMakeFiles/tbb.dir/misc.cpp.o" \
"CMakeFiles/tbb.dir/misc_ex.cpp.o" \
"CMakeFiles/tbb.dir/observer_proxy.cpp.o" \
"CMakeFiles/tbb.dir/parallel_pipeline.cpp.o" \
"CMakeFiles/tbb.dir/private_server.cpp.o" \
"CMakeFiles/tbb.dir/profiling.cpp.o" \
"CMakeFiles/tbb.dir/rml_tbb.cpp.o" \
"CMakeFiles/tbb.dir/rtm_mutex.cpp.o" \
"CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o" \
"CMakeFiles/tbb.dir/semaphore.cpp.o" \
"CMakeFiles/tbb.dir/small_object_pool.cpp.o" \
"CMakeFiles/tbb.dir/task.cpp.o" \
"CMakeFiles/tbb.dir/task_dispatcher.cpp.o" \
"CMakeFiles/tbb.dir/task_group_context.cpp.o" \
"CMakeFiles/tbb.dir/version.cpp.o" \
"CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o"

# External object files for target tbb
tbb_EXTERNAL_OBJECTS =

appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/allocator.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/arena.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/exception.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/governor.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/global_control.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/main.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/market.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/misc.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/private_server.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/profiling.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/task.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/version.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/build.make
appleclang_17.0_cxx11_64_release/libtbb.a: /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/def/mac64-tbb.def
appleclang_17.0_cxx11_64_release/libtbb.a: src/tbb/CMakeFiles/tbb.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Linking CXX static library ../../appleclang_17.0_cxx11_64_release/libtbb.a"
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && $(CMAKE_COMMAND) -P CMakeFiles/tbb.dir/cmake_clean_target.cmake
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/tbb.dir/link.txt --verbose=$(VERBOSE)
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && /Applications/CMake.app/Contents/bin/cmake -DBINARY_DIR=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build -DSOURCE_DIR=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB -DBIN_PATH=/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/appleclang_17.0_cxx11_64_release -DVARS_TEMPLATE=mac/env/vars.sh.in -DVARS_NAME=vars.sh -DTBB_INSTALL_VARS=OFF -DTBB_CMAKE_INSTALL_LIBDIR=lib -P /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/integration/cmake/generate_vars.cmake

# Rule to build all files generated by this target.
src/tbb/CMakeFiles/tbb.dir/build: appleclang_17.0_cxx11_64_release/libtbb.a
.PHONY : src/tbb/CMakeFiles/tbb.dir/build

src/tbb/CMakeFiles/tbb.dir/clean:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb && $(CMAKE_COMMAND) -P CMakeFiles/tbb.dir/cmake_clean.cmake
.PHONY : src/tbb/CMakeFiles/tbb.dir/clean

src/tbb/CMakeFiles/tbb.dir/depend:
	cd /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb /Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB-build/src/tbb/CMakeFiles/tbb.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/tbb/CMakeFiles/tbb.dir/depend

