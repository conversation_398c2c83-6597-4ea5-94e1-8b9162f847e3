
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/address_waiter.cpp" "src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/address_waiter.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/allocator.cpp" "src/tbb/CMakeFiles/tbb.dir/allocator.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/allocator.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/arena.cpp" "src/tbb/CMakeFiles/tbb.dir/arena.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/arena.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/arena_slot.cpp" "src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/arena_slot.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/concurrent_bounded_queue.cpp" "src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/dynamic_link.cpp" "src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/dynamic_link.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/exception.cpp" "src/tbb/CMakeFiles/tbb.dir/exception.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/exception.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/global_control.cpp" "src/tbb/CMakeFiles/tbb.dir/global_control.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/global_control.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/governor.cpp" "src/tbb/CMakeFiles/tbb.dir/governor.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/governor.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/itt_notify.cpp" "src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/itt_notify.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/main.cpp" "src/tbb/CMakeFiles/tbb.dir/main.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/main.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/market.cpp" "src/tbb/CMakeFiles/tbb.dir/market.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/market.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/misc.cpp" "src/tbb/CMakeFiles/tbb.dir/misc.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/misc.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/misc_ex.cpp" "src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/misc_ex.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/observer_proxy.cpp" "src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/observer_proxy.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/parallel_pipeline.cpp" "src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/parallel_pipeline.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/private_server.cpp" "src/tbb/CMakeFiles/tbb.dir/private_server.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/private_server.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/profiling.cpp" "src/tbb/CMakeFiles/tbb.dir/profiling.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/profiling.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/queuing_rw_mutex.cpp" "src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rml_tbb.cpp" "src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/rml_tbb.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rtm_mutex.cpp" "src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/rtm_mutex.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/rtm_rw_mutex.cpp" "src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/semaphore.cpp" "src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/semaphore.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/small_object_pool.cpp" "src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/small_object_pool.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task.cpp" "src/tbb/CMakeFiles/tbb.dir/task.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/task.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task_dispatcher.cpp" "src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/task_dispatcher.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/task_group_context.cpp" "src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/task_group_context.cpp.o.d"
  "/Users/<USER>/Documents/augment-projects/BambuStudio/deps/build/dep_TBB-prefix/src/dep_TBB/src/tbb/version.cpp" "src/tbb/CMakeFiles/tbb.dir/version.cpp.o" "gcc" "src/tbb/CMakeFiles/tbb.dir/version.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
