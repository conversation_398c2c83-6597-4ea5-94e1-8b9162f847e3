/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar qc ../../appleclang_17.0_cxx11_64_release/libtbb.a CMakeFiles/tbb.dir/address_waiter.cpp.o CMakeFiles/tbb.dir/allocator.cpp.o CMakeFiles/tbb.dir/arena.cpp.o CMakeFiles/tbb.dir/arena_slot.cpp.o CMakeFiles/tbb.dir/concurrent_bounded_queue.cpp.o CMakeFiles/tbb.dir/dynamic_link.cpp.o CMakeFiles/tbb.dir/exception.cpp.o CMakeFiles/tbb.dir/governor.cpp.o CMakeFiles/tbb.dir/global_control.cpp.o CMakeFiles/tbb.dir/itt_notify.cpp.o CMakeFiles/tbb.dir/main.cpp.o CMakeFiles/tbb.dir/market.cpp.o CMakeFiles/tbb.dir/misc.cpp.o CMakeFiles/tbb.dir/misc_ex.cpp.o CMakeFiles/tbb.dir/observer_proxy.cpp.o CMakeFiles/tbb.dir/parallel_pipeline.cpp.o CMakeFiles/tbb.dir/private_server.cpp.o CMakeFiles/tbb.dir/profiling.cpp.o CMakeFiles/tbb.dir/rml_tbb.cpp.o CMakeFiles/tbb.dir/rtm_mutex.cpp.o CMakeFiles/tbb.dir/rtm_rw_mutex.cpp.o CMakeFiles/tbb.dir/semaphore.cpp.o CMakeFiles/tbb.dir/small_object_pool.cpp.o CMakeFiles/tbb.dir/task.cpp.o CMakeFiles/tbb.dir/task_dispatcher.cpp.o CMakeFiles/tbb.dir/task_group_context.cpp.o CMakeFiles/tbb.dir/version.cpp.o CMakeFiles/tbb.dir/queuing_rw_mutex.cpp.o
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib ../../appleclang_17.0_cxx11_64_release/libtbb.a
