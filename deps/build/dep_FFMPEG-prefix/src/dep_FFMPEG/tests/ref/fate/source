Files without standard license headers:
libavcodec/file_open.c
libavcodec/ilbcdata.h
libavcodec/ilbcdec.c
libavcodec/interplayacm.c
libavcodec/log2_tab.c
libavcodec/reverse.c
libavdevice/file_open.c
libavdevice/reverse.c
libavfilter/af_arnndn.c
libavfilter/file_open.c
libavfilter/log2_tab.c
libavformat/bitstream.c
libavformat/file_open.c
libavformat/golomb_tab.c
libavformat/log2_tab.c
libavformat/rangecoder_dec.c
libswresample/log2_tab.c
libswscale/log2_tab.c
tools/uncoded_frame.c
tools/yuvcmp.c
Headers without standard inclusion guards:
compat/djgpp/math.h
compat/float/float.h
compat/float/limits.h
libavcodec/bitstream_template.h
tools/decode_simple.h
Use of av_clip() where av_clip_uintp2() could be used:
Use of av_clip() where av_clip_intp2() could be used:
