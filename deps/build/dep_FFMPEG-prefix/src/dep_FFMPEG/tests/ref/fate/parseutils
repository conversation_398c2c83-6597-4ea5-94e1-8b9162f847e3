Testing av_parse_video_rate()
'-inf' -> -1/0 ERROR
'inf' -> 1/0 ERROR
'nan' -> 0/0 ERROR
'123/0' -> 1/0 ERROR
'-123 / 0' -> -1/0 ERROR
'' -> 0/0 ERROR
'/' -> 0/0 ERROR
' 123  /  321' -> 41/107 OK
'foo/foo' -> 0/0 ERROR
'foo/1' -> 0/0 ERROR
'1/foo' -> 1/0 ERROR
'0/0' -> 0/0 ERROR
'/0' -> 0/0 ERROR
'1/' -> 1/0 ERROR
'1' -> 1/1 OK
'0' -> 0/1 ERROR
'-123/123' -> -1/1 ERROR
'-foo' -> 0/0 ERROR
'123.23' -> 12323/100 OK
'.23' -> 23/100 OK
'-.23' -> -23/100 ERROR
'-0.234' -> -117/500 ERROR
'-0.0000001' -> -1/10000000 ERROR
'  21332.2324   ' -> 917286/43 OK
' -21332.2324   ' -> -917286/43 ERROR

Testing av_parse_color()
bikeshed -> R(80) G(64) B(140) A(59)
RaNdOm -> R(185) G(88) B(148) A(94)
foo -> error
red -> R(255) G(0) B(0) A(255)
Red  -> error
RED -> R(255) G(0) B(0) A(255)
Violet -> R(238) G(130) B(238) A(255)
Yellow -> R(255) G(255) B(0) A(255)
Red -> R(255) G(0) B(0) A(255)
0x000000 -> R(0) G(0) B(0) A(255)
0x0000000 -> error
0xff000000 -> R(255) G(0) B(0) A(0)
0x3e34ff -> R(62) G(52) B(255) A(255)
0x3e34ffaa -> R(62) G(52) B(255) A(170)
0xffXXee -> error
0xfoobar -> error
0xffffeeeeeeee -> error
#ff0000 -> R(255) G(0) B(0) A(255)
#ffXX00 -> error
ff0000 -> R(255) G(0) B(0) A(255)
ffXX00 -> error
red@foo -> error
random@10 -> error
0xff0000@1.0 -> R(255) G(0) B(0) A(255)
red@ -> error
red@0xfff -> error
red@0xf -> R(255) G(0) B(0) A(15)
red@2 -> error
red@0.1 -> R(255) G(0) B(0) A(25)
red@-1 -> error
red@0.5 -> R(255) G(0) B(0) A(127)
red@1.0 -> R(255) G(0) B(0) A(255)
red@256 -> error
red@10foo -> error
red@-1.0 -> error
red@-0.0 -> R(255) G(0) B(0) A(0)

Testing av_small_strptime()
fmt:'%Y-%m-%d' spec:'2012-12-21' -> 2012-12-21 00:00:00
fmt:'%Y - %m - %d' spec:'2012-12-21' -> 2012-12-21 00:00:00
fmt:'%Y-%m-%d %H:%M:%S' spec:'2012-12-21 20:12:21' -> 2012-12-21 20:12:21
fmt:'  %Y - %m - %d %H : %M : %S' spec:'   2012 - 12 -  21   20 : 12 : 21' -> 2012-12-21 20:12:21
fmt:'  %Y - %b - %d %H : %M : %S' spec:'   2012 - nOV -  21   20 : 12 : 21' -> 2012-11-21 20:12:21
fmt:'  %Y - %B - %d %H : %M : %S' spec:'   2012 - nOVemBeR -  21   20 : 12 : 21' -> 2012-11-21 20:12:21
fmt:'  %Y - %B%d %H : %M : %S' spec:'   2012 - may21   20 : 12 : 21' -> 2012-05-21 20:12:21
fmt:'  %Y - %B%d %H : %M : %S' spec:'   2012 - mby21   20 : 12 : 21' -> error
fmt:'  %Y - %B - %d %H : %M : %S' spec:'   2012 - JunE -  21   20 : 12 : 21' -> 2012-06-21 20:12:21
fmt:'  %Y - %B - %d %H : %M : %S' spec:'   2012 - Jane -  21   20 : 12 : 21' -> error
fmt:'  %Y - %B - %d %H : %M : %S' spec:'   2012 - January -  21   20 : 12 : 21' -> 2012-01-21 20:12:21

Testing av_parse_time()
(now is 2012-03-17 09:14:13.2 +0100, local time is UTC+1)
now                      ->     1331972053.200000 = 2012-03-17T08:14:13Z
12:35:46                 ->     1331984146.000000 = 2012-03-17T11:35:46Z
2000-12-20 0:02:47.5z    ->      977270567.500000 = 2000-12-20T00:02:47Z
2012 - 02-22  17:44:07   ->     1329929047.000000 = 2012-02-22T16:44:07Z
2000-12-20T010247.6      ->      977270567.600000 = 2000-12-20T00:02:47Z
2000-12-12 1:35:46+05:30 ->      976565146.000000 = 2000-12-11T20:05:46Z
2002-12-12 22:30:40-02   ->     1039739440.000000 = 2002-12-13T00:30:40Z
2:34:56.79               ->           +9296790000
-1:23:45.67              ->           -5025670000
42.1729                  ->             +42172900
-1729.42                 ->           -1729420000
12:34                    ->            +754000000
2147483648s              ->     +2147483648000000
4294967296ms             ->        +4294967296000
8589934592us             ->           +8589934592
9223372036854775808us    -> error

Testing av_get_known_color_name()
AliceBlue -> R(240) G(248) B(255) A(0)
AntiqueWhite -> R(250) G(235) B(215) A(0)
Aqua -> R(0) G(255) B(255) A(0)
Aquamarine -> R(127) G(255) B(212) A(0)
Azure -> R(240) G(255) B(255) A(0)
Beige -> R(245) G(245) B(220) A(0)
Bisque -> R(255) G(228) B(196) A(0)
Black -> R(0) G(0) B(0) A(0)
BlanchedAlmond -> R(255) G(235) B(205) A(0)
Blue -> R(0) G(0) B(255) A(0)
BlueViolet -> R(138) G(43) B(226) A(0)
Brown -> R(165) G(42) B(42) A(0)
BurlyWood -> R(222) G(184) B(135) A(0)
CadetBlue -> R(95) G(158) B(160) A(0)
Chartreuse -> R(127) G(255) B(0) A(0)
Chocolate -> R(210) G(105) B(30) A(0)
Coral -> R(255) G(127) B(80) A(0)
CornflowerBlue -> R(100) G(149) B(237) A(0)
Cornsilk -> R(255) G(248) B(220) A(0)
Crimson -> R(220) G(20) B(60) A(0)
Cyan -> R(0) G(255) B(255) A(0)
DarkBlue -> R(0) G(0) B(139) A(0)
DarkCyan -> R(0) G(139) B(139) A(0)
DarkGoldenRod -> R(184) G(134) B(11) A(0)
DarkGray -> R(169) G(169) B(169) A(0)
DarkGreen -> R(0) G(100) B(0) A(0)
DarkKhaki -> R(189) G(183) B(107) A(0)
DarkMagenta -> R(139) G(0) B(139) A(0)
DarkOliveGreen -> R(85) G(107) B(47) A(0)
Darkorange -> R(255) G(140) B(0) A(0)
DarkOrchid -> R(153) G(50) B(204) A(0)
DarkRed -> R(139) G(0) B(0) A(0)
DarkSalmon -> R(233) G(150) B(122) A(0)
DarkSeaGreen -> R(143) G(188) B(143) A(0)
DarkSlateBlue -> R(72) G(61) B(139) A(0)
DarkSlateGray -> R(47) G(79) B(79) A(0)
DarkTurquoise -> R(0) G(206) B(209) A(0)
DarkViolet -> R(148) G(0) B(211) A(0)
DeepPink -> R(255) G(20) B(147) A(0)
DeepSkyBlue -> R(0) G(191) B(255) A(0)
DimGray -> R(105) G(105) B(105) A(0)
DodgerBlue -> R(30) G(144) B(255) A(0)
FireBrick -> R(178) G(34) B(34) A(0)
FloralWhite -> R(255) G(250) B(240) A(0)
ForestGreen -> R(34) G(139) B(34) A(0)
Fuchsia -> R(255) G(0) B(255) A(0)
Gainsboro -> R(220) G(220) B(220) A(0)
GhostWhite -> R(248) G(248) B(255) A(0)
Gold -> R(255) G(215) B(0) A(0)
GoldenRod -> R(218) G(165) B(32) A(0)
Gray -> R(128) G(128) B(128) A(0)
Green -> R(0) G(128) B(0) A(0)
GreenYellow -> R(173) G(255) B(47) A(0)
HoneyDew -> R(240) G(255) B(240) A(0)
HotPink -> R(255) G(105) B(180) A(0)
IndianRed -> R(205) G(92) B(92) A(0)
Indigo -> R(75) G(0) B(130) A(0)
Ivory -> R(255) G(255) B(240) A(0)
Khaki -> R(240) G(230) B(140) A(0)
Lavender -> R(230) G(230) B(250) A(0)
LavenderBlush -> R(255) G(240) B(245) A(0)
LawnGreen -> R(124) G(252) B(0) A(0)
LemonChiffon -> R(255) G(250) B(205) A(0)
LightBlue -> R(173) G(216) B(230) A(0)
LightCoral -> R(240) G(128) B(128) A(0)
LightCyan -> R(224) G(255) B(255) A(0)
LightGoldenRodYellow -> R(250) G(250) B(210) A(0)
LightGreen -> R(144) G(238) B(144) A(0)
LightGrey -> R(211) G(211) B(211) A(0)
LightPink -> R(255) G(182) B(193) A(0)
LightSalmon -> R(255) G(160) B(122) A(0)
LightSeaGreen -> R(32) G(178) B(170) A(0)
LightSkyBlue -> R(135) G(206) B(250) A(0)
LightSlateGray -> R(119) G(136) B(153) A(0)
LightSteelBlue -> R(176) G(196) B(222) A(0)
LightYellow -> R(255) G(255) B(224) A(0)
Lime -> R(0) G(255) B(0) A(0)
LimeGreen -> R(50) G(205) B(50) A(0)
Linen -> R(250) G(240) B(230) A(0)
Magenta -> R(255) G(0) B(255) A(0)
Maroon -> R(128) G(0) B(0) A(0)
MediumAquaMarine -> R(102) G(205) B(170) A(0)
MediumBlue -> R(0) G(0) B(205) A(0)
MediumOrchid -> R(186) G(85) B(211) A(0)
MediumPurple -> R(147) G(112) B(216) A(0)
MediumSeaGreen -> R(60) G(179) B(113) A(0)
MediumSlateBlue -> R(123) G(104) B(238) A(0)
MediumSpringGreen -> R(0) G(250) B(154) A(0)
MediumTurquoise -> R(72) G(209) B(204) A(0)
MediumVioletRed -> R(199) G(21) B(133) A(0)
MidnightBlue -> R(25) G(25) B(112) A(0)
MintCream -> R(245) G(255) B(250) A(0)
MistyRose -> R(255) G(228) B(225) A(0)
Moccasin -> R(255) G(228) B(181) A(0)
NavajoWhite -> R(255) G(222) B(173) A(0)
Navy -> R(0) G(0) B(128) A(0)
OldLace -> R(253) G(245) B(230) A(0)
Olive -> R(128) G(128) B(0) A(0)
OliveDrab -> R(107) G(142) B(35) A(0)
Orange -> R(255) G(165) B(0) A(0)
OrangeRed -> R(255) G(69) B(0) A(0)
Orchid -> R(218) G(112) B(214) A(0)
PaleGoldenRod -> R(238) G(232) B(170) A(0)
PaleGreen -> R(152) G(251) B(152) A(0)
PaleTurquoise -> R(175) G(238) B(238) A(0)
PaleVioletRed -> R(216) G(112) B(147) A(0)
PapayaWhip -> R(255) G(239) B(213) A(0)
PeachPuff -> R(255) G(218) B(185) A(0)
Peru -> R(205) G(133) B(63) A(0)
Pink -> R(255) G(192) B(203) A(0)
Plum -> R(221) G(160) B(221) A(0)
PowderBlue -> R(176) G(224) B(230) A(0)
Purple -> R(128) G(0) B(128) A(0)
Red -> R(255) G(0) B(0) A(0)
RosyBrown -> R(188) G(143) B(143) A(0)
RoyalBlue -> R(65) G(105) B(225) A(0)
SaddleBrown -> R(139) G(69) B(19) A(0)
Salmon -> R(250) G(128) B(114) A(0)
SandyBrown -> R(244) G(164) B(96) A(0)
SeaGreen -> R(46) G(139) B(87) A(0)
SeaShell -> R(255) G(245) B(238) A(0)
Sienna -> R(160) G(82) B(45) A(0)
Silver -> R(192) G(192) B(192) A(0)
SkyBlue -> R(135) G(206) B(235) A(0)
SlateBlue -> R(106) G(90) B(205) A(0)
SlateGray -> R(112) G(128) B(144) A(0)
Snow -> R(255) G(250) B(250) A(0)
SpringGreen -> R(0) G(255) B(127) A(0)
SteelBlue -> R(70) G(130) B(180) A(0)
Tan -> R(210) G(180) B(140) A(0)
Teal -> R(0) G(128) B(128) A(0)
Thistle -> R(216) G(191) B(216) A(0)
Tomato -> R(255) G(99) B(71) A(0)
Turquoise -> R(64) G(224) B(208) A(0)
Violet -> R(238) G(130) B(238) A(0)
Wheat -> R(245) G(222) B(179) A(0)
White -> R(255) G(255) B(255) A(0)
WhiteSmoke -> R(245) G(245) B(245) A(0)
Yellow -> R(255) G(255) B(0) A(0)
YellowGreen -> R(154) G(205) B(50) A(0)

Testing av_find_info_tag()
0. tag1 found: val1
1. tag2 found: val2
2. tag3 found: val3
3. tag4 not found
4. tag41 found: value 41
5. 41 not found
6. random1 not found
