Testing default values
num=0
toggle=1
string=default
escape=\=,
flags=1
rational=1/1
video_rate=25/1
width=200 height=300
pix_fmt=0bgr
sample_fmt=s16
duration=1000
color=255 192 203 255
channel_layout=311=311
binary=62 69 6e 0
binary_size=4
num64=1
flt=0.333333
dbl=0.333333
array_str[0]=str0
array_str[1]=str|1
array_str[2]=str\2
array_dict[0]: k00	v\00
array_dict[0]: k01	v,01
array_dict[1]: k10	v=1:0
TestContext AVOptions:
  -num               <int>        E.......... set num (from 0 to 100) (default 0)
  -toggle            <int>        E.......... set toggle (from 0 to 1) (default 1)
  -rational          <rational>   E.......... set rational (from 0 to 10) (default 1/1)
  -string            <string>     E.......... set string (default "default")
  -escape            <string>     E.......... set escape str (default "\=,")
  -flags             <flags>      E.......... set flags (default cool)
     cool                         E.......... set cool flag
     lame                         E.......... set lame flag
     mu                           E.......... set mu flag
  -size              <image_size> E.......... set size (default "200x300")
  -pix_fmt           <pix_fmt>    E.......... set pixfmt (default 0bgr)
  -sample_fmt        <sample_fmt> E.......... set samplefmt (default s16)
  -video_rate        <video_rate> E.......... set videorate (default "25")
  -duration          <duration>   E.......... set duration (default 0.001)
  -color             <color>      E.......... set color (default "pink")
  -cl                <channel_layout> E.......... set channel layout (default "hexagonal")
  -bin               <binary>     E.......... set binary value
  -bin1              <binary>     E.......... set binary value
  -bin2              <binary>     E.......... set binary value
  -num64             <int64>      E.......... set num 64bit (from 0 to 100) (default 1)
  -flt               <float>      E.......... set float (from 0 to 100) (default 0.333333)
  -dbl               <double>     E.......... set double (from 0 to 100) (default 0.333333)
  -bool1             <boolean>    E.......... set boolean value (default auto)
  -bool2             <boolean>    E.......... set boolean value (default true)
  -bool3             <boolean>    E.......... set boolean value (default false)
  -dict1             <dictionary> E.......... set dictionary value
  -dict2             <dictionary> E.......... set dictionary value (default "happy=':-)'")
  -array_int         [<int>     ].........T. array of ints
  -array_str         [<string>  ].........T. array of strings (default str0|str\|1|str\\2)
  -array_dict        [<dictionary>].........T. array of dicts (default k00=v\\\\00:k01=v\,01,k10=v\\=1\\:0)

Testing av_opt_is_set_to_default()
name:       num default:1 error:
name:    toggle default:0 error:
name:  rational default:0 error:
name:    string default:0 error:
name:    escape default:0 error:
name:     flags default:0 error:
name:      cool default:1 error:Option not found
name:      lame default:1 error:Option not found
name:        mu default:1 error:Option not found
name:      size default:0 error:
name:   pix_fmt default:0 error:
name:sample_fmt default:0 error:
name:video_rate default:0 error:
name:  duration default:0 error:
name:     color default:0 error:
name:        cl default:0 error:
name:       bin default:0 error:
name:      bin1 default:1 error:
name:      bin2 default:1 error:
name:     num64 default:0 error:
name:       flt default:0 error:
name:       dbl default:0 error:
name:     bool1 default:0 error:
name:     bool2 default:0 error:
name:     bool3 default:1 error:
name:     dict1 default:1 error:
name:     dict2 default:0 error:
name: array_int default:0 error:
name: array_str default:0 error:
name:array_dict default:0 error:
name:       num default:1 error:
name:    toggle default:1 error:
name:  rational default:1 error:
name:    string default:1 error:
name:    escape default:1 error:
name:     flags default:1 error:
name:      cool default:1 error:Option not found
name:      lame default:1 error:Option not found
name:        mu default:1 error:Option not found
name:      size default:1 error:
name:   pix_fmt default:1 error:
name:sample_fmt default:1 error:
name:video_rate default:1 error:
name:  duration default:1 error:
name:     color default:1 error:
name:        cl default:1 error:
name:       bin default:1 error:
name:      bin1 default:1 error:
name:      bin2 default:1 error:
name:     num64 default:1 error:
name:       flt default:1 error:
name:       dbl default:1 error:
name:     bool1 default:1 error:
name:     bool2 default:1 error:
name:     bool3 default:1 error:
name:     dict1 default:1 error:
name:     dict2 default:1 error:
name: array_int default:0 error:
name: array_str default:1 error:
name:array_dict default:1 error:

Testing av_opt_get/av_opt_set()
name: num         get: 0                set: OK               get: 0                OK
name: toggle      get: 1                set: OK               get: 1                OK
name: rational    get: 1/1              set: OK               get: 1/1              OK
name: string      get: default          set: OK               get: default          OK
name: escape      get: \=,              set: OK               get: \=,              OK
name: flags       get: 0x00000001       set: OK               get: 0x00000001       OK
name: size        get: 200x300          set: OK               get: 200x300          OK
name: pix_fmt     get: 0bgr             set: OK               get: 0bgr             OK
name: sample_fmt  get: s16              set: OK               get: s16              OK
name: video_rate  get: 25/1             set: OK               get: 25/1             OK
name: duration    get: 0.001            set: OK               get: 0.001            OK
name: color       get: 0xffc0cbff       set: OK               get: 0xffc0cbff       OK
name: cl          get: hexagonal        set: OK               get: hexagonal        OK
name: bin         get: 62696E00         set: OK               get: 62696E00         OK
name: bin1        get:                  set: OK               get:                  OK
name: bin2        get:                  set: OK               get:                  OK
name: num64       get: 1                set: OK               get: 1                OK
name: flt         get: 0.333333         set: OK               get: 0.333333         OK
name: dbl         get: 0.333333         set: OK               get: 0.333333         OK
name: bool1       get: auto             set: OK               get: auto             OK
name: bool2       get: true             set: OK               get: true             OK
name: bool3       get: false            set: OK               get: false            OK
name: dict1       get:                  set: OK               get:                  OK
name: dict2       get: happy=\:-)       set: OK               get: happy=\:-)       OK
name: array_int   get:                  set: OK               get:                  OK
name: array_str   get: str0|str\|1|str\\2 set: OK               get: str0|str\|1|str\\2 OK
name: array_dict  get: k00=v\\\\00:k01=v\,01,k10=v\\=1\\:0 set: OK               get: k00=v\\\\00:k01=v\,01,k10=v\\=1\\:0 OK
av_opt_set("array_dict", NULL) -> 0
array_dict=NULL; nb_array_dict=0
av_opt_get("array_dict") -> NULL

Test av_opt_serialize()
num=0,toggle=1,rational=1/1,string=default,escape=\\\=\,,flags=0x00000001,size=200x300,pix_fmt=0bgr,sample_fmt=s16,video_rate=25/1,duration=0.001,color=0xffc0cbff,cl=hexagonal,bin=62696E00,bin1=,bin2=,num64=1,flt=0.333333,dbl=0.333333,bool1=auto,bool2=true,bool3=false,dict1=,dict2=happy\=\\:-),array_int=,array_str=str0|str\\|1|str\\\\2,array_dict=k00\=v\\\\\\\\00:k01\=v\\\,01\,k10\=v\\\\\=1\\\\:0
Setting entry with key 'num' to value '0'
Setting entry with key 'toggle' to value '1'
Setting entry with key 'rational' to value '1/1'
Setting entry with key 'string' to value 'default'
Setting entry with key 'escape' to value '\=,'
Setting entry with key 'flags' to value '0x00000001'
Setting entry with key 'size' to value '200x300'
Setting entry with key 'pix_fmt' to value '0bgr'
Setting entry with key 'sample_fmt' to value 's16'
Setting entry with key 'video_rate' to value '25/1'
Setting entry with key 'duration' to value '0.001'
Setting entry with key 'color' to value '0xffc0cbff'
Setting entry with key 'cl' to value 'hexagonal'
Setting entry with key 'bin' to value '62696E00'
Setting entry with key 'bin1' to value ''
Setting entry with key 'bin2' to value ''
Setting entry with key 'num64' to value '1'
Setting entry with key 'flt' to value '0.333333'
Setting entry with key 'dbl' to value '0.333333'
Setting entry with key 'bool1' to value 'auto'
Setting entry with key 'bool2' to value 'true'
Setting entry with key 'bool3' to value 'false'
Setting entry with key 'dict1' to value ''
Setting entry with key 'dict2' to value 'happy=\:-)'
Setting entry with key 'array_int' to value ''
Setting entry with key 'array_str' to value 'str0|str\|1|str\\2'
Setting entry with key 'array_dict' to value 'k00=v\\\\00:k01=v\,01,k10=v\\=1\\:0'
num=0,toggle=1,rational=1/1,string=default,escape=\\\=\,,flags=0x00000001,size=200x300,pix_fmt=0bgr,sample_fmt=s16,video_rate=25/1,duration=0.001,color=0xffc0cbff,cl=hexagonal,bin=62696E00,bin1=,bin2=,num64=1,flt=0.333333,dbl=0.333333,bool1=auto,bool2=true,bool3=false,dict1=,dict2=happy\=\\:-),array_int=,array_str=str0|str\\|1|str\\\\2,array_dict=k00\=v\\\\\\\\00:k01\=v\\\,01\,k10\=v\\\\\=1\\\\:0

Testing av_set_options_string()
Setting options string ''
OK    ''
Setting options string ':'
Missing key or no key/value separator found after key ':'
Error ':'
Setting options string '='
Missing key or no key/value separator found after key ''
Error '='
Setting options string 'foo=:'
Setting entry with key 'foo' to value ''
Key 'foo' not found.
Error 'foo=:'
Setting options string ':=foo'
Setting entry with key ':' to value 'foo'
Key ':' not found.
Error ':=foo'
Setting options string '=foo'
Missing key or no key/value separator found after key ''
Error '=foo'
Setting options string 'foo='
Setting entry with key 'foo' to value ''
Key 'foo' not found.
Error 'foo='
Setting options string 'foo'
Missing key or no key/value separator found after key 'foo'
Error 'foo'
Setting options string 'foo=val'
Setting entry with key 'foo' to value 'val'
Key 'foo' not found.
Error 'foo=val'
Setting options string 'foo==val'
Setting entry with key 'foo' to value '=val'
Key 'foo' not found.
Error 'foo==val'
Setting options string 'toggle=:'
Setting entry with key 'toggle' to value ''
Undefined constant or missing '(' in ''
Unable to parse option value ""
Error 'toggle=:'
Setting options string 'string=:'
Setting entry with key 'string' to value ''
OK    'string=:'
Setting options string 'toggle=1 : foo'
Setting entry with key 'toggle' to value '1'
Missing key or no key/value separator found after key 'foo'
Error 'toggle=1 : foo'
Setting options string 'toggle=100'
Setting entry with key 'toggle' to value '100'
Value 100.000000 for parameter 'toggle' out of range [0 - 1]
Error 'toggle=100'
Setting options string 'toggle==1'
Setting entry with key 'toggle' to value '=1'
Undefined constant or missing '(' in '=1'
Unable to parse option value "=1"
Error 'toggle==1'
Setting options string 'flags=+mu-lame : num=42: toggle=0'
Setting entry with key 'flags' to value '+mu-lame'
Setting entry with key 'num' to value '42'
Setting entry with key 'toggle' to value '0'
OK    'flags=+mu-lame : num=42: toggle=0'
Setting options string 'num=42 : string=blahblah'
Setting entry with key 'num' to value '42'
Setting entry with key 'string' to value 'blahblah'
OK    'num=42 : string=blahblah'
Setting options string 'rational=0 : rational=1/2 : rational=1/-1'
Setting entry with key 'rational' to value '0'
Setting entry with key 'rational' to value '1/2'
Setting entry with key 'rational' to value '1/-1'
Value -1.000000 for parameter 'rational' out of range [0 - 10]
Value -1.000000 for parameter 'rational' out of range [0 - 10]
Error 'rational=0 : rational=1/2 : rational=1/-1'
Setting options string 'rational=-1/0'
Error 'rational=-1/0'
Setting options string 'size=1024x768'
Setting entry with key 'size' to value '1024x768'
OK    'size=1024x768'
Setting options string 'size=pal'
Setting entry with key 'size' to value 'pal'
OK    'size=pal'
Setting options string 'size=bogus'
Setting entry with key 'size' to value 'bogus'
Unable to parse option value "bogus" as image size
Error 'size=bogus'
Setting options string 'pix_fmt=yuv420p'
Setting entry with key 'pix_fmt' to value 'yuv420p'
OK    'pix_fmt=yuv420p'
Setting options string 'pix_fmt=2'
Setting entry with key 'pix_fmt' to value '2'
OK    'pix_fmt=2'
Setting options string 'pix_fmt=bogus'
Setting entry with key 'pix_fmt' to value 'bogus'
Unable to parse option value "bogus" as pixel format
Error 'pix_fmt=bogus'
Setting options string 'sample_fmt=s16'
Setting entry with key 'sample_fmt' to value 's16'
OK    'sample_fmt=s16'
Setting options string 'sample_fmt=2'
Setting entry with key 'sample_fmt' to value '2'
OK    'sample_fmt=2'
Setting options string 'sample_fmt=bogus'
Setting entry with key 'sample_fmt' to value 'bogus'
Unable to parse option value "bogus" as sample format
Error 'sample_fmt=bogus'
Setting options string 'video_rate=pal'
Setting entry with key 'video_rate' to value 'pal'
OK    'video_rate=pal'
Setting options string 'video_rate=25'
Setting entry with key 'video_rate' to value '25'
OK    'video_rate=25'
Setting options string 'video_rate=30000/1001'
Setting entry with key 'video_rate' to value '30000/1001'
OK    'video_rate=30000/1001'
Setting options string 'video_rate=30/1.001'
Setting entry with key 'video_rate' to value '30/1.001'
OK    'video_rate=30/1.001'
Setting options string 'video_rate=bogus'
Setting entry with key 'video_rate' to value 'bogus'
Undefined constant or missing '(' in 'bogus'
Unable to parse option value "bogus" as video rate
Error 'video_rate=bogus'
Setting options string 'duration=bogus'
Setting entry with key 'duration' to value 'bogus'
Unable to parse option value "bogus" as duration
Error 'duration=bogus'
Setting options string 'duration=123.45'
Setting entry with key 'duration' to value '123.45'
OK    'duration=123.45'
Setting options string 'duration=1\:23\:45.67'
Setting entry with key 'duration' to value '1:23:45.67'
OK    'duration=1\:23\:45.67'
Setting options string 'color=blue'
Setting entry with key 'color' to value 'blue'
OK    'color=blue'
Setting options string 'color=0x223300'
Setting entry with key 'color' to value '0x223300'
OK    'color=0x223300'
Setting options string 'color=0x42FF07AA'
Setting entry with key 'color' to value '0x42FF07AA'
OK    'color=0x42FF07AA'
Setting options string 'cl=FL+FR'
Setting entry with key 'cl' to value 'FL+FR'
OK    'cl=FL+FR'
Setting options string 'cl=foo'
Setting entry with key 'cl' to value 'foo'
Unable to parse option value "foo" as channel layout
Error 'cl=foo'
Setting options string 'bin=boguss'
Setting entry with key 'bin' to value 'boguss'
Error 'bin=boguss'
Setting options string 'bin=111'
Setting entry with key 'bin' to value '111'
Error 'bin=111'
Setting options string 'bin=ffff'
Setting entry with key 'bin' to value 'ffff'
OK    'bin=ffff'
Setting options string 'num64=bogus'
Setting entry with key 'num64' to value 'bogus'
Undefined constant or missing '(' in 'bogus'
Unable to parse option value "bogus"
Error 'num64=bogus'
Setting options string 'num64=44'
Setting entry with key 'num64' to value '44'
OK    'num64=44'
Setting options string 'num64=44.4'
Setting entry with key 'num64' to value '44.4'
OK    'num64=44.4'
Setting options string 'num64=-1'
Setting entry with key 'num64' to value '-1'
Value -1.000000 for parameter 'num64' out of range [0 - 100]
Error 'num64=-1'
Setting options string 'num64=101'
Setting entry with key 'num64' to value '101'
Value 101.000000 for parameter 'num64' out of range [0 - 100]
Error 'num64=101'
Setting options string 'flt=bogus'
Setting entry with key 'flt' to value 'bogus'
Undefined constant or missing '(' in 'bogus'
Unable to parse option value "bogus"
Error 'flt=bogus'
Setting options string 'flt=2'
Setting entry with key 'flt' to value '2'
OK    'flt=2'
Setting options string 'flt=2.2'
Setting entry with key 'flt' to value '2.2'
OK    'flt=2.2'
Setting options string 'flt=-1'
Setting entry with key 'flt' to value '-1'
Value -1.000000 for parameter 'flt' out of range [0 - 100]
Error 'flt=-1'
Setting options string 'flt=101'
Setting entry with key 'flt' to value '101'
Value 101.000000 for parameter 'flt' out of range [0 - 100]
Error 'flt=101'
Setting options string 'dbl=bogus'
Setting entry with key 'dbl' to value 'bogus'
Undefined constant or missing '(' in 'bogus'
Unable to parse option value "bogus"
Error 'dbl=bogus'
Setting options string 'dbl=2'
Setting entry with key 'dbl' to value '2'
OK    'dbl=2'
Setting options string 'dbl=2.2'
Setting entry with key 'dbl' to value '2.2'
OK    'dbl=2.2'
Setting options string 'dbl=-1'
Setting entry with key 'dbl' to value '-1'
Value -1.000000 for parameter 'dbl' out of range [0 - 100]
Error 'dbl=-1'
Setting options string 'dbl=101'
Setting entry with key 'dbl' to value '101'
Value 101.000000 for parameter 'dbl' out of range [0 - 100]
Error 'dbl=101'
Setting options string 'bool1=true'
Setting entry with key 'bool1' to value 'true'
OK    'bool1=true'
Setting options string 'bool2=auto'
Setting entry with key 'bool2' to value 'auto'
OK    'bool2=auto'
Setting options string 'dict1='happy=\:-):sad=\:-(''
Setting entry with key 'dict1' to value 'happy=\:-):sad=\:-('
OK    'dict1='happy=\:-):sad=\:-(''
Setting options string 'array_int=0,32,2147483647'
Setting entry with key 'array_int' to value '0,32,2147483647'
OK    'array_int=0,32,2147483647'
Setting options string 'array_int=2147483648'
Setting entry with key 'array_int' to value '2147483648'
Value 2147483648.000000 for parameter 'array_int' out of range [0 - 2.14748e+09]
Error 'array_int=2147483648'

Testing av_opt_set_from_string()
Setting options string ''
OK    ''
Setting options string '5'
Setting 'num' to value '5'
OK    '5'
Setting options string '5:hello'
Setting 'num' to value '5'
Setting 'string' to value 'hello'
OK    '5:hello'
Setting options string '5:hello:size=pal'
Setting 'num' to value '5'
Setting 'string' to value 'hello'
Setting 'size' to value 'pal'
OK    '5:hello:size=pal'
Setting options string '5:size=pal:hello'
Setting 'num' to value '5'
Setting 'size' to value 'pal'
No option name near 'hello'
Error '5:size=pal:hello'
Setting options string ':'
Setting 'num' to value ''
Undefined constant or missing '(' in ''
Unable to parse option value ""
Error ':'
Setting options string '='
Setting '' to value ''
Option '' not found
Error '='
Setting options string ' 5 : hello : size = pal '
Setting 'num' to value '5'
Setting 'string' to value 'hello'
Setting 'size' to value 'pal'
OK    ' 5 : hello : size = pal '
Setting options string 'a_very_long_option_name_that_will_need_to_be_ellipsized_around_here=42'
Setting 'a_very_long_option_name_that_will_need_to_be_ellipsized_around_here' to value '42'
Option 'a_very_long_option_name_that_will_need_to_be_ellipsized_around_here' not found
Error 'a_very_long_option_name_that_will_need_to_be_ellipsized_around_here=42'
