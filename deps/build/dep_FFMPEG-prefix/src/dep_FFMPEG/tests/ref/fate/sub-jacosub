[Script Info]
; Script generated by FFmpeg/Lavc
ScriptType: v4.00+
PlayResX: 384
PlayResY: 288
ScaledBorderAndShadow: yes
YCbCr Matrix: None

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,16,&Hffffff,&Hffffff,&H0,&H0,0,0,0,0,100,100,0,0,1,1,0,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
Dialogue: 0,0:00:00.12,0:00:04.12,Default,,0,0,0,,{\an5}JACOsub\N\NThis script demonstrates some of the capabilities of JACOsub.
Dialogue: 0,0:00:04.12,0:00:06.62,De<PERSON>ult,,0,0,0,,{\an8}Text may be positioned at the top,
Dialogue: 0,0:00:05.12,0:00:07.22,<PERSON><PERSON><PERSON>,,0,0,0,,{\an5}middle,
Dialogue: 0,0:00:06.12,0:00:07.82,Default,,0,0,0,,{\an2}or bottom of the screen.
Dialogue: 0,0:00:08.12,0:00:11.12,Default,,0,0,0,,{\an5}{this is a comment} (And, you just saw, {another comment} timing ranges for different lines of text.
Dialogue: 0,0:00:11.12,0:00:13.62,Default,,0,0,0,,{\an1}Within margin constraints\Nthat you set, text may be\Nleft justified,
Dialogue: 0,0:00:13.62,0:00:14.87,Default,,0,0,0,,{\an2}{the JC is redundant - it's the default}center\Njustified,
Dialogue: 0,0:00:14.87,0:00:16.12,Default,,0,0,0,,{\an3}and also\Nright justified.
Dialogue: 0,0:00:22.42,0:00:27.92,Default,,0,0,0,,Text may appear in different styles\N(Normal, {\b1}Bold{\r}, {\i1}Italic{\r})
Dialogue: 0,0:01:16.12,0:01:21.12,Default,,0,0,0,,{\an5}\N\NAt that time, you may press any key to return to the Editor.
Dialogue: 0,0:01:16.12,0:01:21.12,Default,,0,0,0,,OK, this script will be finished when the screen goes blank.
