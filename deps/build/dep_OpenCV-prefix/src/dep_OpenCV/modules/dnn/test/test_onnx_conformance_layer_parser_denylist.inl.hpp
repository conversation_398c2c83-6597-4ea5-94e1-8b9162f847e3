// The file is autogenerated
// Update note: execute <opencv_extra>/testdata/dnn/onnx/generate_conformance_list.py <gtest_xml_file>
"test_adagrad",
"test_adagrad_multiple",
"test_adam",
"test_adam_multiple",
"test_and2d",
"test_and3d",
"test_and4d",
"test_and_bcast3v1d",
"test_and_bcast3v2d",
"test_and_bcast4v2d",
"test_and_bcast4v3d",
"test_and_bcast4v4d",
"test_basic_convinteger",
"test_batchnorm_epsilon",
"test_batchnorm_epsilon_training_mode",
"test_batchnorm_example",
"test_batchnorm_example_training_mode",
"test_bernoulli",
"test_bernoulli_double",
"test_bernoulli_double_expanded",
"test_bernoulli_expanded",
"test_bernoulli_seed",
"test_bernoulli_seed_expanded",
"test_bitshift_left_uint16",
"test_bitshift_left_uint32",
"test_bitshift_left_uint64",
"test_bitshift_left_uint8",
"test_bitshift_right_uint16",
"test_bitshift_right_uint32",
"test_bitshift_right_uint64",
"test_bitshift_right_uint8",
"test_cast_BFLOAT16_to_FLOAT",
"test_cast_DOUBLE_to_FLOAT",
"test_cast_DOUBLE_to_FLOAT16",
"test_cast_FLOAT16_to_DOUBLE",
"test_cast_FLOAT16_to_FLOAT",
"test_cast_FLOAT_to_BFLOAT16",
"test_cast_FLOAT_to_DOUBLE",
"test_cast_FLOAT_to_FLOAT16",
"test_castlike_BFLOAT16_to_FLOAT",
"test_castlike_BFLOAT16_to_FLOAT_expanded",
"test_castlike_DOUBLE_to_FLOAT",
"test_castlike_DOUBLE_to_FLOAT16",
"test_castlike_DOUBLE_to_FLOAT16_expanded",
"test_castlike_DOUBLE_to_FLOAT_expanded",
"test_castlike_FLOAT16_to_DOUBLE",
"test_castlike_FLOAT16_to_DOUBLE_expanded",
"test_castlike_FLOAT16_to_FLOAT",
"test_castlike_FLOAT16_to_FLOAT_expanded",
"test_castlike_FLOAT_to_BFLOAT16",
"test_castlike_FLOAT_to_BFLOAT16_expanded",
"test_castlike_FLOAT_to_DOUBLE",
"test_castlike_FLOAT_to_DOUBLE_expanded",
"test_castlike_FLOAT_to_FLOAT16",
"test_castlike_FLOAT_to_FLOAT16_expanded",
"test_castlike_FLOAT_to_STRING",
"test_castlike_STRING_to_FLOAT",
"test_clip",
"test_clip_default_inbounds",
"test_clip_default_int8_inbounds",
"test_clip_default_int8_max",
"test_clip_default_int8_min",
"test_clip_default_max",
"test_clip_default_min",
"test_clip_example",
"test_clip_inbounds",
"test_clip_outbounds",
"test_clip_splitbounds",
"test_compress_0",
"test_compress_1",
"test_compress_default_axis",
"test_compress_negative_axis",
"test_constant",
"test_constant_pad",
"test_constantofshape_float_ones",
"test_constantofshape_int_shape_zero",
"test_constantofshape_int_zeros",
"test_convinteger_with_padding",
"test_convinteger_without_padding",
"test_convtranspose",
"test_convtranspose_1d",
"test_convtranspose_3d",
"test_convtranspose_autopad_same",
"test_convtranspose_dilations",
"test_convtranspose_kernel_shape",
"test_convtranspose_output_shape",
"test_convtranspose_pad",
"test_convtranspose_pads",
"test_convtranspose_with_kernel",
"test_cumsum_1d",
"test_cumsum_1d_exclusive",
"test_cumsum_1d_reverse",
"test_cumsum_1d_reverse_exclusive",
"test_cumsum_2d_axis_0",
"test_cumsum_2d_axis_1",
"test_cumsum_2d_negative_axis",
"test_dequantizelinear",
"test_dequantizelinear_axis",
"test_det_2d",
"test_det_nd",
"test_div_example",
"test_dropout_default_mask",
"test_dropout_default_mask_ratio",
"test_dynamicquantizelinear",
"test_dynamicquantizelinear_expanded",
"test_dynamicquantizelinear_max_adjusted",
"test_dynamicquantizelinear_max_adjusted_expanded",
"test_dynamicquantizelinear_min_adjusted",
"test_dynamicquantizelinear_min_adjusted_expanded",
"test_edge_pad",
"test_einsum_batch_diagonal",
"test_einsum_batch_matmul",
"test_einsum_inner_prod",
"test_einsum_sum",
"test_einsum_transpose",
"test_equal",
"test_equal_bcast",
"test_expand_dim_changed",
"test_expand_dim_unchanged",
"test_eyelike_populate_off_main_diagonal",
"test_eyelike_with_dtype",
"test_eyelike_without_dtype",
"test_gather_0",
"test_gather_1",
"test_gather_2d_indices",
"test_gather_elements_0",
"test_gather_elements_1",
"test_gather_elements_negative_indices",
"test_gather_negative_indices",
"test_gathernd_example_float32",
"test_gathernd_example_int32",
"test_gathernd_example_int32_batch_dim1",
"test_gemm_all_attributes",
"test_gemm_alpha",
"test_gemm_beta",
"test_gemm_default_matrix_bias",
"test_gemm_default_no_bias",
"test_gemm_default_scalar_bias",
"test_gemm_default_single_elem_vector_bias",
"test_gemm_default_vector_bias",
"test_gemm_default_zero_bias",
"test_gemm_transposeA",
"test_gemm_transposeB",
"test_greater",
"test_greater_bcast",
"test_greater_equal",
"test_greater_equal_bcast",
"test_greater_equal_bcast_expanded",
"test_greater_equal_expanded",
"test_gridsample",
"test_gridsample_aligncorners_true",
"test_gridsample_bicubic",
"test_gridsample_bilinear",
"test_gridsample_border_padding",
"test_gridsample_nearest",
"test_gridsample_reflection_padding",
"test_gridsample_zeros_padding",
"test_gru_batchwise",
"test_gru_defaults",
"test_gru_seq_length",
"test_gru_with_initial_bias",
"test_hardmax_axis_0",
"test_hardmax_axis_1",
"test_hardmax_axis_2",
"test_hardmax_default_axis",
"test_hardmax_example",
"test_hardmax_negative_axis",
"test_hardmax_one_hot",
"test_identity_opt",
"test_identity_sequence",
"test_if",
"test_if_opt",
"test_if_seq",
"test_instancenorm_epsilon",
"test_instancenorm_example",
"test_isinf",
"test_isinf_negative",
"test_isinf_positive",
"test_isnan",
"test_less",
"test_less_bcast",
"test_less_equal",
"test_less_equal_bcast",
"test_less_equal_bcast_expanded",
"test_less_equal_expanded",
"test_loop11",
"test_loop13_seq",
"test_loop16_seq_none",
"test_lstm_batchwise",
"test_lstm_defaults",
"test_lstm_with_initial_bias",
"test_lstm_with_peepholes",
"test_matmulinteger",
"test_max_example",
"test_max_float16",
"test_max_float32",
"test_max_float64",
"test_max_int16",
"test_max_int32",
"test_max_int64",
"test_max_int8",
"test_max_one_input",
"test_max_two_inputs",
"test_max_uint16",
"test_max_uint32",
"test_max_uint64",
"test_max_uint8",
"test_mean_example",
"test_mean_one_input",
"test_mean_two_inputs",
"test_min_example",
"test_min_float16",
"test_min_float32",
"test_min_float64",
"test_min_int16",
"test_min_int32",
"test_min_int64",
"test_min_int8",
"test_min_one_input",
"test_min_two_inputs",
"test_min_uint16",
"test_min_uint32",
"test_min_uint64",
"test_min_uint8",
"test_mod_broadcast",
"test_mod_int64_fmod",
"test_mod_mixed_sign_float16",
"test_mod_mixed_sign_float32",
"test_mod_mixed_sign_float64",
"test_mod_mixed_sign_int16",
"test_mod_mixed_sign_int32",
"test_mod_mixed_sign_int64",
"test_mod_mixed_sign_int8",
"test_mod_uint16",
"test_mod_uint32",
"test_mod_uint64",
"test_mod_uint8",
"test_momentum",
"test_momentum_multiple",
"test_mul_example",
"test_mvn",
"test_mvn_expanded",
"test_nesterov_momentum",
"test_nllloss_NC",
"test_nllloss_NC_expanded",
"test_nllloss_NCd1",
"test_nllloss_NCd1_expanded",
"test_nllloss_NCd1_ii",
"test_nllloss_NCd1_ii_expanded",
"test_nllloss_NCd1_mean_weight_negative_ii",
"test_nllloss_NCd1_mean_weight_negative_ii_expanded",
"test_nllloss_NCd1_weight",
"test_nllloss_NCd1_weight_expanded",
"test_nllloss_NCd1_weight_ii",
"test_nllloss_NCd1_weight_ii_expanded",
"test_nllloss_NCd1d2",
"test_nllloss_NCd1d2_expanded",
"test_nllloss_NCd1d2_no_weight_reduction_mean_ii",
"test_nllloss_NCd1d2_no_weight_reduction_mean_ii_expanded",
"test_nllloss_NCd1d2_reduction_mean",
"test_nllloss_NCd1d2_reduction_mean_expanded",
"test_nllloss_NCd1d2_reduction_sum",
"test_nllloss_NCd1d2_reduction_sum_expanded",
"test_nllloss_NCd1d2_with_weight",
"test_nllloss_NCd1d2_with_weight_expanded",
"test_nllloss_NCd1d2_with_weight_reduction_mean",
"test_nllloss_NCd1d2_with_weight_reduction_mean_expanded",
"test_nllloss_NCd1d2_with_weight_reduction_sum",
"test_nllloss_NCd1d2_with_weight_reduction_sum_expanded",
"test_nllloss_NCd1d2_with_weight_reduction_sum_ii",
"test_nllloss_NCd1d2_with_weight_reduction_sum_ii_expanded",
"test_nllloss_NCd1d2d3_none_no_weight_negative_ii",
"test_nllloss_NCd1d2d3_none_no_weight_negative_ii_expanded",
"test_nllloss_NCd1d2d3_sum_weight_high_ii",
"test_nllloss_NCd1d2d3_sum_weight_high_ii_expanded",
"test_nllloss_NCd1d2d3d4d5_mean_weight",
"test_nllloss_NCd1d2d3d4d5_mean_weight_expanded",
"test_nllloss_NCd1d2d3d4d5_none_no_weight",
"test_nllloss_NCd1d2d3d4d5_none_no_weight_expanded",
"test_nonmaxsuppression_center_point_box_format",
"test_nonmaxsuppression_flipped_coordinates",
"test_nonmaxsuppression_identical_boxes",
"test_nonmaxsuppression_limit_output_size",
"test_nonmaxsuppression_single_box",
"test_nonmaxsuppression_suppress_by_IOU",
"test_nonmaxsuppression_suppress_by_IOU_and_scores",
"test_nonmaxsuppression_two_batches",
"test_nonmaxsuppression_two_classes",
"test_nonzero_example",
"test_not_2d",
"test_not_3d",
"test_not_4d",
"test_onehot_negative_indices",
"test_onehot_with_axis",
"test_onehot_with_negative_axis",
"test_onehot_without_axis",
"test_optional_get_element",
"test_optional_get_element_sequence",
"test_optional_has_element",
"test_optional_has_element_empty",
"test_or2d",
"test_or3d",
"test_or4d",
"test_or_bcast3v1d",
"test_or_bcast3v2d",
"test_or_bcast4v2d",
"test_or_bcast4v3d",
"test_or_bcast4v4d",
"test_pow",
"test_pow_bcast_array",
"test_pow_bcast_scalar",
"test_pow_example",
"test_pow_types_float",
"test_pow_types_float32_int32",
"test_pow_types_float32_int64",
"test_pow_types_float32_uint32",
"test_pow_types_float32_uint64",
"test_pow_types_int",
"test_pow_types_int32_float32",
"test_pow_types_int32_int32",
"test_pow_types_int64_float32",
"test_pow_types_int64_int64",
"test_prelu_broadcast",
"test_prelu_example",
"test_qlinearconv",
"test_qlinearmatmul_2D",
"test_qlinearmatmul_3D",
"test_quantizelinear",
"test_quantizelinear_axis",
"test_range_float_type_positive_delta",
"test_range_float_type_positive_delta_expanded",
"test_range_int32_type_negative_delta",
"test_range_int32_type_negative_delta_expanded",
"test_reduce_sum_default_axes_keepdims_example",
"test_reduce_sum_default_axes_keepdims_random",
"test_reduce_sum_do_not_keepdims_example",
"test_reduce_sum_do_not_keepdims_random",
"test_reduce_sum_empty_axes_input_noop_example",
"test_reduce_sum_empty_axes_input_noop_random",
"test_reduce_sum_keepdims_example",
"test_reduce_sum_keepdims_random",
"test_reduce_sum_negative_axes_keepdims_example",
"test_reduce_sum_negative_axes_keepdims_random",
"test_reflect_pad",
"test_reshape_allowzero_reordered",
"test_reshape_extended_dims",
"test_reshape_negative_dim",
"test_reshape_negative_extended_dims",
"test_reshape_one_dim",
"test_reshape_reduced_dims",
"test_reshape_reordered_all_dims",
"test_reshape_reordered_last_dims",
"test_reshape_zero_and_negative_dim",
"test_reshape_zero_dim",
"test_resize_downsample_scales_cubic",
"test_resize_downsample_scales_cubic_A_n0p5_exclude_outside",
"test_resize_downsample_scales_cubic_align_corners",
"test_resize_downsample_scales_linear",
"test_resize_downsample_scales_linear_align_corners",
"test_resize_downsample_scales_nearest",
"test_resize_downsample_sizes_cubic",
"test_resize_downsample_sizes_linear_pytorch_half_pixel",
"test_resize_downsample_sizes_nearest",
"test_resize_downsample_sizes_nearest_tf_half_pixel_for_nn",
"test_resize_tf_crop_and_resize",
"test_resize_upsample_scales_cubic",
"test_resize_upsample_scales_cubic_A_n0p5_exclude_outside",
"test_resize_upsample_scales_cubic_align_corners",
"test_resize_upsample_scales_cubic_asymmetric",
"test_resize_upsample_scales_linear",
"test_resize_upsample_scales_linear_align_corners",
"test_resize_upsample_scales_nearest",
"test_resize_upsample_sizes_cubic",
"test_resize_upsample_sizes_nearest",
"test_resize_upsample_sizes_nearest_ceil_half_pixel",
"test_resize_upsample_sizes_nearest_floor_align_corners",
"test_resize_upsample_sizes_nearest_round_prefer_ceil_asymmetric",
"test_reversesequence_batch",
"test_reversesequence_time",
"test_rnn_seq_length",
"test_roialign_aligned_false",
"test_roialign_aligned_true",
"test_scan9_sum",
"test_scan_sum",
"test_scatter_elements_with_axis",
"test_scatter_elements_with_duplicate_indices",
"test_scatter_elements_with_negative_indices",
"test_scatter_elements_without_axis",
"test_scatter_with_axis",
"test_scatter_without_axis",
"test_scatternd",
"test_scatternd_add",
"test_scatternd_multiply",
"test_sce_NCd1_mean_weight_negative_ii",
"test_sce_NCd1_mean_weight_negative_ii_expanded",
"test_sce_NCd1_mean_weight_negative_ii_log_prob",
"test_sce_NCd1_mean_weight_negative_ii_log_prob_expanded",
"test_sce_NCd1d2d3_none_no_weight_negative_ii",
"test_sce_NCd1d2d3_none_no_weight_negative_ii_expanded",
"test_sce_NCd1d2d3_none_no_weight_negative_ii_log_prob",
"test_sce_NCd1d2d3_none_no_weight_negative_ii_log_prob_expanded",
"test_sce_NCd1d2d3_sum_weight_high_ii",
"test_sce_NCd1d2d3_sum_weight_high_ii_expanded",
"test_sce_NCd1d2d3_sum_weight_high_ii_log_prob",
"test_sce_NCd1d2d3_sum_weight_high_ii_log_prob_expanded",
"test_sce_NCd1d2d3d4d5_mean_weight",
"test_sce_NCd1d2d3d4d5_mean_weight_expanded",
"test_sce_NCd1d2d3d4d5_mean_weight_log_prob",
"test_sce_NCd1d2d3d4d5_mean_weight_log_prob_expanded",
"test_sce_NCd1d2d3d4d5_none_no_weight",
"test_sce_NCd1d2d3d4d5_none_no_weight_expanded",
"test_sce_NCd1d2d3d4d5_none_no_weight_log_prob",
"test_sce_NCd1d2d3d4d5_none_no_weight_log_prob_expanded",
"test_sce_mean",
"test_sce_mean_3d",
"test_sce_mean_3d_expanded",
"test_sce_mean_3d_log_prob",
"test_sce_mean_3d_log_prob_expanded",
"test_sce_mean_expanded",
"test_sce_mean_log_prob",
"test_sce_mean_log_prob_expanded",
"test_sce_mean_no_weight_ii",
"test_sce_mean_no_weight_ii_3d",
"test_sce_mean_no_weight_ii_3d_expanded",
"test_sce_mean_no_weight_ii_3d_log_prob",
"test_sce_mean_no_weight_ii_3d_log_prob_expanded",
"test_sce_mean_no_weight_ii_4d",
"test_sce_mean_no_weight_ii_4d_expanded",
"test_sce_mean_no_weight_ii_4d_log_prob",
"test_sce_mean_no_weight_ii_4d_log_prob_expanded",
"test_sce_mean_no_weight_ii_expanded",
"test_sce_mean_no_weight_ii_log_prob",
"test_sce_mean_no_weight_ii_log_prob_expanded",
"test_sce_mean_weight",
"test_sce_mean_weight_expanded",
"test_sce_mean_weight_ii",
"test_sce_mean_weight_ii_3d",
"test_sce_mean_weight_ii_3d_expanded",
"test_sce_mean_weight_ii_3d_log_prob",
"test_sce_mean_weight_ii_3d_log_prob_expanded",
"test_sce_mean_weight_ii_4d",
"test_sce_mean_weight_ii_4d_expanded",
"test_sce_mean_weight_ii_4d_log_prob",
"test_sce_mean_weight_ii_4d_log_prob_expanded",
"test_sce_mean_weight_ii_expanded",
"test_sce_mean_weight_ii_log_prob",
"test_sce_mean_weight_ii_log_prob_expanded",
"test_sce_mean_weight_log_prob",
"test_sce_mean_weight_log_prob_expanded",
"test_sce_none",
"test_sce_none_expanded",
"test_sce_none_log_prob",
"test_sce_none_log_prob_expanded",
"test_sce_none_weights",
"test_sce_none_weights_expanded",
"test_sce_none_weights_log_prob",
"test_sce_none_weights_log_prob_expanded",
"test_sce_sum",
"test_sce_sum_expanded",
"test_sce_sum_log_prob",
"test_sce_sum_log_prob_expanded",
"test_sequence_insert_at_back",
"test_sequence_insert_at_front",
"test_shape",
"test_shape_clip_end",
"test_shape_clip_start",
"test_shape_end_1",
"test_shape_end_negative_1",
"test_shape_example",
"test_shape_start_1",
"test_shape_start_1_end_2",
"test_shape_start_1_end_negative_1",
"test_shape_start_negative_1",
"test_simple_rnn_batchwise",
"test_simple_rnn_defaults",
"test_simple_rnn_with_initial_bias",
"test_size",
"test_size_example",
"test_slice",
"test_slice_default_axes",
"test_slice_default_steps",
"test_slice_end_out_of_bounds",
"test_slice_neg",
"test_slice_neg_steps",
"test_slice_negative_axes",
"test_slice_start_out_of_bounds",
"test_split_variable_parts_1d",
"test_split_variable_parts_2d",
"test_split_variable_parts_default_axis",
"test_split_zero_size_splits",
"test_squeeze",
"test_squeeze_negative_axes",
"test_strnormalizer_export_monday_casesensintive_lower",
"test_strnormalizer_export_monday_casesensintive_nochangecase",
"test_strnormalizer_export_monday_casesensintive_upper",
"test_strnormalizer_export_monday_empty_output",
"test_strnormalizer_export_monday_insensintive_upper_twodim",
"test_strnormalizer_nostopwords_nochangecase",
"test_sub_example",
"test_sum_example",
"test_sum_two_inputs",
"test_tfidfvectorizer_tf_batch_onlybigrams_skip0",
"test_tfidfvectorizer_tf_batch_onlybigrams_skip5",
"test_tfidfvectorizer_tf_batch_uniandbigrams_skip5",
"test_tfidfvectorizer_tf_only_bigrams_skip0",
"test_tfidfvectorizer_tf_onlybigrams_levelempty",
"test_tfidfvectorizer_tf_onlybigrams_skip5",
"test_tfidfvectorizer_tf_uniandbigrams_skip5",
"test_tile",
"test_tile_precomputed",
"test_top_k",
"test_top_k_negative_axis",
"test_top_k_smallest",
"test_training_dropout",
"test_training_dropout_default",
"test_training_dropout_default_mask",
"test_training_dropout_mask",
"test_training_dropout_zero_ratio",
"test_training_dropout_zero_ratio_mask",
"test_tril",
"test_tril_neg",
"test_tril_one_row_neg",
"test_tril_out_neg",
"test_tril_out_pos",
"test_tril_pos",
"test_tril_square",
"test_tril_square_neg",
"test_tril_zero",
"test_triu",
"test_triu_neg",
"test_triu_one_row",
"test_triu_out_neg_out",
"test_triu_out_pos",
"test_triu_pos",
"test_triu_square",
"test_triu_square_neg",
"test_triu_zero",
"test_unique_not_sorted_without_axis",
"test_unique_sorted_with_axis",
"test_unique_sorted_with_axis_3d",
"test_unique_sorted_with_negative_axis",
"test_unique_sorted_without_axis",
"test_unsqueeze_axis_0",
"test_unsqueeze_axis_1",
"test_unsqueeze_axis_2",
"test_unsqueeze_negative_axes",
"test_unsqueeze_three_axes",
"test_unsqueeze_two_axes",
"test_unsqueeze_unsorted_axes",
"test_where_example",
"test_where_long_example",
"test_xor2d",
"test_xor3d",
"test_xor4d",
"test_xor_bcast3v1d",
"test_xor_bcast3v2d",
"test_xor_bcast4v2d",
"test_xor_bcast4v3d",
"test_xor_bcast4v4d",
