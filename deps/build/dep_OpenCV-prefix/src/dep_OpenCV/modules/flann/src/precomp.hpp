#ifndef OPENCV_FLANN_PRECOMP_HPP
#define OPENCV_FLANN_PRECOMP_HPP

#include <cstdio>
#include <cstdarg>
#include <sstream>

#include "opencv2/core.hpp"
#include "opencv2/core/utility.hpp"

#include "opencv2/flann/miniflann.hpp"
#include "opencv2/flann/dist.h"
#include "opencv2/flann/index_testing.h"
#include "opencv2/flann/params.h"
#include "opencv2/flann/saving.h"

// index types
#include "opencv2/flann/all_indices.h"
#include "opencv2/flann/flann_base.hpp"

#include "opencv2/core/private.hpp"

#endif
