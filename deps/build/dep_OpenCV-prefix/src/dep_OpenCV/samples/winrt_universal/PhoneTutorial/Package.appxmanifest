﻿<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/2010/manifest" xmlns:m2="http://schemas.microsoft.com/appx/2013/manifest" xmlns:m3="http://schemas.microsoft.com/appx/2014/manifest" xmlns:mp="http://schemas.microsoft.com/appx/2014/phone/manifest">
  <Identity Name="0a7cc91b-c995-40cf-adf4-b7ac4ba969c6" Publisher="CN=dalestam" Version="1.0.0.0" />
  <mp:PhoneIdentity PhoneProductId="0a7cc91b-c995-40cf-adf4-b7ac4ba969c6" PhonePublisherId="00000000-0000-0000-0000-000000000000" />
  <Properties>
    <DisplayName>PhoneTutorial</DisplayName>
    <PublisherDisplayName>dalestam</PublisherDisplayName>
    <Logo>Assets\StoreLogo.png</Logo>
  </Properties>
  <Prerequisites>
    <OSMinVersion>6.3.1</OSMinVersion>
    <OSMaxVersionTested>6.3.1</OSMaxVersionTested>
  </Prerequisites>
  <Resources>
    <Resource Language="x-generate" />
  </Resources>
  <Applications>
    <Application Id="App" Executable="$targetnametoken$.exe" EntryPoint="PhoneTutorial.App">
      <m3:VisualElements DisplayName="PhoneTutorial" Square150x150Logo="Assets\Logo.png" Square44x44Logo="Assets\SmallLogo.png" Description="PhoneTutorial" ForegroundText="light" BackgroundColor="transparent">
        <m3:DefaultTile Wide310x150Logo="Assets\WideLogo.png" Square71x71Logo="Assets\Square71x71Logo.png">
        </m3:DefaultTile>
        <m3:SplashScreen Image="Assets\SplashScreen.png" />
        <m3:ApplicationView MinWidth="width320" />
        <!--Used in XAML Designer. DO NOT REMOVE-->
        <m3:InitialRotationPreference>
          <m3:Rotation Preference="portrait" />
        </m3:InitialRotationPreference>
      </m3:VisualElements>
    </Application>
  </Applications>
  <Capabilities>
    <Capability Name="internetClientServer" />
  </Capabilities>
</Package>