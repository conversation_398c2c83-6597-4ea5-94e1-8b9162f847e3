﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Common">
      <UniqueIdentifier>a5555ea2-f9e8-4078-90f5-d428f2c0d6d1</UniqueIdentifier>
    </Filter>
    <Filter Include="Assets">
      <UniqueIdentifier>714bc2d8-fb65-4062-80fa-4d1822fe24e2</UniqueIdentifier>
    </Filter>
    <Page Include="Common\StandardStyles.xaml">
      <Filter>Common</Filter>
    </Page>
    <Image Include="Assets\Logo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SmallLogo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\StoreLogo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SplashScreen.png">
      <Filter>Assets</Filter>
    </Image>
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="App.xaml.cpp" />
    <ClCompile Include="MainPage.xaml.cpp" />
    <ClCompile Include="pch.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="pch.h" />
    <ClInclude Include="App.xaml.h" />
    <ClInclude Include="MainPage.xaml.h" />
  </ItemGroup>
  <ItemGroup>
    <AppxManifest Include="Package.appxmanifest" />
  </ItemGroup>
  <ItemGroup>
    <None Include="OcvImageProcessing_TemporaryKey.pfx" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="MainPage.xaml" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="Assets\Lena.png">
      <Filter>Assets</Filter>
    </Image>
  </ItemGroup>
</Project>