# This is the CMakeCache file.
# For build in directory: /Users/<USER>/Documents/augment-projects/BambuStudio/build/debug
# It was generated by CMake: /Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//No help, variable specified on the command line.
BBL_RELEASE_TO_PUBLIC:UNINITIALIZED=1

//Build bbs test tools
BUILD_BBS_TEST_TOOLS:BOOL=OFF

//Build shared libs
BUILD_SHARED_LIBS:BOOL=OFF

//Build the testing tree.
BUILD_TESTING:BOOL=OFF

//Path to a file.
BZIP2_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include

//Path to a library.
BZIP2_LIBRARY_DEBUG:FILEPATH=BZIP2_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
BZIP2_LIBRARY_RELEASE:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libbz2.tbd

//Value Computed by CMake
BambuStudio-native_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src

//Value Computed by CMake
BambuStudio-native_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
BambuStudio-native_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src

//Value Computed by CMake
BambuStudio_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug

//Value Computed by CMake
BambuStudio_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
BambuStudio_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio

//The directory containing a CMake configuration file for Blosc.
Blosc_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Blosc

Boost_ATOMIC_LIBRARY_RELEASE:STRING=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_atomic.a

Boost_CHRONO_LIBRARY_RELEASE:STRING=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_chrono.a

Boost_DATE_TIME_LIBRARY_RELEASE:STRING=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_date_time.a

//Activate the debug messages of the script FindBoost
Boost_DEBUG:BOOL=OFF

//The directory containing a CMake configuration file for Boost.
Boost_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0

Boost_FILESYSTEM_LIBRARY_RELEASE:STRING=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_filesystem.a

//Path to a file.
Boost_INCLUDE_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include

Boost_IOSTREAMS_LIBRARY_RELEASE:STRING=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_iostreams.a

Boost_LOCALE_LIBRARY_RELEASE:STRING=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_locale.a

Boost_LOG_LIBRARY_RELEASE:STRING=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_log.a

Boost_THREAD_LIBRARY_RELEASE:STRING=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_thread.a

//Link with static Boost libraries
CGAL_Boost_USE_STATIC_LIBS:BOOL=ON

//Display memory and real time usage at end of CTest test outputs
CGAL_CTEST_DISPLAY_MEM_AND_TIME:BOOL=OFF

//Activate the CGAL developers mode. See https://github.com/CGAL/cgal/wiki/CGAL_DEV_MODE
CGAL_DEV_MODE:BOOL=OFF

//The directory containing a CMake configuration file for CGAL.
CGAL_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL

CGAL_DO_NOT_WARN_ABOUT_CMAKE_BUILD_TYPE:BOOL=ON

//If set, the ctest command will not skip the tests of the draw
// functions.
CGAL_TEST_DRAW_FUNCTIONS:BOOL=OFF

//Use CGAL with GMPXX: use C++ classes of GNU MP instead of CGAL
// wrappers
CGAL_WITH_GMPXX:BOOL=ON

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Debug

//Enable colored diagnostics throughout.
CMAKE_COLOR_DIAGNOSTICS:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:STRING=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:STRING=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of build database during the build.
CMAKE_EXPORT_BUILD_DATABASE:BOOL=

//No help, variable specified on the command line.
CMAKE_EXPORT_COMPILE_COMMANDS:UNINITIALIZED=ON

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio/install_dir

//No help, variable specified on the command line.
CMAKE_INSTALL_RPATH:UNINITIALIZED=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld

//No help, variable specified on the command line.
CMAKE_MACOSX_BUNDLE:UNINITIALIZED=ON

//No help, variable specified on the command line.
CMAKE_MACOSX_RPATH:UNINITIALIZED=ON

//No help, variable specified on the command line.
CMAKE_MAKE_PROGRAM:UNINITIALIZED=/Applications/CLion.app/Contents/bin/ninja/mac/aarch64/ninja

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=CMAKE_OBJCOPY-NOTFOUND

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=

//Minimum OS X deployment version
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=10.15

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk

//No help, variable specified on the command line.
CMAKE_PREFIX_PATH:UNINITIALIZED=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=BambuStudio

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.0.6

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=6

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Enable to build RPM source packages
CPACK_SOURCE_RPM:BOOL=OFF

//Enable to build TBZ2 source packages
CPACK_SOURCE_TBZ2:BOOL=ON

//Enable to build TGZ source packages
CPACK_SOURCE_TGZ:BOOL=ON

//Enable to build TXZ source packages
CPACK_SOURCE_TXZ:BOOL=ON

//Enable to build TZ source packages
CPACK_SOURCE_TZ:BOOL=ON

//Enable to build ZIP source packages
CPACK_SOURCE_ZIP:BOOL=OFF

//The directory containing a CMake configuration file for CURL.
CURL_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CURL

//Value Computed by CMake
Clipper2_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/clipper2

//Value Computed by CMake
Clipper2_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
Clipper2_LIB_DEPENDS:STATIC=general;-lm;

//Value Computed by CMake
Clipper2_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/clipper2

//Path to a library.
DISKARBITRATION_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DiskArbitration.framework

//Path to a file.
EXPAT_INCLUDE_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include

//Path to a library.
EXPAT_LIBRARY_DEBUG:FILEPATH=EXPAT_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
EXPAT_LIBRARY_RELEASE:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libexpat.a

//Not copy FFMPEG file
FLATPAK:BOOL=FALSE

//Path to a library.
FOUNDATION:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework

//The directory containing a CMake configuration file for GLEW.
GLEW_DIR:PATH=/opt/homebrew/lib/cmake/glew

//The directory containing the GMPXX include files
GMPXX_INCLUDE_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include

//Path to the GMPXX library
GMPXX_LIBRARIES:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libgmpxx.a

//The directory containing the GMP header files
GMP_INCLUDE_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include

//Path to the Debug GMP library
GMP_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libgmp.a

//Path to the Release GMP library
GMP_LIBRARY_RELEASE:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libgmp.a

//Value Computed by CMake
HintsToPot_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/hints

//Value Computed by CMake
HintsToPot_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
HintsToPot_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/hints

//iconv include directory
Iconv_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include

//iconv library (if not in the C library)
Iconv_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libiconv.tbd

//The directory containing a CMake configuration file for IlmBase.
IlmBase_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/IlmBase

//Path to a file.
JPEG_INCLUDE_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include

//Path to a library.
JPEG_LIBRARY_DEBUG:FILEPATH=JPEG_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
JPEG_LIBRARY_RELEASE:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libjpeg.a

//Path to a file.
LIBLZMA_INCLUDE_DIR:PATH=/opt/homebrew/include

//Path to a library.
LIBLZMA_LIBRARY_DEBUG:FILEPATH=LIBLZMA_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
LIBLZMA_LIBRARY_RELEASE:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/liblzma.tbd

//Path to a library.
LIBRT:FILEPATH=LIBRT-NOTFOUND

//Configure to build MCUT as a shared/dynamic library
MCUT_BUILD_AS_SHARED_LIB:BOOL=OFF

//Configure to build docs with Doxygen
MCUT_BUILD_DOCUMENTATION:BOOL=OFF

//Configure to build MCUT engine with a shared (amongst contexts)
// thread-pool
MCUT_BUILD_WITH_COMPUTE_HELPER_THREADPOOL:BOOL=ON

//The MCUT include directory
MCUT_INCLUDE_DIR:STRING=/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include

//Path to the compiled MCUT library file
MCUT_LIB_PATH:STRING=$<TARGET_FILE:mcut>

//Path to a library.
MODELIO:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ModelIO.framework

//The directory containing the MPFR header files
MPFR_INCLUDE_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include

//Path to the MPFR library
MPFR_LIBRARIES:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libmpfr.a

MPFR_LIBRARIES_DIR:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib

//Path to a library.
NLopt_LIBS:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libnlopt.a

//Include for the OpenGL GLU library
OPENGL_GLU_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework

//Include for OpenGL on OS X
OPENGL_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework

//OpenGL library for OS X
OPENGL_gl_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework

//GLU library for OS X (usually same as OpenGL library)
OPENGL_glu_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework

//Path to a library.
OPENSSL_CRYPTO_LIBRARY:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libcrypto.a

//Path to a file.
OPENSSL_INCLUDE_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include

//Path to a library.
OPENSSL_SSL_LIBRARY:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libssl.a

//Path to OpenVDB installation's find modules.
OPENVDB_FIND_MODULE_PATH:PATH=

//Path to a program.
OPENVDB_PRINT:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/bin/vdb_print

//The directory containing a CMake configuration file for OpenCASCADE.
OpenCASCADE_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade

//The directory containing a CMake configuration file for OpenCV.
OpenCV_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencv4

//Suffix for the debug libraries
OpenVDB_DEBUG_SUFFIX:STRING=d

//Path to a file.
OpenVDB_INCLUDE_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include

//Path to a library.
OpenVDB_openvdb_LIBRARY_DEBUG:FILEPATH=OpenVDB_openvdb_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OpenVDB_openvdb_LIBRARY_RELEASE:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libopenvdb.a

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/opt/homebrew/bin/pkg-config

//Path to a library.
PNG_LIBRARY_DEBUG:FILEPATH=PNG_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
PNG_LIBRARY_RELEASE:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libpng.a

//Path to a file.
PNG_PNG_INCLUDE_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include

//The directory containing a CMake configuration file for Qhull.
Qhull_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Qhull

//Enable ASan on Clang and GCC
SLIC3R_ASAN:BOOL=OFF

//Build development sandboxes
SLIC3R_BUILD_SANDBOXES:BOOL=OFF

//Build unit tests
SLIC3R_BUILD_TESTS:BOOL=OFF

//Allow perfoming desktop integration during runtime
SLIC3R_DESKTOP_INTEGRATION:BOOL=ON

//Verify encoding of source files
SLIC3R_ENC_CHECK:BOOL=OFF

//Assume BambuStudio is to be installed in a FHS directory structure
SLIC3R_FHS:BOOL=OFF

//GTK version to use with wxWidgets on Linux
SLIC3R_GTK:STRING=2

//Compile BambuStudio with GUI components (OpenGL, wxWidgets)
SLIC3R_GUI:BOOL=ON

//Compile on Visual Studio in parallel
SLIC3R_MSVC_COMPILE_PARALLEL:BOOL=ON

//Generate PDB files on MSVC in Release mode
SLIC3R_MSVC_PDB:BOOL=ON

//Use precompiled headers
SLIC3R_PCH:BOOL=ON

//Compile XS Perl module and enable Perl unit and integration tests
SLIC3R_PERL_XS:BOOL=OFF

//Compile BambuStudio with an invasive Shiny profiler
SLIC3R_PROFILE:BOOL=OFF

//Compile BambuStudio with static libraries (Boost, TBB, glew)
SLIC3R_STATIC:BOOL=ON

//Build against wxWidgets stable (3.0) as oppsed to dev (3.1) on
// Linux
SLIC3R_WX_STABLE:BOOL=OFF

//Value Computed by CMake
Shiny_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/Shiny

//Value Computed by CMake
Shiny_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Shiny_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/Shiny

//The directory containing a CMake configuration file for TBB.
TBB_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/TBB

//Path to a file.
TIFF_INCLUDE_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include

//Path to a library.
TIFF_LIBRARY_DEBUG:FILEPATH=TIFF_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
TIFF_LIBRARY_RELEASE:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libtiff.a

//The directory containing a CMake configuration file for Tiff.
Tiff_DIR:PATH=Tiff_DIR-NOTFOUND

//Path to a file.
ZLIB_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include

//Path to a library.
ZLIB_LIBRARY_DEBUG:FILEPATH=ZLIB_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
ZLIB_LIBRARY_RELEASE:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libz.tbd

//Value Computed by CMake
admesh_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/admesh

//Value Computed by CMake
admesh_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
admesh_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/admesh

//The directory containing a CMake configuration file for boost_algorithm.
boost_algorithm_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_algorithm-1.84.0

//The directory containing a CMake configuration file for boost_align.
boost_align_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_align-1.84.0

//The directory containing a CMake configuration file for boost_array.
boost_array_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_array-1.84.0

//The directory containing a CMake configuration file for boost_asio.
boost_asio_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_asio-1.84.0

//The directory containing a CMake configuration file for boost_assert.
boost_assert_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_assert-1.84.0

//The directory containing a CMake configuration file for boost_atomic.
boost_atomic_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_atomic-1.84.0

//The directory containing a CMake configuration file for boost_bind.
boost_bind_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_bind-1.84.0

//The directory containing a CMake configuration file for boost_chrono.
boost_chrono_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_chrono-1.84.0

//The directory containing a CMake configuration file for boost_concept_check.
boost_concept_check_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_concept_check-1.84.0

//The directory containing a CMake configuration file for boost_config.
boost_config_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_config-1.84.0

//The directory containing a CMake configuration file for boost_container.
boost_container_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_container-1.84.0

//The directory containing a CMake configuration file for boost_container_hash.
boost_container_hash_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_container_hash-1.84.0

//The directory containing a CMake configuration file for boost_context.
boost_context_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_context-1.84.0

//The directory containing a CMake configuration file for boost_conversion.
boost_conversion_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_conversion-1.84.0

//The directory containing a CMake configuration file for boost_core.
boost_core_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_core-1.84.0

//The directory containing a CMake configuration file for boost_coroutine.
boost_coroutine_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_coroutine-1.84.0

//The directory containing a CMake configuration file for boost_date_time.
boost_date_time_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_date_time-1.84.0

//The directory containing a CMake configuration file for boost_describe.
boost_describe_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_describe-1.84.0

//The directory containing a CMake configuration file for boost_detail.
boost_detail_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_detail-1.84.0

//The directory containing a CMake configuration file for boost_dynamic_bitset.
boost_dynamic_bitset_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_dynamic_bitset-1.84.0

//The directory containing a CMake configuration file for boost_endian.
boost_endian_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_endian-1.84.0

//The directory containing a CMake configuration file for boost_exception.
boost_exception_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_exception-1.84.0

//The directory containing a CMake configuration file for boost_filesystem.
boost_filesystem_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_filesystem-1.84.0

//The directory containing a CMake configuration file for boost_function.
boost_function_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_function-1.84.0

//The directory containing a CMake configuration file for boost_function_types.
boost_function_types_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_function_types-1.84.0

//The directory containing a CMake configuration file for boost_functional.
boost_functional_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_functional-1.84.0

//The directory containing a CMake configuration file for boost_fusion.
boost_fusion_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_fusion-1.84.0

//The directory containing a CMake configuration file for boost_headers.
boost_headers_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_headers-1.84.0

//The directory containing a CMake configuration file for boost_integer.
boost_integer_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_integer-1.84.0

//The directory containing a CMake configuration file for boost_interprocess.
boost_interprocess_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_interprocess-1.84.0

//The directory containing a CMake configuration file for boost_intrusive.
boost_intrusive_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_intrusive-1.84.0

//The directory containing a CMake configuration file for boost_io.
boost_io_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_io-1.84.0

//The directory containing a CMake configuration file for boost_iostreams.
boost_iostreams_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0

//The directory containing a CMake configuration file for boost_iterator.
boost_iterator_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iterator-1.84.0

//The directory containing a CMake configuration file for boost_lexical_cast.
boost_lexical_cast_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_lexical_cast-1.84.0

//The directory containing a CMake configuration file for boost_locale.
boost_locale_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_locale-1.84.0

//The directory containing a CMake configuration file for boost_log.
boost_log_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_log-1.84.0

//The directory containing a CMake configuration file for boost_move.
boost_move_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_move-1.84.0

//The directory containing a CMake configuration file for boost_mp11.
boost_mp11_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_mp11-1.84.0

//The directory containing a CMake configuration file for boost_mpl.
boost_mpl_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_mpl-1.84.0

//The directory containing a CMake configuration file for boost_numeric_conversion.
boost_numeric_conversion_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_numeric_conversion-1.84.0

//The directory containing a CMake configuration file for boost_optional.
boost_optional_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_optional-1.84.0

//The directory containing a CMake configuration file for boost_parameter.
boost_parameter_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_parameter-1.84.0

//The directory containing a CMake configuration file for boost_phoenix.
boost_phoenix_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_phoenix-1.84.0

//The directory containing a CMake configuration file for boost_pool.
boost_pool_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_pool-1.84.0

//The directory containing a CMake configuration file for boost_predef.
boost_predef_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_predef-1.84.0

//The directory containing a CMake configuration file for boost_preprocessor.
boost_preprocessor_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_preprocessor-1.84.0

//The directory containing a CMake configuration file for boost_proto.
boost_proto_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_proto-1.84.0

//The directory containing a CMake configuration file for boost_random.
boost_random_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_random-1.84.0

//The directory containing a CMake configuration file for boost_range.
boost_range_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_range-1.84.0

//The directory containing a CMake configuration file for boost_ratio.
boost_ratio_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_ratio-1.84.0

//The directory containing a CMake configuration file for boost_regex.
boost_regex_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_regex-1.84.0

//The directory containing a CMake configuration file for boost_smart_ptr.
boost_smart_ptr_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_smart_ptr-1.84.0

//The directory containing a CMake configuration file for boost_spirit.
boost_spirit_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_spirit-1.84.0

//The directory containing a CMake configuration file for boost_static_assert.
boost_static_assert_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_static_assert-1.84.0

//The directory containing a CMake configuration file for boost_system.
boost_system_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_system-1.84.0

//The directory containing a CMake configuration file for boost_thread.
boost_thread_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_thread-1.84.0

//The directory containing a CMake configuration file for boost_throw_exception.
boost_throw_exception_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_throw_exception-1.84.0

//The directory containing a CMake configuration file for boost_tokenizer.
boost_tokenizer_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_tokenizer-1.84.0

//The directory containing a CMake configuration file for boost_tuple.
boost_tuple_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_tuple-1.84.0

//The directory containing a CMake configuration file for boost_type_index.
boost_type_index_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_type_index-1.84.0

//The directory containing a CMake configuration file for boost_type_traits.
boost_type_traits_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_type_traits-1.84.0

//The directory containing a CMake configuration file for boost_typeof.
boost_typeof_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_typeof-1.84.0

//The directory containing a CMake configuration file for boost_unordered.
boost_unordered_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_unordered-1.84.0

//The directory containing a CMake configuration file for boost_utility.
boost_utility_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_utility-1.84.0

//The directory containing a CMake configuration file for boost_variant2.
boost_variant2_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_variant2-1.84.0

//The directory containing a CMake configuration file for boost_variant.
boost_variant_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_variant-1.84.0

//The directory containing a CMake configuration file for boost_winapi.
boost_winapi_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_winapi-1.84.0

//The directory containing a CMake configuration file for cereal.
cereal_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/share/cmake/cereal

//Value Computed by CMake
clipper_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/clipper

//Value Computed by CMake
clipper_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
clipper_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/clipper

//The directory containing a CMake configuration file for glfw3.
glfw3_DIR:PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/glfw3

//Value Computed by CMake
glu-libtess_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/glu-libtess

//Value Computed by CMake
glu-libtess_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
glu-libtess_LIB_DEPENDS:STATIC=general;m;

//Value Computed by CMake
glu-libtess_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/glu-libtess

//Value Computed by CMake
imgui_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/imgui

//Value Computed by CMake
imgui_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
imgui_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/imgui

//Value Computed by CMake
imguizmo_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/imguizmo

//Value Computed by CMake
imguizmo_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
imguizmo_LIB_DEPENDS:STATIC=general;imgui;

//Value Computed by CMake
imguizmo_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/imguizmo

//Value Computed by CMake
libigl_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libigl

//The directory containing a CMake configuration file for libigl.
libigl_DIR:PATH=libigl_DIR-NOTFOUND

//Value Computed by CMake
libigl_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
libigl_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl

//Value Computed by CMake
libslic3r_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r

//Value Computed by CMake
libslic3r_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
libslic3r_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/libslic3r

//Value Computed by CMake
libslic3r_gui_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/slic3r

//Value Computed by CMake
libslic3r_gui_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
libslic3r_gui_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/slic3r

//Value Computed by CMake
mcut_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/mcut

//Value Computed by CMake
mcut_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
mcut_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut

//Value Computed by CMake
minilzo_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/minilzo

//Value Computed by CMake
minilzo_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
minilzo_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/minilzo

//Value Computed by CMake
miniz_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/miniz

//Value Computed by CMake
miniz_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
miniz_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/miniz

//Value Computed by CMake
nowide_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/boost

//Value Computed by CMake
nowide_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
nowide_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/boost

//Path to a library.
pkgcfg_lib_PC_EXPAT_expat:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libexpat.a

//Path to a library.
pkgcfg_lib__OPENSSL_crypto:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libcrypto.a

//Path to a library.
pkgcfg_lib__OPENSSL_ssl:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libssl.a

//Value Computed by CMake
semver_BINARY_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/semver

//Value Computed by CMake
semver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
semver_SOURCE_DIR:STATIC=/Users/<USER>/Documents/augment-projects/BambuStudio/src/semver

//Location of wxWidgets library configuration provider binary (wx-config).
wxWidgets_CONFIG_EXECUTABLE:FILEPATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/bin/wx-config

//Use debug build?
wxWidgets_USE_DEBUG:BOOL=OFF

//Link libraries statically?
wxWidgets_USE_STATIC:BOOL=ON

//Use unicode build?
wxWidgets_USE_UNICODE:BOOL=ON

//Location of wxWidgets resource file compiler binary (wxrc)
wxWidgets_wxrc_EXECUTABLE:FILEPATH=/opt/homebrew/bin/wxrc

//The directory containing a CMake configuration file for zstd.
zstd_DIR:PATH=/opt/homebrew/lib/cmake/zstd


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: BZIP2_INCLUDE_DIR
BZIP2_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BZIP2_LIBRARY_DEBUG
BZIP2_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BZIP2_LIBRARY_RELEASE
BZIP2_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//Have symbol BZ2_bzCompressInit
BZIP2_NEED_PREFIX:INTERNAL=1
//ADVANCED property for variable: Boost_DEBUG
Boost_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_DIR
Boost_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CGAL_Boost_USE_STATIC_LIBS
CGAL_Boost_USE_STATIC_LIBS-ADVANCED:INTERNAL=1
//CGAL library is configured to use GMP
CGAL_USE_GMP:INTERNAL=TRUE
//CGAL library is configured to use MPFR
CGAL_USE_MPFR:INTERNAL=TRUE
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=31
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=6
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/Applications/CMake.app/Contents/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=MACHO
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_BUILD_DATABASE
CMAKE_EXPORT_BUILD_DATABASE-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/Users/<USER>/Documents/augment-projects/BambuStudio
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=24
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_RPM
CPACK_SOURCE_RPM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_TBZ2
CPACK_SOURCE_TBZ2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_TGZ
CPACK_SOURCE_TGZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_TXZ
CPACK_SOURCE_TXZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_TZ
CPACK_SOURCE_TZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_ZIP
CPACK_SOURCE_ZIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: EXPAT_INCLUDE_DIR
EXPAT_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: EXPAT_LIBRARY_DEBUG
EXPAT_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: EXPAT_LIBRARY_RELEASE
EXPAT_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//Details about finding Boost
FIND_PACKAGE_MESSAGE_DETAILS_Boost:INTERNAL=[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfig.cmake][c ][v1.84.0(1.48)]
//Details about finding EXPAT
FIND_PACKAGE_MESSAGE_DETAILS_EXPAT:INTERNAL=[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libexpat.a][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include][v2.5.0()]
//Details about finding GLEW
FIND_PACKAGE_MESSAGE_DETAILS_GLEW:INTERNAL=[/opt/homebrew/lib/cmake/glew/glew-config.cmake][v()]
//Details about finding GMP
FIND_PACKAGE_MESSAGE_DETAILS_GMP:INTERNAL=[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libgmp.a][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include][v()]
//Details about finding MPFR
FIND_PACKAGE_MESSAGE_DETAILS_MPFR:INTERNAL=[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libmpfr.a][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include][v()]
//Details about finding OpenCV
FIND_PACKAGE_MESSAGE_DETAILS_OpenCV:INTERNAL=[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local][cfound components: core ][v4.6.0()]
//Details about finding OpenGL
FIND_PACKAGE_MESSAGE_DETAILS_OpenGL:INTERNAL=[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework][c ][v()]
//Details about finding OpenSSL
FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL:INTERNAL=[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libcrypto.a][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include][c ][v3.1.2(3)]
//Details about finding OpenVDB
FIND_PACKAGE_MESSAGE_DETAILS_OpenVDB:INTERNAL=[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libopenvdb.a][cfound components: openvdb ][v8.2.0(5.0)]
//Details about finding PNG
FIND_PACKAGE_MESSAGE_DETAILS_PNG:INTERNAL=[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libpng.a][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include][v1.6.35()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/opt/homebrew/bin/pkg-config][v2.3.0()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding ZLIB
FIND_PACKAGE_MESSAGE_DETAILS_ZLIB:INTERNAL=[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libz.tbd][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include][c ][v1.2.12()]
//Details about finding wxWidgets
FIND_PACKAGE_MESSAGE_DETAILS_wxWidgets:INTERNAL=[-L/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib;-pthread;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_gl-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_webview-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_aui-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_baseu_net-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_media-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_html-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_core-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_baseu-3.1.a;OpenGL.framework;OpenGL.framework;-framework WebKit;-framework AVFoundation;-framework CoreMedia;-weak_framework AVKit;-lwx_osx_cocoau_core-3.1;-lwx_baseu-3.1;libjpeg.a;libpng.a;libz.tbd;libtiff.a;-framework AudioToolbox;-framework WebKit;libz.tbd;-lwxregexu-3.1;libiconv.tbd;-framework CoreFoundation;-framework Security;-framework Carbon;-framework Cocoa;-framework IOKit;-framework QuartzCore][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/wx/include/osx_cocoa-unicode-static-3.1;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/wx-3.1][v3.1.5(3.1)]
//Test HAVE_CXX_ATOMICS64_WITHOUT_LIB
HAVE_CXX_ATOMICS64_WITHOUT_LIB:INTERNAL=1
//ADVANCED property for variable: Iconv_INCLUDE_DIR
Iconv_INCLUDE_DIR-ADVANCED:INTERNAL=1
//Test Iconv_IS_BUILT_IN
Iconv_IS_BUILT_IN:INTERNAL=
//ADVANCED property for variable: Iconv_LIBRARY
Iconv_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JPEG_INCLUDE_DIR
JPEG_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JPEG_LIBRARY_DEBUG
JPEG_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JPEG_LIBRARY_RELEASE
JPEG_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//Have library /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/liblzma.tbd
LIBLZMA_HAS_AUTO_DECODER:INTERNAL=1
//Have library /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/liblzma.tbd
LIBLZMA_HAS_EASY_ENCODER:INTERNAL=1
//Have library /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/liblzma.tbd
LIBLZMA_HAS_LZMA_PRESET:INTERNAL=1
//ADVANCED property for variable: LIBLZMA_INCLUDE_DIR
LIBLZMA_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LIBLZMA_LIBRARY_DEBUG
LIBLZMA_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LIBLZMA_LIBRARY_RELEASE
LIBLZMA_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//Test LLVM_HAS_ATOMICS
LLVM_HAS_ATOMICS:INTERNAL=1
//ADVANCED property for variable: NLopt_LIBS
NLopt_LIBS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_GLU_INCLUDE_DIR
OPENGL_GLU_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_INCLUDE_DIR
OPENGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gl_LIBRARY
OPENGL_gl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glu_LIBRARY
OPENGL_glu_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_CRYPTO_LIBRARY
OPENSSL_CRYPTO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_INCLUDE_DIR
OPENSSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_SSL_LIBRARY
OPENSSL_SSL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenVDB_INCLUDE_DIR
OpenVDB_INCLUDE_DIR-ADVANCED:INTERNAL=1
PC_EXPAT_CFLAGS:INTERNAL=
PC_EXPAT_CFLAGS_I:INTERNAL=
PC_EXPAT_CFLAGS_OTHER:INTERNAL=
PC_EXPAT_FOUND:INTERNAL=1
PC_EXPAT_INCLUDEDIR:INTERNAL=/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/usr/include
PC_EXPAT_INCLUDE_DIRS:INTERNAL=
PC_EXPAT_LDFLAGS:INTERNAL=-L/usr/lib;-lexpat
PC_EXPAT_LDFLAGS_OTHER:INTERNAL=
PC_EXPAT_LIBDIR:INTERNAL=/usr/lib
PC_EXPAT_LIBRARIES:INTERNAL=expat
PC_EXPAT_LIBRARY_DIRS:INTERNAL=/usr/lib
PC_EXPAT_LIBS:INTERNAL=
PC_EXPAT_LIBS_L:INTERNAL=
PC_EXPAT_LIBS_OTHER:INTERNAL=
PC_EXPAT_LIBS_PATHS:INTERNAL=
PC_EXPAT_MODULE_NAME:INTERNAL=expat
PC_EXPAT_PREFIX:INTERNAL=/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/usr
PC_EXPAT_STATIC_CFLAGS:INTERNAL=-DXML_STATIC
PC_EXPAT_STATIC_CFLAGS_I:INTERNAL=
PC_EXPAT_STATIC_CFLAGS_OTHER:INTERNAL=-DXML_STATIC
PC_EXPAT_STATIC_INCLUDE_DIRS:INTERNAL=
PC_EXPAT_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lexpat
PC_EXPAT_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_EXPAT_STATIC_LIBDIR:INTERNAL=
PC_EXPAT_STATIC_LIBRARIES:INTERNAL=expat
PC_EXPAT_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
PC_EXPAT_STATIC_LIBS:INTERNAL=
PC_EXPAT_STATIC_LIBS_L:INTERNAL=
PC_EXPAT_STATIC_LIBS_OTHER:INTERNAL=
PC_EXPAT_STATIC_LIBS_PATHS:INTERNAL=
PC_EXPAT_VERSION:INTERNAL=2.7.1
PC_EXPAT_expat_INCLUDEDIR:INTERNAL=
PC_EXPAT_expat_LIBDIR:INTERNAL=
PC_EXPAT_expat_PREFIX:INTERNAL=
PC_EXPAT_expat_VERSION:INTERNAL=
PC_OpenVDB_CFLAGS:INTERNAL=
PC_OpenVDB_CFLAGS_I:INTERNAL=
PC_OpenVDB_CFLAGS_OTHER:INTERNAL=
PC_OpenVDB_FOUND:INTERNAL=
PC_OpenVDB_INCLUDEDIR:INTERNAL=
PC_OpenVDB_LIBDIR:INTERNAL=
PC_OpenVDB_LIBS:INTERNAL=
PC_OpenVDB_LIBS_L:INTERNAL=
PC_OpenVDB_LIBS_OTHER:INTERNAL=
PC_OpenVDB_LIBS_PATHS:INTERNAL=
PC_OpenVDB_MODULE_NAME:INTERNAL=
PC_OpenVDB_OpenVDB_INCLUDEDIR:INTERNAL=
PC_OpenVDB_OpenVDB_LIBDIR:INTERNAL=
PC_OpenVDB_OpenVDB_PREFIX:INTERNAL=
PC_OpenVDB_OpenVDB_VERSION:INTERNAL=
PC_OpenVDB_PREFIX:INTERNAL=
PC_OpenVDB_STATIC_CFLAGS:INTERNAL=
PC_OpenVDB_STATIC_CFLAGS_I:INTERNAL=
PC_OpenVDB_STATIC_CFLAGS_OTHER:INTERNAL=
PC_OpenVDB_STATIC_LIBDIR:INTERNAL=
PC_OpenVDB_STATIC_LIBS:INTERNAL=
PC_OpenVDB_STATIC_LIBS_L:INTERNAL=
PC_OpenVDB_STATIC_LIBS_OTHER:INTERNAL=
PC_OpenVDB_STATIC_LIBS_PATHS:INTERNAL=
PC_OpenVDB_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PNG_LIBRARY_DEBUG
PNG_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PNG_LIBRARY_RELEASE
PNG_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PNG_PNG_INCLUDE_DIR
PNG_PNG_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TIFF_INCLUDE_DIR
TIFF_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TIFF_LIBRARY_DEBUG
TIFF_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TIFF_LIBRARY_RELEASE
TIFF_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_INCLUDE_DIR
ZLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_DEBUG
ZLIB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_RELEASE
ZLIB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/Users/<USER>/Documents/augment-projects/BambuStudio/install_dir
_OPENSSL_CFLAGS:INTERNAL=-I/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include
_OPENSSL_CFLAGS_I:INTERNAL=
_OPENSSL_CFLAGS_OTHER:INTERNAL=
_OPENSSL_FOUND:INTERNAL=1
_OPENSSL_INCLUDEDIR:INTERNAL=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include
_OPENSSL_INCLUDE_DIRS:INTERNAL=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include
_OPENSSL_LDFLAGS:INTERNAL=-L/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib;-lssl;-lcrypto
_OPENSSL_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_LIBDIR:INTERNAL=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib
_OPENSSL_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_LIBRARY_DIRS:INTERNAL=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib
_OPENSSL_LIBS:INTERNAL=
_OPENSSL_LIBS_L:INTERNAL=
_OPENSSL_LIBS_OTHER:INTERNAL=
_OPENSSL_LIBS_PATHS:INTERNAL=
_OPENSSL_MODULE_NAME:INTERNAL=openssl
_OPENSSL_PREFIX:INTERNAL=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local
_OPENSSL_STATIC_CFLAGS:INTERNAL=-I/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include
_OPENSSL_STATIC_CFLAGS_I:INTERNAL=
_OPENSSL_STATIC_CFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_INCLUDE_DIRS:INTERNAL=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include
_OPENSSL_STATIC_LDFLAGS:INTERNAL=-L/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib;-lssl;-lcrypto
_OPENSSL_STATIC_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBDIR:INTERNAL=
_OPENSSL_STATIC_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_STATIC_LIBRARY_DIRS:INTERNAL=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib
_OPENSSL_STATIC_LIBS:INTERNAL=
_OPENSSL_STATIC_LIBS_L:INTERNAL=
_OPENSSL_STATIC_LIBS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBS_PATHS:INTERNAL=
_OPENSSL_VERSION:INTERNAL=3.1.2
_OPENSSL_openssl_INCLUDEDIR:INTERNAL=
_OPENSSL_openssl_LIBDIR:INTERNAL=
_OPENSSL_openssl_PREFIX:INTERNAL=
_OPENSSL_openssl_VERSION:INTERNAL=
__pkg_config_arguments_PC_EXPAT:INTERNAL=QUIET;expat
__pkg_config_arguments__OPENSSL:INTERNAL=QUIET;openssl
__pkg_config_checked_PC_EXPAT:INTERNAL=1
__pkg_config_checked_PC_OpenVDB:INTERNAL=1
__pkg_config_checked__OPENSSL:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_EXPAT_expat
pkgcfg_lib_PC_EXPAT_expat-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_crypto
pkgcfg_lib__OPENSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_ssl
pkgcfg_lib__OPENSSL_ssl-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib

