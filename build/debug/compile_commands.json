[{"directory": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ -DBOOST_NO_CXX98_FUNCTION_BASE -DMCUT_WITH_COMPUTE_HELPER_THREADPOOL=1 -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DUNICODE -DWXINTL_NO_GETTEXT_MACRO -D_HAS_AUTO_PTR_ETC=0 -D_UNICODE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE -I/Users/<USER>/Documents/augment-projects/BambuStudio/src -I/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include -isystem /Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics -fsigned-char -DDEBUG -Werror=return-type -Wno-unused-function -Wno-unused-variable -Wno-unused-but-set-variable -Wno-unused-label -Wno-unused-local-typedefs -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-error=enum-constexpr-conversion -o src/mcut/CMakeFiles/mcut.dir/source/mcut.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/mcut.cpp", "file": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/mcut.cpp", "output": "src/mcut/CMakeFiles/mcut.dir/source/mcut.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ -DBOOST_NO_CXX98_FUNCTION_BASE -DMCUT_WITH_COMPUTE_HELPER_THREADPOOL=1 -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DUNICODE -DWXINTL_NO_GETTEXT_MACRO -D_HAS_AUTO_PTR_ETC=0 -D_UNICODE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE -I/Users/<USER>/Documents/augment-projects/BambuStudio/src -I/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include -isystem /Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics -fsigned-char -DDEBUG -Werror=return-type -Wno-unused-function -Wno-unused-variable -Wno-unused-but-set-variable -Wno-unused-label -Wno-unused-local-typedefs -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-error=enum-constexpr-conversion -o src/mcut/CMakeFiles/mcut.dir/source/kernel.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/kernel.cpp", "file": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/kernel.cpp", "output": "src/mcut/CMakeFiles/mcut.dir/source/kernel.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ -DBOOST_NO_CXX98_FUNCTION_BASE -DMCUT_WITH_COMPUTE_HELPER_THREADPOOL=1 -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DUNICODE -DWXINTL_NO_GETTEXT_MACRO -D_HAS_AUTO_PTR_ETC=0 -D_UNICODE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE -I/Users/<USER>/Documents/augment-projects/BambuStudio/src -I/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include -isystem /Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics -fsigned-char -DDEBUG -Werror=return-type -Wno-unused-function -Wno-unused-variable -Wno-unused-but-set-variable -Wno-unused-label -Wno-unused-local-typedefs -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-error=enum-constexpr-conversion -o src/mcut/CMakeFiles/mcut.dir/source/hmesh.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/hmesh.cpp", "file": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/hmesh.cpp", "output": "src/mcut/CMakeFiles/mcut.dir/source/hmesh.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ -DBOOST_NO_CXX98_FUNCTION_BASE -DMCUT_WITH_COMPUTE_HELPER_THREADPOOL=1 -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DUNICODE -DWXINTL_NO_GETTEXT_MACRO -D_HAS_AUTO_PTR_ETC=0 -D_UNICODE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE -I/Users/<USER>/Documents/augment-projects/BambuStudio/src -I/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include -isystem /Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics -fsigned-char -DDEBUG -Werror=return-type -Wno-unused-function -Wno-unused-variable -Wno-unused-but-set-variable -Wno-unused-label -Wno-unused-local-typedefs -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-error=enum-constexpr-conversion -o src/mcut/CMakeFiles/mcut.dir/source/math.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/math.cpp", "file": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/math.cpp", "output": "src/mcut/CMakeFiles/mcut.dir/source/math.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ -DBOOST_NO_CXX98_FUNCTION_BASE -DMCUT_WITH_COMPUTE_HELPER_THREADPOOL=1 -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DUNICODE -DWXINTL_NO_GETTEXT_MACRO -D_HAS_AUTO_PTR_ETC=0 -D_UNICODE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE -I/Users/<USER>/Documents/augment-projects/BambuStudio/src -I/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include -isystem /Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics -fsigned-char -DDEBUG -Werror=return-type -Wno-unused-function -Wno-unused-variable -Wno-unused-but-set-variable -Wno-unused-label -Wno-unused-local-typedefs -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-error=enum-constexpr-conversion -o src/mcut/CMakeFiles/mcut.dir/source/bvh.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/bvh.cpp", "file": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/bvh.cpp", "output": "src/mcut/CMakeFiles/mcut.dir/source/bvh.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc -DBOOST_NO_CXX98_FUNCTION_BASE -DMCUT_WITH_COMPUTE_HELPER_THREADPOOL=1 -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DUNICODE -DWXINTL_NO_GETTEXT_MACRO -D_HAS_AUTO_PTR_ETC=0 -D_UNICODE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE -I/Users/<USER>/Documents/augment-projects/BambuStudio/src -I/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include -isystem /Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen  -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics -fsigned-char -DDEBUG -Werror=return-type -Wno-unused-function -Wno-unused-variable -Wno-unused-but-set-variable -Wno-unused-label -Wno-unused-local-typedefs -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-error=enum-constexpr-conversion -o src/mcut/CMakeFiles/mcut.dir/source/shewchuk.c.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/shewchuk.c", "file": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/shewchuk.c", "output": "src/mcut/CMakeFiles/mcut.dir/source/shewchuk.c.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ -DBOOST_NO_CXX98_FUNCTION_BASE -DMCUT_WITH_COMPUTE_HELPER_THREADPOOL=1 -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DUNICODE -DWXINTL_NO_GETTEXT_MACRO -D_HAS_AUTO_PTR_ETC=0 -D_UNICODE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE -I/Users/<USER>/Documents/augment-projects/BambuStudio/src -I/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include -isystem /Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics -fsigned-char -DDEBUG -Werror=return-type -Wno-unused-function -Wno-unused-variable -Wno-unused-but-set-variable -Wno-unused-label -Wno-unused-local-typedefs -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-error=enum-constexpr-conversion -o src/mcut/CMakeFiles/mcut.dir/source/frontend.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/frontend.cpp", "file": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/frontend.cpp", "output": "src/mcut/CMakeFiles/mcut.dir/source/frontend.cpp.o"}, {"directory": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ -DBOOST_NO_CXX98_FUNCTION_BASE -DMCUT_WITH_COMPUTE_HELPER_THREADPOOL=1 -DSLIC3R_DESKTOP_INTEGRATION -DSLIC3R_GUI -DUNICODE -DWXINTL_NO_GETTEXT_MACRO -D_HAS_AUTO_PTR_ETC=0 -D_UNICODE -DwxNO_UNSAFE_WXSTRING_CONV -DwxUSE_UNICODE -I/Users/<USER>/Documents/augment-projects/BambuStudio/src -I/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl -I/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include -isystem /Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen  -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics -fsigned-char -DDEBUG -Werror=return-type -Wno-unused-function -Wno-unused-variable -Wno-unused-but-set-variable -Wno-unused-label -Wno-unused-local-typedefs -Wno-ignored-attributes -Wno-deprecated-declarations -Wno-error=enum-constexpr-conversion -o src/mcut/CMakeFiles/mcut.dir/source/preproc.cpp.o -c /Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/preproc.cpp", "file": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/source/preproc.cpp", "output": "src/mcut/CMakeFiles/mcut.dir/source/preproc.cpp.o"}]