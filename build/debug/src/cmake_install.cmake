# Install script for directory: /Users/<USER>/Documents/augment-projects/BambuStudio/src

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/usr/local")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Debug")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set path to fallback-tool for dependency-resolution.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/BambuStudio")
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/BambuStudio" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/BambuStudio")
    execute_process(COMMAND /usr/bin/install_name_tool
      -delete_rpath "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib"
      -delete_rpath "/opt/homebrew/lib"
      "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/BambuStudio")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip" -u -r "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/BambuStudio")
    endif()
  endif()
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for each subdirectory.
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/build-utils/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/admesh/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/boost/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/clipper/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/clipper2/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/miniz/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/minilzo/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/glu-libtess/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/qhull/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/Shiny/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/semver/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libigl/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/hints/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/mcut/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libnest2d/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/imgui/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/imguizmo/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/hidapi/cmake_install.cmake")
  include("/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/slic3r/cmake_install.cmake")

endif()

string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
if(CMAKE_INSTALL_LOCAL_ONLY)
  file(WRITE "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/install_local_manifest.txt"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
endif()
