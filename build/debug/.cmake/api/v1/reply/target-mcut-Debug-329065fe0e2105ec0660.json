{"archive": {}, "artifacts": [{"path": "src/mcut/libmcutd.a"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_library", "create_library_target", "add_compile_options", "add_compile_definitions", "target_compile_definitions", "add_definitions", "include_directories", "target_include_directories"], "files": ["src/mcut/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 1, "file": 0, "line": 208, "parent": 0}, {"command": 0, "file": 0, "line": 177, "parent": 1}, {"file": 1}, {"command": 2, "file": 1, "line": 139, "parent": 3}, {"command": 2, "file": 1, "line": 173, "parent": 3}, {"command": 2, "file": 1, "line": 257, "parent": 3}, {"command": 2, "file": 1, "line": 259, "parent": 3}, {"command": 2, "file": 1, "line": 264, "parent": 3}, {"command": 2, "file": 1, "line": 271, "parent": 3}, {"command": 2, "file": 1, "line": 275, "parent": 3}, {"command": 3, "file": 1, "line": 130, "parent": 3}, {"command": 4, "file": 0, "line": 182, "parent": 1}, {"command": 5, "file": 1, "line": 96, "parent": 3}, {"command": 5, "file": 1, "line": 92, "parent": 3}, {"command": 5, "file": 1, "line": 339, "parent": 3}, {"command": 5, "file": 1, "line": 342, "parent": 3}, {"command": 6, "file": 1, "line": 322, "parent": 3}, {"command": 6, "file": 1, "line": 324, "parent": 3}, {"command": 6, "file": 1, "line": 326, "parent": 3}, {"command": 7, "file": 0, "line": 179, "parent": 1}, {"command": 6, "file": 1, "line": 491, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++11 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics"}, {"backtrace": 4, "fragment": "-f<PERSON>-char"}, {"backtrace": 5, "fragment": "-DDEBUG"}, {"backtrace": 6, "fragment": "-Werror=return-type"}, {"backtrace": 7, "fragment": "-Wno-unused-function"}, {"backtrace": 7, "fragment": "-Wno-unused-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-label"}, {"backtrace": 7, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 8, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 9, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 10, "fragment": "-Wno-error=enum-constexpr-conversion"}], "defines": [{"backtrace": 11, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 12, "define": "MCUT_WITH_COMPUTE_HELPER_THREADPOOL=1"}, {"backtrace": 13, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 14, "define": "SLIC3R_GUI"}, {"backtrace": 15, "define": "UNICODE"}, {"backtrace": 15, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 11, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 15, "define": "_UNICODE"}, {"backtrace": 16, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 15, "define": "wxUSE_UNICODE"}], "includes": [{"backtrace": 17, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 18, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 19, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 20, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include"}, {"backtrace": 21, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "11"}, "sourceIndexes": [0, 1, 2, 3, 4, 6, 7]}, {"compileCommandFragments": [{"fragment": " -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics"}, {"backtrace": 4, "fragment": "-f<PERSON>-char"}, {"backtrace": 5, "fragment": "-DDEBUG"}, {"backtrace": 6, "fragment": "-Werror=return-type"}, {"backtrace": 7, "fragment": "-Wno-unused-function"}, {"backtrace": 7, "fragment": "-Wno-unused-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-label"}, {"backtrace": 7, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 8, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 9, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 10, "fragment": "-Wno-error=enum-constexpr-conversion"}], "defines": [{"backtrace": 11, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 12, "define": "MCUT_WITH_COMPUTE_HELPER_THREADPOOL=1"}, {"backtrace": 13, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 14, "define": "SLIC3R_GUI"}, {"backtrace": 15, "define": "UNICODE"}, {"backtrace": 15, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 11, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 15, "define": "_UNICODE"}, {"backtrace": 16, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 15, "define": "wxUSE_UNICODE"}], "includes": [{"backtrace": 17, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 18, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 19, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 20, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include"}, {"backtrace": 21, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}], "language": "C", "sourceIndexes": [5]}], "id": "mcut::@a392ea28d34c20f491b4", "name": "mcut", "nameOnDisk": "libmcutd.a", "paths": {"build": "src/mcut", "source": "src/mcut"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "src/mcut/source/mcut.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/mcut/source/kernel.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/mcut/source/hmesh.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/mcut/source/math.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/mcut/source/bvh.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 1, "path": "src/mcut/source/shewchuk.c", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/mcut/source/frontend.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/mcut/source/preproc.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}