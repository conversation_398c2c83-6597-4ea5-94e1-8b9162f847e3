{"archive": {}, "artifacts": [{"path": "src/libnest2d/liblibnest2d.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_compile_options", "target_link_libraries", "add_compile_definitions", "target_compile_definitions", "add_definitions", "include_directories", "target_include_directories"], "files": ["src/libnest2d/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 24, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 139, "parent": 2}, {"command": 1, "file": 1, "line": 173, "parent": 2}, {"command": 1, "file": 1, "line": 257, "parent": 2}, {"command": 1, "file": 1, "line": 259, "parent": 2}, {"command": 1, "file": 1, "line": 264, "parent": 2}, {"command": 1, "file": 1, "line": 271, "parent": 2}, {"command": 1, "file": 1, "line": 275, "parent": 2}, {"command": 2, "file": 0, "line": 27, "parent": 0}, {"command": 3, "file": 1, "line": 130, "parent": 2}, {"command": 4, "file": 0, "line": 28, "parent": 0}, {"command": 5, "file": 1, "line": 96, "parent": 2}, {"command": 5, "file": 1, "line": 92, "parent": 2}, {"command": 5, "file": 1, "line": 339, "parent": 2}, {"command": 5, "file": 1, "line": 342, "parent": 2}, {"command": 6, "file": 1, "line": 322, "parent": 2}, {"command": 6, "file": 1, "line": 324, "parent": 2}, {"command": 7, "file": 0, "line": 26, "parent": 0}, {"command": 6, "file": 1, "line": 491, "parent": 2}, {"command": 6, "file": 1, "line": 326, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics"}, {"backtrace": 3, "fragment": "-f<PERSON>-char"}, {"backtrace": 4, "fragment": "-DDEBUG"}, {"backtrace": 5, "fragment": "-Werror=return-type"}, {"backtrace": 6, "fragment": "-Wno-unused-function"}, {"backtrace": 6, "fragment": "-Wno-unused-variable"}, {"backtrace": 6, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 6, "fragment": "-Wno-unused-label"}, {"backtrace": 6, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 7, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 8, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 9, "fragment": "-Wno-error=enum-constexpr-conversion"}, {"backtrace": 10, "fragment": "-DOPENVDB_ABI_VERSION_NUMBER=8"}], "defines": [{"backtrace": 10, "define": "BOOST_ASIO_DISABLE_KQUEUE"}, {"backtrace": 10, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 10, "define": "BOOST_ATOMIC_STATIC_LINK"}, {"backtrace": 10, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 10, "define": "BOOST_CHRONO_STATIC_LINK"}, {"backtrace": 10, "define": "BOOST_CONTAINER_NO_LIB"}, {"backtrace": 10, "define": "BOOST_CONTAINER_STATIC_LINK"}, {"backtrace": 10, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 10, "define": "BOOST_DATE_TIME_STATIC_LINK"}, {"backtrace": 10, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 10, "define": "BOOST_FILESYSTEM_STATIC_LINK=1"}, {"backtrace": 10, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 10, "define": "BOOST_IOSTREAMS_STATIC_LINK"}, {"backtrace": 10, "define": "BOOST_LOCALE_NO_LIB"}, {"backtrace": 10, "define": "BOOST_LOCALE_STATIC_LINK"}, {"backtrace": 10, "define": "BOOST_LOG_NO_LIB"}, {"backtrace": 10, "define": "BOOST_LOG_STATIC_LINK"}, {"backtrace": 11, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 10, "define": "BOOST_RANDOM_NO_LIB"}, {"backtrace": 10, "define": "BOOST_RANDOM_STATIC_LINK"}, {"backtrace": 10, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 10, "define": "BOOST_THREAD_STATIC_LINK"}, {"backtrace": 10, "define": "BOOST_THREAD_USE_LIB"}, {"backtrace": 12, "define": "LIBNEST2D_GEOMETRIES_libslic3r"}, {"backtrace": 12, "define": "LIBNEST2D_OPTIMIZER_nlopt"}, {"backtrace": 12, "define": "LIBNEST2D_STATIC"}, {"backtrace": 12, "define": "LIBNEST2D_THREADING_tbb"}, {"backtrace": 10, "define": "OPENVDB_OPENEXR_STATICLIB"}, {"backtrace": 10, "define": "OPENVDB_STATICLIB"}, {"backtrace": 13, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 14, "define": "SLIC3R_GUI"}, {"backtrace": 10, "define": "TBB_USE_CAPTURED_EXCEPTION=0"}, {"backtrace": 10, "define": "TBB_USE_DEBUG"}, {"backtrace": 15, "define": "UNICODE"}, {"backtrace": 10, "define": "USE_TBB"}, {"backtrace": 15, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 11, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 15, "define": "_UNICODE"}, {"backtrace": 16, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 15, "define": "wxUSE_UNICODE"}], "includes": [{"backtrace": 17, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 18, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 19, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libnest2d/include"}, {"backtrace": 10, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r"}, {"backtrace": 10, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencascade"}, {"backtrace": 10, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/miniz"}, {"backtrace": 10, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/glu-libtess/include"}, {"backtrace": 10, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/clipper2/Clipper2Lib/include"}, {"backtrace": 20, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}, {"backtrace": 21, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 10, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"backtrace": 10, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencv4"}, {"backtrace": 10, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/OpenEXR"}], "language": "CXX", "languageStandard": {"backtraces": [10, 10, 10, 10, 10, 10], "standard": "17"}, "sourceIndexes": [19]}], "dependencies": [{"id": "libslic3r::@87deb467dcbeda20dc6b"}], "id": "libnest2d::@8f4a5c5a495c7f0c1447", "name": "libnest2d", "nameOnDisk": "liblibnest2d.a", "paths": {"build": "src/libnest2d", "source": "src/libnest2d"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}, {"name": "Source Files", "sourceIndexes": [19]}], "sources": [{"backtrace": 1, "path": "src/libnest2d/include/libnest2d/libnest2d.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/nester.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/geometry_traits.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/geometry_traits_nfp.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/common.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/optimizer.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/utils/metaloop.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/utils/rotfinder.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/utils/rotcalipers.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/placers/placer_boilerplate.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/placers/bottomleftplacer.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/placers/nfpplacer.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/selections/selection_boilerplate.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/selections/firstfit.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/backends/libslic3r/geometries.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/optimizers/nlopt/nlopt_boilerplate.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/optimizers/nlopt/simplex.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/optimizers/nlopt/subplex.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libnest2d/include/libnest2d/optimizers/nlopt/genetic.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/libnest2d/src/libnest2d.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}