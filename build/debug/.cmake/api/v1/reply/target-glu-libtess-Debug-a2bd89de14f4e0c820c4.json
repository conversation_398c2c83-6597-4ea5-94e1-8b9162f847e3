{"archive": {}, "artifacts": [{"path": "src/glu-libtess/libglu-libtess.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_compile_options", "add_compile_definitions", "add_definitions", "include_directories", "target_include_directories"], "files": ["src/glu-libtess/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 139, "parent": 2}, {"command": 1, "file": 1, "line": 173, "parent": 2}, {"command": 1, "file": 1, "line": 257, "parent": 2}, {"command": 1, "file": 1, "line": 259, "parent": 2}, {"command": 1, "file": 1, "line": 264, "parent": 2}, {"command": 1, "file": 1, "line": 271, "parent": 2}, {"command": 1, "file": 1, "line": 275, "parent": 2}, {"command": 2, "file": 1, "line": 130, "parent": 2}, {"command": 3, "file": 1, "line": 96, "parent": 2}, {"command": 3, "file": 1, "line": 92, "parent": 2}, {"command": 3, "file": 1, "line": 339, "parent": 2}, {"command": 3, "file": 1, "line": 342, "parent": 2}, {"command": 4, "file": 1, "line": 322, "parent": 2}, {"command": 4, "file": 1, "line": 324, "parent": 2}, {"command": 4, "file": 1, "line": 326, "parent": 2}, {"command": 5, "file": 0, "line": 36, "parent": 0}, {"command": 4, "file": 1, "line": 491, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics"}, {"backtrace": 3, "fragment": "-f<PERSON>-char"}, {"backtrace": 4, "fragment": "-DDEBUG"}, {"backtrace": 5, "fragment": "-Werror=return-type"}, {"backtrace": 6, "fragment": "-Wno-unused-function"}, {"backtrace": 6, "fragment": "-Wno-unused-variable"}, {"backtrace": 6, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 6, "fragment": "-Wno-unused-label"}, {"backtrace": 6, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 7, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 8, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 9, "fragment": "-Wno-error=enum-constexpr-conversion"}], "defines": [{"backtrace": 10, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 11, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 12, "define": "SLIC3R_GUI"}, {"backtrace": 13, "define": "UNICODE"}, {"backtrace": 13, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 10, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 13, "define": "_UNICODE"}, {"backtrace": 14, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 13, "define": "wxUSE_UNICODE"}], "includes": [{"backtrace": 15, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 16, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 17, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 18, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/glu-libtess"}, {"backtrace": 18, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/glu-libtess/include"}, {"backtrace": 19, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}], "language": "C", "sourceIndexes": [1, 3, 6, 8, 10, 12, 16, 18, 20, 22]}], "id": "glu-libtess::@86057b0eb26356d52b06", "name": "glu-libtess", "nameOnDisk": "libglu-libtess.a", "paths": {"build": "src/glu-libtess", "source": "src/glu-libtess"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 2, 4, 5, 7, 9, 11, 13, 14, 15, 17, 19, 21, 23, 24]}, {"name": "Source Files", "sourceIndexes": [1, 3, 6, 8, 10, 12, 16, 18, 20, 22]}], "sources": [{"backtrace": 1, "path": "src/glu-libtess/src/dict-list.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/glu-libtess/src/dict.c", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/glu-libtess/src/dict.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/glu-libtess/src/geom.c", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/glu-libtess/src/geom.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/glu-libtess/src/gluos.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/glu-libtess/src/memalloc.c", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/glu-libtess/src/memalloc.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/glu-libtess/src/mesh.c", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/glu-libtess/src/mesh.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/glu-libtess/src/normal.c", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/glu-libtess/src/normal.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/glu-libtess/src/priorityq.c", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/glu-libtess/src/priorityq.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/glu-libtess/src/priorityq-heap.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/glu-libtess/src/priorityq-sort.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/glu-libtess/src/render.c", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/glu-libtess/src/render.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/glu-libtess/src/sweep.c", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/glu-libtess/src/sweep.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/glu-libtess/src/tess.c", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/glu-libtess/src/tess.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/glu-libtess/src/tessmono.c", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/glu-libtess/src/tessmono.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/glu-libtess/include/glu-libtess.h", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}