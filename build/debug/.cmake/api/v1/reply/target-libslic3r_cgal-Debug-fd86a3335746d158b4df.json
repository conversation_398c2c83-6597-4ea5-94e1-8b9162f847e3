{"archive": {}, "artifacts": [{"path": "src/libslic3r/liblibslic3r_cgal.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_options", "add_compile_definitions", "add_definitions", "include_directories", "target_include_directories"], "files": ["src/libslic3r/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 496, "parent": 0}, {"command": 1, "file": 0, "line": 527, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 139, "parent": 3}, {"command": 2, "file": 1, "line": 173, "parent": 3}, {"command": 2, "file": 1, "line": 257, "parent": 3}, {"command": 2, "file": 1, "line": 259, "parent": 3}, {"command": 2, "file": 1, "line": 264, "parent": 3}, {"command": 2, "file": 1, "line": 271, "parent": 3}, {"command": 2, "file": 1, "line": 275, "parent": 3}, {"command": 3, "file": 1, "line": 130, "parent": 3}, {"command": 4, "file": 1, "line": 96, "parent": 3}, {"command": 4, "file": 1, "line": 92, "parent": 3}, {"command": 4, "file": 1, "line": 339, "parent": 3}, {"command": 4, "file": 1, "line": 342, "parent": 3}, {"command": 5, "file": 1, "line": 322, "parent": 3}, {"command": 5, "file": 1, "line": 324, "parent": 3}, {"command": 6, "file": 0, "line": 502, "parent": 0}, {"command": 5, "file": 1, "line": 491, "parent": 3}, {"command": 5, "file": 1, "line": 326, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics"}, {"backtrace": 4, "fragment": "-f<PERSON>-char"}, {"backtrace": 5, "fragment": "-DDEBUG"}, {"backtrace": 6, "fragment": "-Werror=return-type"}, {"backtrace": 7, "fragment": "-Wno-unused-function"}, {"backtrace": 7, "fragment": "-Wno-unused-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-label"}, {"backtrace": 7, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 8, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 9, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 10, "fragment": "-Wno-error=enum-constexpr-conversion"}], "defines": [{"backtrace": 11, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 2, "define": "CGAL_USE_GMPXX=1"}, {"backtrace": 12, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 13, "define": "SLIC3R_GUI"}, {"backtrace": 14, "define": "UNICODE"}, {"backtrace": 14, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 11, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 14, "define": "_UNICODE"}, {"backtrace": 15, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 14, "define": "wxUSE_UNICODE"}], "includes": [{"backtrace": 16, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 17, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 18, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r"}, {"backtrace": 19, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}, {"backtrace": 20, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [1, 3, 5, 7, 9]}], "dependencies": [{"backtrace": 2, "id": "mcut::@a392ea28d34c20f491b4"}], "id": "libslic3r_cgal::@87deb467dcbeda20dc6b", "name": "libslic3r_cgal", "nameOnDisk": "liblibslic3r_cgal.a", "paths": {"build": "src/libslic3r", "source": "src/libslic3r"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 2, 4, 6, 8]}, {"name": "Source Files", "sourceIndexes": [1, 3, 5, 7, 9]}], "sources": [{"backtrace": 1, "path": "src/libslic3r/CutSurface.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/libslic3r/CutSurface.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/IntersectionPoints.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/libslic3r/IntersectionPoints.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/MeshBoolean.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/libslic3r/MeshBoolean.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/TryCatchSignal.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/libslic3r/TryCatchSignal.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Triangulation.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/libslic3r/Triangulation.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}