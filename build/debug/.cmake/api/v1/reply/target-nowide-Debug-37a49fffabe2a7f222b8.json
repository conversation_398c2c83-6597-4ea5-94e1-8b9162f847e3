{"archive": {}, "artifacts": [{"path": "src/boost/libnowide.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_compile_options", "target_link_libraries", "add_compile_definitions", "add_definitions", "include_directories"], "files": ["src/boost/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 139, "parent": 2}, {"command": 1, "file": 1, "line": 173, "parent": 2}, {"command": 1, "file": 1, "line": 257, "parent": 2}, {"command": 1, "file": 1, "line": 259, "parent": 2}, {"command": 1, "file": 1, "line": 264, "parent": 2}, {"command": 1, "file": 1, "line": 271, "parent": 2}, {"command": 1, "file": 1, "line": 275, "parent": 2}, {"command": 2, "file": 0, "line": 22, "parent": 0}, {"command": 3, "file": 1, "line": 130, "parent": 2}, {"command": 4, "file": 1, "line": 96, "parent": 2}, {"command": 4, "file": 1, "line": 92, "parent": 2}, {"command": 4, "file": 1, "line": 339, "parent": 2}, {"command": 4, "file": 1, "line": 342, "parent": 2}, {"command": 5, "file": 1, "line": 322, "parent": 2}, {"command": 5, "file": 1, "line": 324, "parent": 2}, {"command": 5, "file": 1, "line": 326, "parent": 2}, {"command": 5, "file": 1, "line": 491, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics"}, {"backtrace": 3, "fragment": "-f<PERSON>-char"}, {"backtrace": 4, "fragment": "-DDEBUG"}, {"backtrace": 5, "fragment": "-Werror=return-type"}, {"backtrace": 6, "fragment": "-Wno-unused-function"}, {"backtrace": 6, "fragment": "-Wno-unused-variable"}, {"backtrace": 6, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 6, "fragment": "-Wno-unused-label"}, {"backtrace": 6, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 7, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 8, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 9, "fragment": "-Wno-error=enum-constexpr-conversion"}], "defines": [{"backtrace": 10, "define": "BOOST_ASIO_DISABLE_KQUEUE"}, {"backtrace": 11, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 12, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 13, "define": "SLIC3R_GUI"}, {"backtrace": 14, "define": "UNICODE"}, {"backtrace": 14, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 11, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 14, "define": "_UNICODE"}, {"backtrace": 15, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 14, "define": "wxUSE_UNICODE"}], "includes": [{"backtrace": 16, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 17, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 18, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 19, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}, {"backtrace": 10, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [9]}], "id": "nowide::@34b20850f9a50713ec38", "name": "nowide", "nameOnDisk": "libnowide.a", "paths": {"build": "src/boost", "source": "src/boost"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14]}, {"name": "Source Files", "sourceIndexes": [9]}], "sources": [{"backtrace": 1, "path": "src/boost/nowide/args.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/boost/nowide/cenv.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/boost/nowide/config.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/boost/nowide/convert.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/boost/nowide/cstdio.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/boost/nowide/cstdlib.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/boost/nowide/filebuf.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/boost/nowide/fstream.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/boost/nowide/integration/filesystem.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/boost/nowide/iostream.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/boost/nowide/iostream.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/boost/nowide/stackstring.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/boost/nowide/system.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/boost/nowide/utf8_codecvt.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/boost/nowide/windows.hpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}