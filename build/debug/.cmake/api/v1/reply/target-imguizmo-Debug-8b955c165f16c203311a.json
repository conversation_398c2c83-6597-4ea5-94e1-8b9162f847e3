{"archive": {}, "artifacts": [{"path": "src/imguizmo/libimguizmo.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_options", "add_compile_definitions", "add_definitions", "include_directories"], "files": ["src/imguizmo/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}, {"command": 1, "file": 0, "line": 9, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 139, "parent": 3}, {"command": 2, "file": 1, "line": 173, "parent": 3}, {"command": 2, "file": 1, "line": 257, "parent": 3}, {"command": 2, "file": 1, "line": 259, "parent": 3}, {"command": 2, "file": 1, "line": 264, "parent": 3}, {"command": 2, "file": 1, "line": 271, "parent": 3}, {"command": 2, "file": 1, "line": 275, "parent": 3}, {"command": 3, "file": 1, "line": 130, "parent": 3}, {"command": 4, "file": 1, "line": 96, "parent": 3}, {"command": 4, "file": 1, "line": 92, "parent": 3}, {"command": 4, "file": 1, "line": 339, "parent": 3}, {"command": 4, "file": 1, "line": 342, "parent": 3}, {"command": 5, "file": 1, "line": 322, "parent": 3}, {"command": 5, "file": 1, "line": 324, "parent": 3}, {"command": 5, "file": 1, "line": 326, "parent": 3}, {"command": 5, "file": 1, "line": 491, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics"}, {"backtrace": 4, "fragment": "-f<PERSON>-char"}, {"backtrace": 5, "fragment": "-DDEBUG"}, {"backtrace": 6, "fragment": "-Werror=return-type"}, {"backtrace": 7, "fragment": "-Wno-unused-function"}, {"backtrace": 7, "fragment": "-Wno-unused-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-label"}, {"backtrace": 7, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 8, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 9, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 10, "fragment": "-Wno-error=enum-constexpr-conversion"}], "defines": [{"backtrace": 11, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 12, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 13, "define": "SLIC3R_GUI"}, {"backtrace": 14, "define": "UNICODE"}, {"backtrace": 14, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 11, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 14, "define": "_UNICODE"}, {"backtrace": 15, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 14, "define": "wxUSE_UNICODE"}], "includes": [{"backtrace": 16, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 17, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 18, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 19, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [1]}], "dependencies": [{"backtrace": 2, "id": "imgui::@f0bb1e571c12c8d458e0"}], "id": "imguizmo::@67d194951d60576ffe05", "name": "<PERSON><PERSON><PERSON><PERSON>", "nameOnDisk": "libimguizmo.a", "paths": {"build": "src/imguizmo", "source": "src/imguizmo"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "path": "src/imguizmo/ImGuizmo.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/imguizmo/ImGuizmo.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}