{"entries": [{"name": "BUILD_BBS_TEST_TOOLS", "properties": [{"name": "HELPSTRING", "value": "Build bbs test tools"}], "type": "BOOL", "value": "OFF"}, {"name": "BUILD_SHARED_LIBS", "properties": [{"name": "HELPSTRING", "value": "Build shared libs"}], "type": "BOOL", "value": "OFF"}, {"name": "BUILD_TESTING", "properties": [{"name": "HELPSTRING", "value": "Build the testing tree."}], "type": "BOOL", "value": "OFF"}, {"name": "BZIP2_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include"}, {"name": "BZIP2_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BZIP2_LIBRARY_DEBUG-NOTFOUND"}, {"name": "BZIP2_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libbz2.tbd"}, {"name": "BZIP2_NEED_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Have symbol BZ2_bzCompressInit"}], "type": "INTERNAL", "value": "1"}, {"name": "BambuStudio-native_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src"}, {"name": "BambuStudio-native_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "BambuStudio-native_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"name": "BambuStudio_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug"}, {"name": "BambuStudio_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "BambuStudio_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio"}, {"name": "Blosc_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Blosc."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Blosc"}, {"name": "Boost_ATOMIC_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_atomic.a"}, {"name": "Boost_CHRONO_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_chrono.a"}, {"name": "Boost_DATE_TIME_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_date_time.a"}, {"name": "Boost_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Activate the debug messages of the script FindBoost"}], "type": "BOOL", "value": "OFF"}, {"name": "Boost_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Boost."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0"}, {"name": "Boost_FILESYSTEM_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_filesystem.a"}, {"name": "Boost_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "Boost_IOSTREAMS_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_iostreams.a"}, {"name": "Boost_LOCALE_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_locale.a"}, {"name": "Boost_LOG_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_log.a"}, {"name": "Boost_THREAD_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_thread.a"}, {"name": "CGAL_Boost_USE_STATIC_LIBS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Link with static Boost libraries"}], "type": "BOOL", "value": "ON"}, {"name": "CGAL_CTEST_DISPLAY_MEM_AND_TIME", "properties": [{"name": "HELPSTRING", "value": "Display memory and real time usage at end of CTest test outputs"}], "type": "BOOL", "value": "OFF"}, {"name": "CGAL_DEV_MODE", "properties": [{"name": "HELPSTRING", "value": "Activate the CGAL developers mode. See https://github.com/CGAL/cgal/wiki/CGAL_DEV_MODE"}], "type": "BOOL", "value": "OFF"}, {"name": "CGAL_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for CGAL."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL"}, {"name": "CGAL_DO_NOT_WARN_ABOUT_CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "BOOL", "value": "ON"}, {"name": "CGAL_TEST_DRAW_FUNCTIONS", "properties": [{"name": "HELPSTRING", "value": "If set, the ctest command will not skip the tests of the draw functions."}], "type": "BOOL", "value": "OFF"}, {"name": "CGAL_USE_GMP", "properties": [{"name": "HELPSTRING", "value": "CGAL library is configured to use GMP"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "CGAL_USE_MPFR", "properties": [{"name": "HELPSTRING", "value": "CGAL library is configured to use MPFR"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "CGAL_WITH_GMPXX", "properties": [{"name": "HELPSTRING", "value": "Use CGAL with GMPXX: use C++ classes of GNU MP instead of CGAL wrappers"}], "type": "BOOL", "value": "ON"}, {"name": "CMAKE_ADDR2LINE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_ADDR2LINE-NOTFOUND"}, {"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."}], "type": "STRING", "value": "Debug"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "31"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "6"}, {"name": "CMAKE_COLOR_DIAGNOSTICS", "properties": [{"name": "HELPSTRING", "value": "Enable colored diagnostics throughout."}], "type": "BOOL", "value": "ON"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cmake"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cpack"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/ctest"}, {"name": "CMAKE_CXX_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler"}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_C_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler"}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_DLLTOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_DLLTOOL-NOTFOUND"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "MACHO"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXPORT_BUILD_DATABASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable output of build database during the build."}], "type": "BOOL", "value": ""}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable output of compile commands during generation."}], "type": "BOOL", "value": ""}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Ninja"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_LIBC_PTHREAD", "properties": [{"name": "HELPSTRING", "value": "Test CMAKE_HAVE_LIBC_PTHREAD"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio"}, {"name": "CMAKE_INSTALL_BINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "User executables (bin)"}], "type": "PATH", "value": "bin"}, {"name": "CMAKE_INSTALL_DATADIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data (DATAROOTDIR)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_DATAROOTDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data root (share)"}], "type": "PATH", "value": "share"}, {"name": "CMAKE_INSTALL_DOCDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Documentation root (DATAROOTDIR/doc/PROJECT_NAME)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_INCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files (include)"}], "type": "PATH", "value": "include"}, {"name": "CMAKE_INSTALL_INFODIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Info documentation (DATAROOTDIR/info)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LIBDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Object code libraries (lib)"}], "type": "PATH", "value": "lib"}, {"name": "CMAKE_INSTALL_LIBEXECDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program executables (libexec)"}], "type": "PATH", "value": "libexec"}, {"name": "CMAKE_INSTALL_LOCALEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Locale-dependent data (DATAROOTDIR/locale)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LOCALSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable single-machine data (var)"}], "type": "PATH", "value": "var"}, {"name": "CMAKE_INSTALL_MANDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Man documentation (DATAROOTDIR/man)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_NAME_TOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/install_name_tool"}, {"name": "CMAKE_INSTALL_OLDINCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files for non-gcc (/usr/include)"}], "type": "PATH", "value": "/usr/include"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "/usr/local"}, {"name": "CMAKE_INSTALL_RUNSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Run-time variable data (LOCALSTATEDIR/run)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_SBINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "System admin executables (sbin)"}], "type": "PATH", "value": "sbin"}, {"name": "CMAKE_INSTALL_SHAREDSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable architecture-independent data (com)"}], "type": "PATH", "value": "com"}, {"name": "CMAKE_INSTALL_SYSCONFDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only single-machine data (etc)"}], "type": "PATH", "value": "etc"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld"}, {"name": "CMAKE_MAKE_PROGRAM", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "UNINITIALIZED", "value": "/Applications/CLion.app/Contents/bin/ninja/mac/aarch64/ninja"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_NM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/nm"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "24"}, {"name": "CMAKE_OBJCOPY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_OBJCOPY-NOTFOUND"}, {"name": "CMAKE_OBJDUMP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump"}, {"name": "CMAKE_OSX_ARCHITECTURES", "properties": [{"name": "HELPSTRING", "value": "Build architectures for OSX"}], "type": "STRING", "value": ""}, {"name": "CMAKE_OSX_DEPLOYMENT_TARGET", "properties": [{"name": "HELPSTRING", "value": "Minimum OS X deployment version"}], "type": "STRING", "value": "14.0"}, {"name": "CMAKE_OSX_SYSROOT", "properties": [{"name": "HELPSTRING", "value": "The product will be built against the headers and libraries located inside the indicated SDK."}], "type": "PATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PREFIX_PATH", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "UNINITIALIZED", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "BambuStudio"}, {"name": "CMAKE_PROJECT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "1.0.6"}, {"name": "CMAKE_PROJECT_VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "1"}, {"name": "CMAKE_PROJECT_VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0"}, {"name": "CMAKE_PROJECT_VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "6"}, {"name": "CMAKE_PROJECT_VERSION_TWEAK", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib"}, {"name": "CMAKE_READELF", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_READELF-NOTFOUND"}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STRIP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip"}, {"name": "CMAKE_TAPI", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi"}, {"name": "CMAKE_UNAME", "properties": [{"name": "HELPSTRING", "value": "uname command"}], "type": "INTERNAL", "value": "/usr/bin/uname"}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "CPACK_SOURCE_RPM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build RPM source packages"}], "type": "BOOL", "value": "OFF"}, {"name": "CPACK_SOURCE_TBZ2", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build TBZ2 source packages"}], "type": "BOOL", "value": "ON"}, {"name": "CPACK_SOURCE_TGZ", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build TGZ source packages"}], "type": "BOOL", "value": "ON"}, {"name": "CPACK_SOURCE_TXZ", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build TXZ source packages"}], "type": "BOOL", "value": "ON"}, {"name": "CPACK_SOURCE_TZ", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build TZ source packages"}], "type": "BOOL", "value": "ON"}, {"name": "CPACK_SOURCE_ZIP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable to build ZIP source packages"}], "type": "BOOL", "value": "OFF"}, {"name": "CURL_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for CURL."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CURL"}, {"name": "Clipper2_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/clipper2"}, {"name": "Clipper2_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Clipper2_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;-lm;"}, {"name": "Clipper2_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/clipper2"}, {"name": "DISKARBITRATION_LIBRARY", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DiskArbitration.framework"}, {"name": "EXPAT_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "EXPAT_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "EXPAT_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "EXPAT_LIBRARY_DEBUG-NOTFOUND"}, {"name": "EXPAT_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libexpat.a"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Boost", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON><PERSON>"}], "type": "INTERNAL", "value": "[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfig.cmake][c ][v1.84.0(1.48)]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_EXPAT", "properties": [{"name": "HELPSTRING", "value": "Details about finding EXPAT"}], "type": "INTERNAL", "value": "[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libexpat.a][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include][v2.5.0()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_GLEW", "properties": [{"name": "HELPSTRING", "value": "Details about finding GLEW"}], "type": "INTERNAL", "value": "[/opt/homebrew/lib/cmake/glew/glew-config.cmake][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_GMP", "properties": [{"name": "HELPSTRING", "value": "Details about finding GMP"}], "type": "INTERNAL", "value": "[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libgmp.a][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_MPFR", "properties": [{"name": "HELPSTRING", "value": "Details about finding MPFR"}], "type": "INTERNAL", "value": "[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libmpfr.a][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenCV", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenCV"}], "type": "INTERNAL", "value": "[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local][cfound components: core ][v4.6.0()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenGL", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenGL"}], "type": "INTERNAL", "value": "[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework][c ][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenSSL"}], "type": "INTERNAL", "value": "[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libcrypto.a][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include][c ][v3.1.2(3)]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenVDB", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenVDB"}], "type": "INTERNAL", "value": "[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libopenvdb.a][cfound components: openvdb ][v8.2.0(5.0)]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PNG", "properties": [{"name": "HELPSTRING", "value": "Details about finding PNG"}], "type": "INTERNAL", "value": "[/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libpng.a][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include][v1.6.35()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig", "properties": [{"name": "HELPSTRING", "value": "Details about finding PkgConfig"}], "type": "INTERNAL", "value": "[/opt/homebrew/bin/pkg-config][v2.3.0()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Threads", "properties": [{"name": "HELPSTRING", "value": "Details about finding Threads"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_ZLIB", "properties": [{"name": "HELPSTRING", "value": "Details about finding ZLIB"}], "type": "INTERNAL", "value": "[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libz.tbd][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include][c ][v1.2.12()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_wxWidgets", "properties": [{"name": "HELPSTRING", "value": "Details about finding wxWidgets"}], "type": "INTERNAL", "value": "[-L/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib;-pthread;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_gl-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_webview-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_aui-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_baseu_net-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_media-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_html-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_core-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_baseu-3.1.a;OpenGL.framework;OpenGL.framework;-framework WebKit;-framework AVFoundation;-framework CoreMedia;-weak_framework AVKit;-lwx_osx_cocoau_core-3.1;-lwx_baseu-3.1;libjpeg.a;libpng.a;libz.tbd;libtiff.a;-framework AudioToolbox;-framework WebKit;libz.tbd;-lwxregexu-3.1;libiconv.tbd;-framework CoreFoundation;-framework Security;-framework Carbon;-framework Cocoa;-framework IOKit;-framework QuartzCore][/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/wx/include/osx_cocoa-unicode-static-3.1;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/wx-3.1][v3.1.5(3.1)]"}, {"name": "FLATPAK", "properties": [{"name": "HELPSTRING", "value": "Not copy FFMPEG file"}], "type": "BOOL", "value": "FALSE"}, {"name": "FOUNDATION", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework"}, {"name": "GLEW_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for GLEW."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/glew"}, {"name": "GMPXX_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing the GMPXX include files"}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "GMPXX_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": "Path to the GMPXX library"}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libgmpxx.a"}, {"name": "GMP_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing the GMP header files"}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "GMP_LIBRARY_DEBUG", "properties": [{"name": "HELPSTRING", "value": "Path to the Debug GMP library"}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libgmp.a"}, {"name": "GMP_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": "Path to the Release GMP library"}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libgmp.a"}, {"name": "HAVE_CXX_ATOMICS64_WITHOUT_LIB", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_CXX_ATOMICS64_WITHOUT_LIB"}], "type": "INTERNAL", "value": "1"}, {"name": "HintsToPot_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/hints"}, {"name": "HintsToPot_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "HintsToPot_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/hints"}, {"name": "Iconv_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "iconv include directory"}], "type": "PATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include"}, {"name": "Iconv_IS_BUILT_IN", "properties": [{"name": "HELPSTRING", "value": "Test Iconv_IS_BUILT_IN"}], "type": "INTERNAL", "value": ""}, {"name": "Iconv_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "iconv library (if not in the C library)"}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libiconv.tbd"}, {"name": "IlmBase_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for IlmBase."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/IlmBase"}, {"name": "JPEG_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "JPEG_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "JPEG_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "JPEG_LIBRARY_DEBUG-NOTFOUND"}, {"name": "JPEG_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libjpeg.a"}, {"name": "LIBLZMA_HAS_AUTO_DECODER", "properties": [{"name": "HELPSTRING", "value": "Have library /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/liblzma.tbd"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBLZMA_HAS_EASY_ENCODER", "properties": [{"name": "HELPSTRING", "value": "Have library /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/liblzma.tbd"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBLZMA_HAS_LZMA_PRESET", "properties": [{"name": "HELPSTRING", "value": "Have library /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/liblzma.tbd"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBLZMA_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/opt/homebrew/include"}, {"name": "LIBLZMA_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "LIBLZMA_LIBRARY_DEBUG-NOTFOUND"}, {"name": "LIBLZMA_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/liblzma.tbd"}, {"name": "LIBRT", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "LIBRT-NOTFOUND"}, {"name": "LLVM_HAS_ATOMICS", "properties": [{"name": "HELPSTRING", "value": "Test LLVM_HAS_ATOMICS"}], "type": "INTERNAL", "value": "1"}, {"name": "MCUT_BUILD_AS_SHARED_LIB", "properties": [{"name": "HELPSTRING", "value": "Configure to build MCUT as a shared/dynamic library"}], "type": "BOOL", "value": "OFF"}, {"name": "MCUT_BUILD_DOCUMENTATION", "properties": [{"name": "HELPSTRING", "value": "Configure to build docs with Doxygen"}], "type": "BOOL", "value": "OFF"}, {"name": "MCUT_BUILD_WITH_COMPUTE_HELPER_THREADPOOL", "properties": [{"name": "HELPSTRING", "value": "Configure to build MCUT engine with a shared (amongst contexts) thread-pool"}], "type": "BOOL", "value": "ON"}, {"name": "MCUT_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": "The MCUT include directory"}], "type": "STRING", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include"}, {"name": "MCUT_LIB_PATH", "properties": [{"name": "HELPSTRING", "value": "Path to the compiled MCUT library file"}], "type": "STRING", "value": "$<TARGET_FILE:mcut>"}, {"name": "MODELIO", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ModelIO.framework"}, {"name": "MPFR_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing the MPFR header files"}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "MPFR_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": "Path to the MPFR library"}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libmpfr.a"}, {"name": "MPFR_LIBRARIES_DIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib"}, {"name": "NLopt_DEFINITIONS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "NLopt_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "NLopt_LIBRARY_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "NLopt_LIBS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libnlopt.a"}, {"name": "OPENGL_GLU_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Include for the OpenGL GLU library"}], "type": "PATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework"}, {"name": "OPENGL_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Include for OpenGL"}], "type": "PATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework"}, {"name": "OPENGL_gl_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "OpenGL GL library"}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework"}, {"name": "OPENGL_glu_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "OpenGL GLU library"}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework"}, {"name": "OPENSSL_CRYPTO_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libcrypto.a"}, {"name": "OPENSSL_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "OPENSSL_SSL_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libssl.a"}, {"name": "OPENVDB_FIND_MODULE_PATH", "properties": [{"name": "HELPSTRING", "value": "Path to OpenVDB installation's find modules."}], "type": "PATH", "value": ""}, {"name": "OPENVDB_PRINT", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/bin/vdb_print"}, {"name": "OpenCASCADE_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for OpenCASCADE."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade"}, {"name": "OpenCV_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for OpenCV."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencv4"}, {"name": "OpenVDB_DEBUG_SUFFIX", "properties": [{"name": "HELPSTRING", "value": "Suffix for the debug libraries"}], "type": "STRING", "value": "d"}, {"name": "OpenVDB_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "OpenVDB_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "OpenVDB_openvdb_LIBRARY_DEBUG", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "OpenVDB_openvdb_LIBRARY_DEBUG-NOTFOUND"}, {"name": "OpenVDB_openvdb_LIBRARY_RELEASE", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libopenvdb.a"}, {"name": "PC_EXPAT_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "PC_EXPAT_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/usr/include"}, {"name": "PC_EXPAT_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib;-lexpat"}, {"name": "PC_EXPAT_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}, {"name": "PC_EXPAT_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "expat"}, {"name": "PC_EXPAT_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}, {"name": "PC_EXPAT_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "expat"}, {"name": "PC_EXPAT_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/usr"}, {"name": "PC_EXPAT_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-DXML_STATIC"}, {"name": "PC_EXPAT_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-DXML_STATIC"}, {"name": "PC_EXPAT_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib;-lexpat"}, {"name": "PC_EXPAT_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "expat"}, {"name": "PC_EXPAT_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}, {"name": "PC_EXPAT_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "2.7.1"}, {"name": "PC_EXPAT_expat_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_expat_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_expat_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_EXPAT_expat_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_OpenVDB_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_OpenVDB_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_OpenVDB_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_OpenVDB_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PC_OpenVDB_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_CONFIG_ARGN", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Arguments to supply to pkg-config"}], "type": "STRING", "value": ""}, {"name": "PKG_CONFIG_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "pkg-config executable"}], "type": "FILEPATH", "value": "/opt/homebrew/bin/pkg-config"}, {"name": "PNG_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "PNG_LIBRARY_DEBUG-NOTFOUND"}, {"name": "PNG_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libpng.a"}, {"name": "PNG_PNG_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "Qhull_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qhull."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Qhull"}, {"name": "SLIC3R_ASAN", "properties": [{"name": "HELPSTRING", "value": "Enable ASan on Clang and GCC"}], "type": "BOOL", "value": "OFF"}, {"name": "SLIC3R_BUILD_SANDBOXES", "properties": [{"name": "HELPSTRING", "value": "Build development sandboxes"}], "type": "BOOL", "value": "OFF"}, {"name": "SLIC3R_BUILD_TESTS", "properties": [{"name": "HELPSTRING", "value": "Build unit tests"}], "type": "BOOL", "value": "OFF"}, {"name": "SLIC3R_DESKTOP_INTEGRATION", "properties": [{"name": "HELPSTRING", "value": "Allow perfoming desktop integration during runtime"}], "type": "BOOL", "value": "ON"}, {"name": "SLIC3R_ENC_CHECK", "properties": [{"name": "HELPSTRING", "value": "Verify encoding of source files"}], "type": "BOOL", "value": "OFF"}, {"name": "SLIC3R_FHS", "properties": [{"name": "HELPSTRING", "value": "Assume BambuStudio is to be installed in a FHS directory structure"}], "type": "BOOL", "value": "OFF"}, {"name": "SLIC3R_GTK", "properties": [{"name": "HELPSTRING", "value": "GTK version to use with wxWidgets on Linux"}], "type": "STRING", "value": "2"}, {"name": "SLIC3R_GUI", "properties": [{"name": "HELPSTRING", "value": "Compile BambuStudio with GUI components (OpenGL, wxWidgets)"}], "type": "BOOL", "value": "ON"}, {"name": "SLIC3R_MSVC_COMPILE_PARALLEL", "properties": [{"name": "HELPSTRING", "value": "Compile on Visual Studio in parallel"}], "type": "BOOL", "value": "ON"}, {"name": "SLIC3R_MSVC_PDB", "properties": [{"name": "HELPSTRING", "value": "Generate PDB files on MSVC in Release mode"}], "type": "BOOL", "value": "ON"}, {"name": "SLIC3R_PCH", "properties": [{"name": "HELPSTRING", "value": "Use precompiled headers"}], "type": "BOOL", "value": "ON"}, {"name": "SLIC3R_PERL_XS", "properties": [{"name": "HELPSTRING", "value": "Compile XS Perl module and enable Perl unit and integration tests"}], "type": "BOOL", "value": "OFF"}, {"name": "SLIC3R_PROFILE", "properties": [{"name": "HELPSTRING", "value": "Compile BambuStudio with an invasive Shiny profiler"}], "type": "BOOL", "value": "OFF"}, {"name": "SLIC3R_STATIC", "properties": [{"name": "HELPSTRING", "value": "Compile BambuStudio with static libraries (Boost, TBB, glew)"}], "type": "BOOL", "value": "ON"}, {"name": "SLIC3R_WX_STABLE", "properties": [{"name": "HELPSTRING", "value": "Build against wxWidgets stable (3.0) as oppsed to dev (3.1) on Linux"}], "type": "BOOL", "value": "OFF"}, {"name": "Shiny_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/Shiny"}, {"name": "Shiny_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "Shiny_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/Shiny"}, {"name": "TBB_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for TBB."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/TBB"}, {"name": "TIFF_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "TIFF_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "TIFF_LIBRARY_DEBUG-NOTFOUND"}, {"name": "TIFF_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libtiff.a"}, {"name": "Tiff_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Tiff."}], "type": "PATH", "value": "Tiff_DIR-NOTFOUND"}, {"name": "ZLIB_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include"}, {"name": "ZLIB_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "ZLIB_LIBRARY_DEBUG-NOTFOUND"}, {"name": "ZLIB_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libz.tbd"}, {"name": "_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "CMAKE_INSTALL_PREFIX during last run"}], "type": "INTERNAL", "value": "/usr/local"}, {"name": "_OPENSSL_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "_OPENSSL_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "_OPENSSL_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "_OPENSSL_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "_OPENSSL_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib;-lssl;-lcrypto"}, {"name": "_OPENSSL_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib"}, {"name": "_OPENSSL_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "ssl;crypto"}, {"name": "_OPENSSL_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib"}, {"name": "_OPENSSL_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "openssl"}, {"name": "_OPENSSL_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local"}, {"name": "_OPENSSL_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "_OPENSSL_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"name": "_OPENSSL_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib;-lssl;-lcrypto"}, {"name": "_OPENSSL_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "ssl;crypto"}, {"name": "_OPENSSL_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib"}, {"name": "_OPENSSL_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "3.1.2"}, {"name": "_OPENSSL_openssl_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_openssl_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_openssl_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "_OPENSSL_openssl_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "__pkg_config_arguments_PC_EXPAT", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "QUIET;expat"}, {"name": "__pkg_config_arguments__OPENSSL", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "QUIET;openssl"}, {"name": "__pkg_config_checked_PC_EXPAT", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_PC_OpenVDB", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked__OPENSSL", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "admesh_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/admesh"}, {"name": "admesh_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "admesh_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/admesh"}, {"name": "boost_algorithm_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_algorithm."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_algorithm-1.84.0"}, {"name": "boost_align_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_align."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_align-1.84.0"}, {"name": "boost_array_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_array."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_array-1.84.0"}, {"name": "boost_asio_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_asio."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_asio-1.84.0"}, {"name": "boost_assert_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_assert."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_assert-1.84.0"}, {"name": "boost_atomic_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_atomic."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_atomic-1.84.0"}, {"name": "boost_bind_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_bind."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_bind-1.84.0"}, {"name": "boost_chrono_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_chrono."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_chrono-1.84.0"}, {"name": "boost_concept_check_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_concept_check."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_concept_check-1.84.0"}, {"name": "boost_config_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_config."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_config-1.84.0"}, {"name": "boost_container_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_container."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_container-1.84.0"}, {"name": "boost_container_hash_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_container_hash."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_container_hash-1.84.0"}, {"name": "boost_context_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_context."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_context-1.84.0"}, {"name": "boost_conversion_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_conversion."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_conversion-1.84.0"}, {"name": "boost_core_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_core."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_core-1.84.0"}, {"name": "boost_coroutine_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_coroutine."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_coroutine-1.84.0"}, {"name": "boost_date_time_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_date_time."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_date_time-1.84.0"}, {"name": "boost_describe_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_describe."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_describe-1.84.0"}, {"name": "boost_detail_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_detail."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_detail-1.84.0"}, {"name": "boost_dynamic_bitset_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_dynamic_bitset."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_dynamic_bitset-1.84.0"}, {"name": "boost_endian_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_endian."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_endian-1.84.0"}, {"name": "boost_exception_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_exception."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_exception-1.84.0"}, {"name": "boost_filesystem_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_filesystem."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_filesystem-1.84.0"}, {"name": "boost_function_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_function."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_function-1.84.0"}, {"name": "boost_function_types_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_function_types."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_function_types-1.84.0"}, {"name": "boost_functional_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_functional."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_functional-1.84.0"}, {"name": "boost_fusion_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_fusion."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_fusion-1.84.0"}, {"name": "boost_headers_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_headers."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_headers-1.84.0"}, {"name": "boost_integer_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_integer."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_integer-1.84.0"}, {"name": "boost_interprocess_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_interprocess."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_interprocess-1.84.0"}, {"name": "boost_intrusive_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_intrusive."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_intrusive-1.84.0"}, {"name": "boost_io_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_io."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_io-1.84.0"}, {"name": "boost_iostreams_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_iostreams."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0"}, {"name": "boost_iterator_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_iterator."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iterator-1.84.0"}, {"name": "boost_lexical_cast_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_lexical_cast."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_lexical_cast-1.84.0"}, {"name": "boost_locale_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_locale."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_locale-1.84.0"}, {"name": "boost_log_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_log."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_log-1.84.0"}, {"name": "boost_move_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_move."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_move-1.84.0"}, {"name": "boost_mp11_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_mp11."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_mp11-1.84.0"}, {"name": "boost_mpl_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_mpl."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_mpl-1.84.0"}, {"name": "boost_numeric_conversion_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_numeric_conversion."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_numeric_conversion-1.84.0"}, {"name": "boost_optional_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_optional."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_optional-1.84.0"}, {"name": "boost_parameter_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_parameter."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_parameter-1.84.0"}, {"name": "boost_phoenix_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_phoenix."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_phoenix-1.84.0"}, {"name": "boost_pool_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_pool."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_pool-1.84.0"}, {"name": "boost_predef_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_predef."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_predef-1.84.0"}, {"name": "boost_preprocessor_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_preprocessor."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_preprocessor-1.84.0"}, {"name": "boost_proto_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_proto."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_proto-1.84.0"}, {"name": "boost_random_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_random."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_random-1.84.0"}, {"name": "boost_range_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_range."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_range-1.84.0"}, {"name": "boost_ratio_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_ratio."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_ratio-1.84.0"}, {"name": "boost_regex_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_regex."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_regex-1.84.0"}, {"name": "boost_smart_ptr_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_smart_ptr."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_smart_ptr-1.84.0"}, {"name": "boost_spirit_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_spirit."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_spirit-1.84.0"}, {"name": "boost_static_assert_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_static_assert."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_static_assert-1.84.0"}, {"name": "boost_system_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_system."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_system-1.84.0"}, {"name": "boost_thread_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_thread."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_thread-1.84.0"}, {"name": "boost_throw_exception_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_throw_exception."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_throw_exception-1.84.0"}, {"name": "boost_tokenizer_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_tokenizer."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_tokenizer-1.84.0"}, {"name": "boost_tuple_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_tuple."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_tuple-1.84.0"}, {"name": "boost_type_index_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_type_index."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_type_index-1.84.0"}, {"name": "boost_type_traits_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_type_traits."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_type_traits-1.84.0"}, {"name": "boost_typeof_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_typeof."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_typeof-1.84.0"}, {"name": "boost_unordered_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_unordered."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_unordered-1.84.0"}, {"name": "boost_utility_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_utility."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_utility-1.84.0"}, {"name": "boost_variant2_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_variant2."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_variant2-1.84.0"}, {"name": "boost_variant_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_variant."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_variant-1.84.0"}, {"name": "boost_winapi_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for boost_winapi."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_winapi-1.84.0"}, {"name": "cereal_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for cereal."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/share/cmake/cereal"}, {"name": "clipper_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/clipper"}, {"name": "clipper_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "clipper_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/clipper"}, {"name": "glfw3_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for glfw3."}], "type": "PATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/glfw3"}, {"name": "glu-libtess_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/glu-libtess"}, {"name": "glu-libtess_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "glu-libtess_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;m;"}, {"name": "glu-libtess_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/glu-libtess"}, {"name": "imgui_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/imgui"}, {"name": "imgui_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "imgui_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/imgui"}, {"name": "imguizmo_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/imguizmo"}, {"name": "imguizmo_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "imguizmo_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;imgui;"}, {"name": "imguizmo_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/imguizmo"}, {"name": "libigl_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libigl"}, {"name": "libigl_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for libigl."}], "type": "PATH", "value": "libigl_DIR-NOTFOUND"}, {"name": "libigl_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "libigl_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"name": "libslic3r_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r"}, {"name": "libslic3r_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "libslic3r_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libslic3r"}, {"name": "libslic3r_gui_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/slic3r"}, {"name": "libslic3r_gui_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "libslic3r_gui_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/slic3r"}, {"name": "mcut_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/mcut"}, {"name": "mcut_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "mcut_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut"}, {"name": "minilzo_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/minilzo"}, {"name": "minilzo_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "minilzo_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/minilzo"}, {"name": "miniz_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/miniz"}, {"name": "miniz_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "miniz_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/miniz"}, {"name": "nowide_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/boost"}, {"name": "nowide_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "nowide_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/boost"}, {"name": "pkgcfg_lib_PC_EXPAT_expat", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libexpat.a"}, {"name": "pkgcfg_lib__OPENSSL_crypto", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libcrypto.a"}, {"name": "pkgcfg_lib__OPENSSL_ssl", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libssl.a"}, {"name": "prefix_result", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib"}, {"name": "semver_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/semver"}, {"name": "semver_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "semver_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/semver"}, {"name": "wxWidgets_CONFIG_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "Location of wxWidgets library configuration provider binary (wx-config)."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/bin/wx-config"}, {"name": "wxWidgets_USE_DEBUG", "properties": [{"name": "HELPSTRING", "value": "Use debug build?"}], "type": "BOOL", "value": "OFF"}, {"name": "wxWidgets_USE_STATIC", "properties": [{"name": "HELPSTRING", "value": "Link libraries statically?"}], "type": "BOOL", "value": "ON"}, {"name": "wxWidgets_USE_UNICODE", "properties": [{"name": "HELPSTRING", "value": "Use unicode build?"}], "type": "BOOL", "value": "ON"}, {"name": "wxWidgets_wxrc_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "Location of wxWidgets resource file compiler binary (wxrc)"}], "type": "FILEPATH", "value": "/opt/homebrew/bin/wxrc"}, {"name": "zstd_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for zstd."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/zstd"}], "kind": "cache", "version": {"major": 2, "minor": 0}}