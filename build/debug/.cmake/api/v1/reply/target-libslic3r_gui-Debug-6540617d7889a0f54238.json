{"archive": {}, "artifacts": [{"path": "src/slic3r/liblibslic3r_gui.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_options", "add_compile_definitions", "target_compile_definitions", "add_definitions", "set_property", "include", "include_directories", "target_include_directories", "target_precompile_headers", "add_precompiled_header"], "files": ["src/slic3r/CMakeLists.txt", "CMakeLists.txt", "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/UsewxWidgets.cmake", "src/CMakeLists.txt", "cmake/modules/PrecompiledHeader.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 644, "parent": 0}, {"command": 1, "file": 0, "line": 655, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 139, "parent": 3}, {"command": 2, "file": 1, "line": 173, "parent": 3}, {"command": 2, "file": 1, "line": 257, "parent": 3}, {"command": 2, "file": 1, "line": 259, "parent": 3}, {"command": 2, "file": 1, "line": 264, "parent": 3}, {"command": 2, "file": 1, "line": 271, "parent": 3}, {"command": 2, "file": 1, "line": 275, "parent": 3}, {"command": 3, "file": 1, "line": 130, "parent": 3}, {"command": 4, "file": 0, "line": 716, "parent": 0}, {"command": 5, "file": 1, "line": 96, "parent": 3}, {"command": 5, "file": 1, "line": 92, "parent": 3}, {"command": 5, "file": 1, "line": 339, "parent": 3}, {"file": 3}, {"command": 7, "file": 3, "line": 58, "parent": 16}, {"file": 2, "parent": 17}, {"command": 6, "file": 2, "line": 55, "parent": 18}, {"command": 4, "file": 0, "line": 680, "parent": 0}, {"command": 5, "file": 1, "line": 342, "parent": 3}, {"command": 8, "file": 1, "line": 322, "parent": 3}, {"command": 8, "file": 1, "line": 324, "parent": 3}, {"command": 8, "file": 3, "line": 30, "parent": 16}, {"command": 9, "file": 0, "line": 645, "parent": 0}, {"command": 8, "file": 1, "line": 491, "parent": 3}, {"command": 8, "file": 1, "line": 326, "parent": 3}, {"command": 8, "file": 2, "line": 46, "parent": 18}, {"command": 11, "file": 0, "line": 688, "parent": 0}, {"command": 10, "file": 4, "line": 257, "parent": 29}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -pthread -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics -F/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/System/Library/Frameworks "}, {"backtrace": 4, "fragment": "-f<PERSON>-char"}, {"backtrace": 5, "fragment": "-DDEBUG"}, {"backtrace": 6, "fragment": "-Werror=return-type"}, {"backtrace": 7, "fragment": "-Wno-unused-function"}, {"backtrace": 7, "fragment": "-Wno-unused-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-label"}, {"backtrace": 7, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 8, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 9, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 10, "fragment": "-Wno-error=enum-constexpr-conversion"}, {"backtrace": 2, "fragment": "-DOPENVDB_ABI_VERSION_NUMBER=8"}, {"fragment": "-Winvalid-pch -Xclang -emit-pch -Xclang -include -Xclang /Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/slic3r/CMakeFiles/libslic3r_gui.dir/cmake_pch.hxx -x c++-header"}], "defines": [{"backtrace": 2, "define": "BOOST_ASIO_DISABLE_KQUEUE"}, {"backtrace": 2, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 2, "define": "BOOST_ATOMIC_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CHRONO_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_CONTAINER_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CONTAINER_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_STATIC_LINK=1"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_LOCALE_NO_LIB"}, {"backtrace": 2, "define": "BOOST_LOCALE_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_LOG_NO_LIB"}, {"backtrace": 2, "define": "BOOST_LOG_STATIC_LINK"}, {"backtrace": 11, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 2, "define": "BOOST_RANDOM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_RANDOM_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 2, "define": "BOOST_THREAD_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_THREAD_USE_LIB"}, {"backtrace": 2, "define": "CURL_STATICLIB"}, {"backtrace": 2, "define": "GLEW_STATIC"}, {"backtrace": 2, "define": "LIBNEST2D_GEOMETRIES_libslic3r"}, {"backtrace": 2, "define": "LIBNEST2D_OPTIMIZER_nlopt"}, {"backtrace": 2, "define": "LIBNEST2D_STATIC"}, {"backtrace": 2, "define": "LIBNEST2D_THREADING_tbb"}, {"backtrace": 2, "define": "OPENVDB_OPENEXR_STATICLIB"}, {"backtrace": 2, "define": "OPENVDB_STATICLIB"}, {"backtrace": 12, "define": "SLIC3R_CURRENTLY_COMPILING_GUI_MODULE"}, {"backtrace": 13, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 14, "define": "SLIC3R_GUI"}, {"backtrace": 2, "define": "TBB_USE_CAPTURED_EXCEPTION=0"}, {"backtrace": 2, "define": "TBB_USE_DEBUG"}, {"backtrace": 15, "define": "UNICODE"}, {"backtrace": 2, "define": "USE_TBB"}, {"backtrace": 15, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 19, "define": "WXUSINGDLL"}, {"backtrace": 11, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 15, "define": "_UNICODE"}, {"backtrace": 19, "define": "__WXMAC__"}, {"backtrace": 19, "define": "__WXOSX_COCOA__"}, {"backtrace": 19, "define": "__WXOSX__"}, {"backtrace": 20, "define": "wxDEBUG_LEVEL=0"}, {"backtrace": 21, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 15, "define": "wxUSE_UNICODE"}], "frameworks": [{"path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DiskArbitration.framework"}, {"backtrace": 2, "isSystem": true, "path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework"}], "includes": [{"backtrace": 22, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 23, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 24, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/hidapi/include"}, {"backtrace": 25, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/slic3r/Utils"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencascade"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libnest2d/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/miniz"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/glu-libtess/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/clipper2/Clipper2Lib/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/minilzo"}, {"backtrace": 26, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}, {"backtrace": 27, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 28, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/wx/include/osx_cocoa-unicode-static-3.1"}, {"backtrace": 28, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/wx-3.1"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencv4"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/OpenEXR"}, {"backtrace": 2, "isSystem": true, "path": "/opt/homebrew/include"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2, 2, 2, 2, 2], "standard": "17"}, "precompileHeaders": [{"backtrace": 30, "header": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/slic3r/pchheader.hpp"}], "sourceIndexes": [0]}, {"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -pthread -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics -F/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/System/Library/Frameworks "}, {"backtrace": 4, "fragment": "-f<PERSON>-char"}, {"backtrace": 5, "fragment": "-DDEBUG"}, {"backtrace": 6, "fragment": "-Werror=return-type"}, {"backtrace": 7, "fragment": "-Wno-unused-function"}, {"backtrace": 7, "fragment": "-Wno-unused-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-label"}, {"backtrace": 7, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 8, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 9, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 10, "fragment": "-Wno-error=enum-constexpr-conversion"}, {"backtrace": 2, "fragment": "-DOPENVDB_ABI_VERSION_NUMBER=8"}, {"fragment": "-Winvalid-pch -Xclang -include-pch -Xclang /Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/slic3r/CMakeFiles/libslic3r_gui.dir/cmake_pch.hxx.pch -Xclang -include -Xclang /Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/slic3r/CMakeFiles/libslic3r_gui.dir/cmake_pch.hxx"}], "defines": [{"backtrace": 2, "define": "BOOST_ASIO_DISABLE_KQUEUE"}, {"backtrace": 2, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 2, "define": "BOOST_ATOMIC_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CHRONO_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_CONTAINER_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CONTAINER_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_STATIC_LINK=1"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_LOCALE_NO_LIB"}, {"backtrace": 2, "define": "BOOST_LOCALE_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_LOG_NO_LIB"}, {"backtrace": 2, "define": "BOOST_LOG_STATIC_LINK"}, {"backtrace": 11, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 2, "define": "BOOST_RANDOM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_RANDOM_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 2, "define": "BOOST_THREAD_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_THREAD_USE_LIB"}, {"backtrace": 2, "define": "CURL_STATICLIB"}, {"backtrace": 2, "define": "GLEW_STATIC"}, {"backtrace": 2, "define": "LIBNEST2D_GEOMETRIES_libslic3r"}, {"backtrace": 2, "define": "LIBNEST2D_OPTIMIZER_nlopt"}, {"backtrace": 2, "define": "LIBNEST2D_STATIC"}, {"backtrace": 2, "define": "LIBNEST2D_THREADING_tbb"}, {"backtrace": 2, "define": "OPENVDB_OPENEXR_STATICLIB"}, {"backtrace": 2, "define": "OPENVDB_STATICLIB"}, {"backtrace": 12, "define": "SLIC3R_CURRENTLY_COMPILING_GUI_MODULE"}, {"backtrace": 13, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 14, "define": "SLIC3R_GUI"}, {"backtrace": 2, "define": "TBB_USE_CAPTURED_EXCEPTION=0"}, {"backtrace": 2, "define": "TBB_USE_DEBUG"}, {"backtrace": 15, "define": "UNICODE"}, {"backtrace": 2, "define": "USE_TBB"}, {"backtrace": 15, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 19, "define": "WXUSINGDLL"}, {"backtrace": 11, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 15, "define": "_UNICODE"}, {"backtrace": 19, "define": "__WXMAC__"}, {"backtrace": 19, "define": "__WXOSX_COCOA__"}, {"backtrace": 19, "define": "__WXOSX__"}, {"backtrace": 20, "define": "wxDEBUG_LEVEL=0"}, {"backtrace": 21, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 15, "define": "wxUSE_UNICODE"}], "frameworks": [{"path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DiskArbitration.framework"}, {"backtrace": 2, "isSystem": true, "path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework"}], "includes": [{"backtrace": 22, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 23, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 24, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/hidapi/include"}, {"backtrace": 25, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/slic3r/Utils"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencascade"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libnest2d/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/miniz"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/glu-libtess/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/clipper2/Clipper2Lib/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/minilzo"}, {"backtrace": 26, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}, {"backtrace": 27, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 28, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/wx/include/osx_cocoa-unicode-static-3.1"}, {"backtrace": 28, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/wx-3.1"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencv4"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/OpenEXR"}, {"backtrace": 2, "isSystem": true, "path": "/opt/homebrew/include"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2, 2, 2, 2, 2], "standard": "17"}, "precompileHeaders": [{"backtrace": 30, "header": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/slic3r/pchheader.hpp"}], "sourceIndexes": [1, 3, 6, 7, 9, 11, 14, 16, 18, 19, 22, 24, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 67, 69, 71, 73, 75, 77, 79, 81, 83, 85, 88, 89, 91, 93, 95, 97, 101, 102, 105, 108, 110, 111, 113, 115, 117, 119, 121, 123, 125, 127, 129, 131, 133, 135, 137, 139, 141, 143, 145, 147, 149, 151, 153, 155, 157, 159, 161, 164, 166, 168, 169, 172, 174, 175, 177, 179, 181, 183, 185, 187, 189, 191, 193, 195, 197, 199, 201, 203, 205, 207, 209, 211, 214, 216, 218, 220, 221, 223, 225, 227, 229, 231, 233, 235, 237, 239, 241, 243, 245, 247, 249, 251, 253, 255, 258, 260, 261, 263, 266, 267, 269, 271, 273, 275, 278, 280, 281, 283, 285, 287, 289, 291, 293, 295, 297, 299, 301, 303, 305, 307, 309, 311, 313, 315, 317, 319, 321, 323, 325, 327, 329, 331, 333, 336, 338, 340, 342, 344, 346, 347, 349, 351, 353, 355, 358, 361, 362, 365, 369, 373, 376, 378, 380, 381, 383, 386, 388, 390, 392, 395, 397, 399, 401, 403, 405, 407, 409, 411, 413, 414, 416, 419, 421, 423, 425, 427, 429, 431, 434, 435, 437, 439, 441, 444, 445, 447, 449, 451, 453, 455, 457, 460, 462, 465, 467, 469, 471, 473, 475, 477, 479, 481, 483, 485, 487, 488, 490, 492, 494, 496, 498, 501, 503, 505, 507, 509, 511, 513, 514, 516, 519, 521, 523, 525, 526, 528, 530, 532, 534, 536, 539, 540, 542, 544, 546, 549, 551, 553, 556, 558, 560, 562, 563, 566, 567, 569, 571, 573, 575, 577, 580, 582, 584, 586, 589, 591, 593, 596, 597, 598, 601, 603, 605, 607, 609, 611, 613, 615, 617, 619, 620, 622, 624, 626, 628, 630, 632]}, {"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -pthread -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics -F/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/System/Library/Frameworks "}, {"backtrace": 4, "fragment": "-f<PERSON>-char"}, {"backtrace": 5, "fragment": "-DDEBUG"}, {"backtrace": 6, "fragment": "-Werror=return-type"}, {"backtrace": 7, "fragment": "-Wno-unused-function"}, {"backtrace": 7, "fragment": "-Wno-unused-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-label"}, {"backtrace": 7, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 8, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 9, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 10, "fragment": "-Wno-error=enum-constexpr-conversion"}, {"backtrace": 2, "fragment": "-DOPENVDB_ABI_VERSION_NUMBER=8"}], "defines": [{"backtrace": 2, "define": "BOOST_ASIO_DISABLE_KQUEUE"}, {"backtrace": 2, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 2, "define": "BOOST_ATOMIC_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CHRONO_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_CONTAINER_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CONTAINER_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_STATIC_LINK=1"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_LOCALE_NO_LIB"}, {"backtrace": 2, "define": "BOOST_LOCALE_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_LOG_NO_LIB"}, {"backtrace": 2, "define": "BOOST_LOG_STATIC_LINK"}, {"backtrace": 11, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 2, "define": "BOOST_RANDOM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_RANDOM_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 2, "define": "BOOST_THREAD_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_THREAD_USE_LIB"}, {"backtrace": 2, "define": "CURL_STATICLIB"}, {"backtrace": 2, "define": "GLEW_STATIC"}, {"backtrace": 2, "define": "LIBNEST2D_GEOMETRIES_libslic3r"}, {"backtrace": 2, "define": "LIBNEST2D_OPTIMIZER_nlopt"}, {"backtrace": 2, "define": "LIBNEST2D_STATIC"}, {"backtrace": 2, "define": "LIBNEST2D_THREADING_tbb"}, {"backtrace": 2, "define": "OPENVDB_OPENEXR_STATICLIB"}, {"backtrace": 2, "define": "OPENVDB_STATICLIB"}, {"backtrace": 12, "define": "SLIC3R_CURRENTLY_COMPILING_GUI_MODULE"}, {"backtrace": 13, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 14, "define": "SLIC3R_GUI"}, {"backtrace": 2, "define": "TBB_USE_CAPTURED_EXCEPTION=0"}, {"backtrace": 2, "define": "TBB_USE_DEBUG"}, {"backtrace": 15, "define": "UNICODE"}, {"backtrace": 2, "define": "USE_TBB"}, {"backtrace": 15, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 19, "define": "WXUSINGDLL"}, {"backtrace": 11, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 15, "define": "_UNICODE"}, {"backtrace": 19, "define": "__WXMAC__"}, {"backtrace": 19, "define": "__WXOSX_COCOA__"}, {"backtrace": 19, "define": "__WXOSX__"}, {"backtrace": 20, "define": "wxDEBUG_LEVEL=0"}, {"backtrace": 21, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 15, "define": "wxUSE_UNICODE"}], "frameworks": [{"path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DiskArbitration.framework"}, {"backtrace": 2, "isSystem": true, "path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework"}], "includes": [{"backtrace": 22, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 23, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 24, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/hidapi/include"}, {"backtrace": 25, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/slic3r/Utils"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencascade"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libnest2d/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/miniz"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/glu-libtess/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/clipper2/Clipper2Lib/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/minilzo"}, {"backtrace": 26, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}, {"backtrace": 27, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 28, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/wx/include/osx_cocoa-unicode-static-3.1"}, {"backtrace": 28, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/wx-3.1"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencv4"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/OpenEXR"}, {"backtrace": 2, "isSystem": true, "path": "/opt/homebrew/include"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2, 2, 2, 2, 2], "standard": "17"}, "precompileHeaders": [{"backtrace": 30, "header": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/slic3r/pchheader.hpp"}], "sourceIndexes": [633, 634, 635, 637, 638, 640]}], "dependencies": [{"backtrace": 2, "id": "imgui::@f0bb1e571c12c8d458e0"}, {"backtrace": 2, "id": "admesh::@db36e4f74c66f2bf2254"}, {"backtrace": 2, "id": "nowide::@34b20850f9a50713ec38"}, {"backtrace": 2, "id": "clipper::@81359c37bfb1d12160f2"}, {"backtrace": 2, "id": "Clipper2::@9bf50c2026546175e5f3"}, {"backtrace": 2, "id": "miniz_static::@8c4610aaeb52d7e4118f"}, {"backtrace": 2, "id": "minilzo_static::@a95d82a94b3f20e643da"}, {"backtrace": 2, "id": "glu-libtess::@86057b0eb26356d52b06"}, {"backtrace": 2, "id": "semver::@9d48bd82e9816d725e2b"}, {"backtrace": 2, "id": "mcut::@a392ea28d34c20f491b4"}, {"backtrace": 2, "id": "libnest2d::@8f4a5c5a495c7f0c1447"}, {"backtrace": 2, "id": "libslic3r_cgal::@87deb467dcbeda20dc6b"}, {"backtrace": 2, "id": "imguizmo::@67d194951d60576ffe05"}, {"backtrace": 2, "id": "hidapi::@1f7e0fad1acc4ee8b79a"}], "id": "libslic3r_gui::@1086e93791e4332ab3a6", "name": "libslic3r_gui", "nameOnDisk": "liblibslic3r_gui.a", "paths": {"build": "src/slic3r", "source": "src/slic3r"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "", "sourceIndexes": [1, 2]}, {"name": "GUI\\Printer", "sourceIndexes": [3, 4]}, {"name": "GUI\\Widgets", "sourceIndexes": [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76]}, {"name": "GUI", "sourceIndexes": [77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 159, 160, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 635, 636, 637, 638, 639, 640, 641, 642]}, {"name": "GUI\\Gizmos", "sourceIndexes": [113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 161, 162]}, {"name": "Config", "sourceIndexes": [347, 348, 349, 350]}, {"name": "Utils", "sourceIndexes": [351, 352, 506, 507, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 633, 634]}, {"name": "GUI\\Jobs", "sourceIndexes": [364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401]}, {"name": "GUI\\DeviceCore", "sourceIndexes": [586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628]}, {"name": "GUI\\DeviceTab", "sourceIndexes": [629, 630, 631, 632]}, {"name": "Precompile Header File", "sourceIndexes": [643]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "path": "build/debug/src/slic3r/CMakeFiles/libslic3r_gui.dir/cmake_pch.hxx.cxx", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/pchheader.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/slic3r/pchheader.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Printer/PrinterFileSystem.cpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "src/slic3r/GUI/Printer/PrinterFileSystem.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/AnimaController.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/AnimaController.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/AxisCtrlButton.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/AxisCtrlButton.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/Button.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/Button.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/CheckBox.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/CheckBox.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/RadioBox.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/RadioBox.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/ProgressDialog.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/ProgressDialog.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/RoundedRectangle.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/RoundedRectangle.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/ComboBox.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/ComboBox.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/SideButton.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/SideButton.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/SideMenuPopup.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/SideMenuPopup.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/DropDown.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/DropDown.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/PopupWindow.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/PopupWindow.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/Label.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/Label.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/LinkLabel.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/LinkLabel.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/Scrollbar.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/Scrollbar.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/ScrolledWindow.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/ScrolledWindow.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/StaticBox.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/StaticBox.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/StaticGroup.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/StaticGroup.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/ImageSwitchButton.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/ImageSwitchButton.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/SwitchButton.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/SwitchButton.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/SpinInput.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/SpinInput.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/StaticLine.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/StaticLine.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/StateColor.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/StateColor.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/StateHandler.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/StateHandler.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/TabCtrl.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/TabCtrl.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/TextInput.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/TextInput.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/TempInput.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/TempInput.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/AMSControl.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/AMSControl.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/AMSItem.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/AMSItem.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/FilamentLoad.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/FilamentLoad.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/FanControl.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/FanControl.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/StepCtrl.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/StepCtrl.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/ProgressBar.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/ProgressBar.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/SideTools.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/SideTools.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/WebView.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/WebView.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Widgets/ErrorMsgStaticText.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/slic3r/GUI/Widgets/ErrorMsgStaticText.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/AboutDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/AboutDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/NetworkTestDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/NetworkTestDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/AuxiliaryDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/AuxiliaryDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Auxiliary.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Auxiliary.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DailyTips.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/DailyTips.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/EncodedFilament.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/EncodedFilament.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Project.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Project.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/BackgroundSlicingProcess.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/BackgroundSlicingProcess.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/BitmapCache.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/BitmapCache.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ImageGrid.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ImageGrid.h", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/3DScene.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/3DScene.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/format.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GLShadersManager.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GLShadersManager.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GLShader.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GLShader.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GLCanvas3D.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GLCanvas3D.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GLEnums.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/OpenGLManager.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/OpenGLManager.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Selection.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Selection.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/SlicingProgressNotification.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/SlicingProgressNotification.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmosManager.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmosManager.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmosCommon.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmosCommon.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoBase.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoBase.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoMove.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoMove.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoRotate.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoRotate.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoScale.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoScale.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoSlaSupports.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoSlaSupports.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoFdmSupports.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoFdmSupports.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoFlatten.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoFlatten.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoAdvancedCut.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoAdvancedCut.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoHollow.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoHollow.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoPainterBase.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoPainterBase.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoSimplify.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoSimplify.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoMmuSegmentation.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoMmuSegmentation.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoFuzzySkin.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoFuzzySkin.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoFaceDetector.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoFaceDetector.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoMeasure.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoMeasure.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoBrimEars.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoBrimEars.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoAssembly.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoAssembly.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoSeam.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoSeam.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoText.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoText.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoSVG.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoSVG.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoMeshBoolean.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GLGizmoMeshBoolean.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GLSelectionRectangle.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GLSelectionRectangle.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Gizmos/GizmoObjectManipulation.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/Gizmos/GizmoObjectManipulation.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/slic3r/GUI/GLModel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GLModel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GLTexture.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GLTexture.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GLToolbar.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GLToolbar.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GuiColor.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GuiColor.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/IMToolbar.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/IMToolbar.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GCodeViewer.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GCodeViewer.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ImageDPIFrame.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ImageDPIFrame.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Preferences.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Preferences.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/AMSSetting.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/AMSSetting.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/AMSMaterialsSetting.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/AMSMaterialsSetting.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ExtrusionCalibration.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ExtrusionCalibration.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/PresetHints.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/PresetHints.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI_Init.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI_Init.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI_Preview.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI_Preview.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI_App.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI_App.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI_Utils.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI_Utils.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/I18N.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/I18N.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/IconManager.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/IconManager.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MainFrame.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MainFrame.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/BBLTopbar.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/BBLTopbar.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/BedShapeDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/BedShapeDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Plater.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Plater.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/PartPlate.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/PartPlate.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/UserNotification.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/UserNotification.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/PresetComboBoxes.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/PresetComboBoxes.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/BitmapComboBox.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/BitmapComboBox.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/SavePresetDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/SavePresetDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI_Colors.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI_Colors.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI_Factories.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI_Factories.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI_ObjectList.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI_ObjectList.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI_ObjectLayers.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI_ObjectLayers.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI_AuxiliaryList.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI_AuxiliaryList.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI_ObjectSettings.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI_ObjectSettings.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI_ObjectTable.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI_ObjectTable.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/GUI_ObjectTableSettings.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/GUI_ObjectTableSettings.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MeshUtils.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MeshUtils.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/TickCode.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/TickCode.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Tab.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Tab.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ParamsDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ParamsDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ParamsPanel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ParamsPanel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/PrintHostDialogs.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/PrintHostDialogs.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/AmsWidgets.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/AmsWidgets.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MediaFilePanel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MediaFilePanel.h", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MediaPlayCtrl.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MediaPlayCtrl.h", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MonitorBasePanel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MonitorBasePanel.h", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/UpgradePanel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/UpgradePanel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/HintNotification.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/HintNotification.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/HMSPanel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/HMSPanel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MonitorPage.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MonitorPage.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/StatusPanel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/StatusPanel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/HMS.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/HMS.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/SliceInfoPanel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/SliceInfoPanel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/CameraPopup.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/CameraPopup.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Monitor.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Monitor.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/WebViewDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/WebViewDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/PrinterWebView.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/PrinterWebView.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/WebDownPluginDlg.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/WebDownPluginDlg.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/WebGuideDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/WebGuideDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/WebUserLoginDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/WebUserLoginDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ConfigManipulation.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ConfigManipulation.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Field.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Field.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DragDropPanel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/DragDropPanel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/CapsuleButton.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/CapsuleButton.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/FilamentMapPanel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/FilamentMapPanel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/FilamentMapDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/FilamentMapDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/OptionsGroup.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/OptionsGroup.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/OG_CustomCtrl.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/OG_CustomCtrl.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MarkdownTip.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MarkdownTip.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/2DBed.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/2DBed.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/3DBed.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/3DBed.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Camera.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Camera.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/CameraUtils.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/CameraUtils.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/wxExtensions.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/wxExtensions.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ObjColorDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ObjColorDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/FilamentPickerDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/FilamentPickerDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/FilamentBitmapUtils.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/FilamentBitmapUtils.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/WipeTowerDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/WipeTowerDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/RemovableDriveManager.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/RemovableDriveManager.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/SendSystemInfoDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/SendSystemInfoDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/StepMeshDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/StepMeshDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/BaseTransparentDPIFrame.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/BaseTransparentDPIFrame.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/SyncAmsInfoDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/SyncAmsInfoDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/SurfaceDrag.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/SurfaceDrag.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/TextLines.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/TextLines.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/PlateSettingsDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/PlateSettingsDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ImGuiWrapper.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ImGuiWrapper.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ImageMessageDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ImageMessageDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceManager.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceManager.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceErrorDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceErrorDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/UserManager.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/UserManager.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/HttpServer.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/HttpServer.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Config/Snapshot.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/slic3r/Config/Snapshot.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Config/Version.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/slic3r/Config/Version.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/ASCIIFolding.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/ASCIIFolding.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ConfigWizard.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ConfigWizard.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ConnectPrinter.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ConnectPrinter.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ConfigWizard_private.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MsgDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MsgDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/DownloadProgressDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DownloadProgressDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/UpdateDialogs.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/UpdateDialogs.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/Job.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/Job.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/JobNew.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/Worker.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/BoostThreadWorker.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/BoostThreadWorker.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/BusyCursorJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/ThreadSafeQueue.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/PlaterWorker.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/EmbossJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/EmbossJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/PlaterJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/PlaterJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/UpgradeNetworkJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/UpgradeNetworkJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/ArrangeJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/ArrangeJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/CreateFontNameImageJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/CreateFontNameImageJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/CreateFontStyleImagesJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/CreateFontStyleImagesJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/OrientJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/OrientJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/RotoptimizeJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/RotoptimizeJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/FillBedJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/FillBedJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/SLAImportJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/SLAImportJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/ProgressIndicator.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/PrintJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/PrintJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/SendJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/SendJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/BindJob.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/BindJob.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/Jobs/NotificationProgressIndicator.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Jobs/NotificationProgressIndicator.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/slic3r/GUI/PhysicalPrinterDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/PhysicalPrinterDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ProgressStatusBar.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ProgressStatusBar.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/BBLStatusBar.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/BBLStatusBar.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/BBLStatusBarSend.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/BBLStatusBarSend.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/BBLStatusBarPrint.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/BBLStatusBarPrint.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/BBLStatusBarBind.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/BBLStatusBarBind.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Mouse3DController.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Mouse3DController.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/IMSlider.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/IMSlider.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/IMSlider_Utils.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Notebook.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Notebook.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/TabButton.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/TabButton.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Tabbook.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Tabbook.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ObjectDataViewModel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ObjectDataViewModel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/AuxiliaryDataViewModel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/AuxiliaryDataViewModel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/InstanceCheck.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/InstanceCheck.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Search.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Search.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/FilamentGroupPopup.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/FilamentGroupPopup.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/NotificationManager.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/NotificationManager.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/UnsavedChangesDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/UnsavedChangesDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/UserPresetsDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/UserPresetsDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ExtraRenderers.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ExtraRenderers.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ProjectDirtyStateManager.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ProjectDirtyStateManager.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DesktopIntegrationDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/DesktopIntegrationDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DragCanvas.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/DragCanvas.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/PublishDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/PublishDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/RecenterDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/RecenterDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/PrivacyUpdateDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/PrivacyUpdateDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/BonjourDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/BonjourDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/BindDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/BindDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/PartSkipCommon.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/PartSkipDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/PartSkipDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/SkipPartCanvas.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/SkipPartCanvas.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ModelMall.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ModelMall.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/SelectMachine.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/SelectMachine.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/PrePrintChecker.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/PrePrintChecker.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/SelectMachinePop.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/SelectMachinePop.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/SendToPrinter.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/SendToPrinter.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/AmsMappingPopup.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/AmsMappingPopup.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/ReleaseNote.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/ReleaseNote.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/HelioReleaseNote.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/HelioReleaseNote.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/SingleChoiceDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/SingleChoiceDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/CaliHistoryDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/CaliHistoryDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/CalibrationPanel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/CalibrationPanel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/CalibrationWizard.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/CalibrationWizard.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/CalibrationWizardPage.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/CalibrationWizardPage.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/CalibrationWizardStartPage.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/CalibrationWizardStartPage.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/CalibrationWizardPresetPage.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/CalibrationWizardPresetPage.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/CalibrationWizardCaliPage.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/CalibrationWizardCaliPage.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/CalibrationWizardSavePage.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/CalibrationWizardSavePage.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/calib_dlg.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/calib_dlg.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/Calibration.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/Calibration.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/PrintOptionsDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/PrintOptionsDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/CreatePresetsDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/CreatePresetsDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/Utils/json_diff.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/json_diff.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/GUI/KBShortcutsDialog.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/KBShortcutsDialog.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MultiMachine.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MultiMachine.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MultiMachinePage.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MultiMachinePage.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MultiMachineManagerPage.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MultiMachineManagerPage.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MultiPrintJob.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MultiPrintJob.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MultiSendMachineModel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MultiSendMachineModel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MultiTaskManagerPage.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MultiTaskManagerPage.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/MultiTaskModel.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/MultiTaskModel.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/SendMultiMachinePage.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/SendMultiMachinePage.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/TaskManager.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/TaskManager.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/WxFontUtils.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/WxFontUtils.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/EmbossStyleManager.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/EmbossStyleManager.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/Http.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/Http.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/FixModelByWin10.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/FixModelByWin10.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/Bonjour.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/Bonjour.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/BBLUtil.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/BBLUtil.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/FileHelp.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/FileHelp.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/PresetUpdater.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/PresetUpdater.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/Process.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/Process.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/RaycastManager.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/RaycastManager.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/Profile.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/UndoRedo.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/UndoRedo.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/HexFile.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/HexFile.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/TCPConsole.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/TCPConsole.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/minilzo_extension.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/minilzo_extension.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/ColorSpaceConvert.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/ColorSpaceConvert.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/OctoPrint.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/OctoPrint.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/PrintHost.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/PrintHost.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/NetworkAgent.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/NetworkAgent.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/MKS.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/MKS.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/CpuMemory.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/CpuMemory.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/Duet.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/Duet.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/FlashAir.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/FlashAir.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/AstroBox.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/AstroBox.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/Repetier.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/Repetier.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/CalibUtils.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/CalibUtils.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/ProfileDescription.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/FontConfigHelp.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/FontConfigHelp.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/FontUtils.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/FontUtils.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/Utils/HelioDragon.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/slic3r/Utils/HelioDragon.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevBed.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevBed.h", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevConfig.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevConfig.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevConfigUtil.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevConfigUtil.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevCtrl.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevCtrl.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevDefs.h", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevExtruderSystem.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevExtruderSystem.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevExtruderSystemCtrl.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevFan.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevFan.h", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevFilaAmsSetting.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevFilaAmsSetting.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevFilaBlackList.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevFilaBlackList.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevFilaSystem.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevFilaSystem.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevFirmware.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevFirmware.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevPrintOptions.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevPrintOptions.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevPrintTaskInfo.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevPrintTaskInfo.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevHMS.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevHMS.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevStorage.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevStorage.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevInfo.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevInfo.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevLamp.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevLamp.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevLampCtrl.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevManager.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevManager.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevMapping.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevMapping.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevNozzleSystem.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevNozzleSystem.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceCore/DevUtil.h", "sourceGroupIndex": 9}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceCore/DevUtil.cpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceTab/uiAmsHumidityPopup.h", "sourceGroupIndex": 10}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceTab/uiAmsHumidityPopup.cpp", "sourceGroupIndex": 10}, {"backtrace": 1, "path": "src/slic3r/GUI/DeviceTab/uiDeviceUpdateVersion.h", "sourceGroupIndex": 10}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/slic3r/GUI/DeviceTab/uiDeviceUpdateVersion.cpp", "sourceGroupIndex": 10}, {"backtrace": 1, "compileGroupIndex": 2, "path": "src/slic3r/Utils/RetinaHelperImpl.mm", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 2, "path": "src/slic3r/Utils/MacDarkMode.mm", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 2, "path": "src/slic3r/GUI/RemovableDriveManagerMM.mm", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/RemovableDriveManagerMM.h", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 2, "path": "src/slic3r/GUI/Mouse3DHandlerMac.mm", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 2, "path": "src/slic3r/GUI/InstanceCheckMac.mm", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/InstanceCheckMac.h", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 2, "path": "src/slic3r/GUI/wxMediaCtrl2.mm", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/wxMediaCtrl2.h", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/slic3r/GUI/wxMediaCtrl3.h", "sourceGroupIndex": 4}, {"backtrace": 0, "path": "build/debug/src/slic3r/CMakeFiles/libslic3r_gui.dir/cmake_pch.hxx", "sourceGroupIndex": 11}], "type": "STATIC_LIBRARY"}