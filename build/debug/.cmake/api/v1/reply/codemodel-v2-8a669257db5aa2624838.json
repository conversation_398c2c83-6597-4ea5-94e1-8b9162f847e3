{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Debug-d8732754f855fef9fcbb.json", "minimumCMakeVersion": {"string": "3.3"}, "projectIndex": 0, "source": ".", "targetIndexes": [5, 6, 7]}, {"build": "src", "childIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21], "hasInstallRule": true, "jsonFile": "directory-src-Debug-e9312277345e8f9c0915.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 1, "source": "src", "targetIndexes": [0]}, {"build": "src/build-utils", "jsonFile": "directory-src.build-utils-Debug-02ed3e8cb81edc336f30.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 1, "projectIndex": 1, "source": "src/build-utils"}, {"build": "src/admesh", "jsonFile": "directory-src.admesh-Debug-55b4a6aad065ebd3f109.json", "minimumCMakeVersion": {"string": "2.8.12"}, "parentIndex": 1, "projectIndex": 2, "source": "src/admesh", "targetIndexes": [3]}, {"build": "src/boost", "jsonFile": "directory-src.boost-Debug-9ce05a965ba194f786c2.json", "minimumCMakeVersion": {"string": "2.8.12"}, "parentIndex": 1, "projectIndex": 3, "source": "src/boost", "targetIndexes": [20]}, {"build": "src/clipper", "jsonFile": "directory-src.clipper-Debug-9eaccf765053762700be.json", "minimumCMakeVersion": {"string": "2.8.12"}, "parentIndex": 1, "projectIndex": 4, "source": "src/clipper", "targetIndexes": [4]}, {"build": "src/clipper2", "jsonFile": "directory-src.clipper2-Debug-e2e121cac339c4ce5679.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 1, "projectIndex": 5, "source": "src/clipper2", "targetIndexes": [1]}, {"build": "src/miniz", "jsonFile": "directory-src.miniz-Debug-7251d6676e2de6a74d85.json", "minimumCMakeVersion": {"string": "2.8.12"}, "parentIndex": 1, "projectIndex": 6, "source": "src/miniz", "targetIndexes": [19]}, {"build": "src/minilzo", "jsonFile": "directory-src.minilzo-Debug-d9275628c768849e3707.json", "minimumCMakeVersion": {"string": "2.6"}, "parentIndex": 1, "projectIndex": 7, "source": "src/minilzo", "targetIndexes": [18]}, {"build": "src/glu-libtess", "jsonFile": "directory-src.glu-libtess-Debug-08886352526f0c2ac5e5.json", "minimumCMakeVersion": {"string": "2.8.12"}, "parentIndex": 1, "projectIndex": 8, "source": "src/glu-libtess", "targetIndexes": [8]}, {"build": "src/qhull", "jsonFile": "directory-src.qhull-Debug-f88d5edf021350b2721e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 1, "projectIndex": 1, "source": "src/qhull"}, {"build": "src/Shiny", "jsonFile": "directory-src.Shiny-Debug-e88e41700c0570ff4a8c.json", "minimumCMakeVersion": {"string": "2.8.12"}, "parentIndex": 1, "projectIndex": 9, "source": "src/Shiny", "targetIndexes": [2]}, {"build": "src/semver", "jsonFile": "directory-src.semver-Debug-6957387d2ae8832a8ff8.json", "minimumCMakeVersion": {"string": "2.8.12"}, "parentIndex": 1, "projectIndex": 10, "source": "src/semver", "targetIndexes": [21]}, {"build": "src/libigl", "jsonFile": "directory-src.libigl-Debug-b25bfb6fa05750a11dc9.json", "minimumCMakeVersion": {"string": "3.0"}, "parentIndex": 1, "projectIndex": 11, "source": "src/libigl"}, {"build": "src/hints", "jsonFile": "directory-src.hints-Debug-2b6ae74725b97f5bf895.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 1, "projectIndex": 12, "source": "src/hints", "targetIndexes": [10]}, {"build": "src/mcut", "hasInstallRule": true, "jsonFile": "directory-src.mcut-Debug-8018a7954edf8899a492.json", "minimumCMakeVersion": {"string": "3.12"}, "parentIndex": 1, "projectIndex": 13, "source": "src/mcut", "targetIndexes": [17]}, {"build": "src/libnest2d", "jsonFile": "directory-src.libnest2d-Debug-271ba47845885c42efca.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 1, "projectIndex": 1, "source": "src/libnest2d", "targetIndexes": [13]}, {"build": "src/libslic3r", "jsonFile": "directory-src.libslic3r-Debug-73addf39fd8e31064b18.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 1, "projectIndex": 14, "source": "src/libslic3r", "targetIndexes": [14, 15]}, {"build": "src/imgui", "jsonFile": "directory-src.imgui-Debug-142fd662c150e6570d76.json", "minimumCMakeVersion": {"string": "2.8.12"}, "parentIndex": 1, "projectIndex": 15, "source": "src/imgui", "targetIndexes": [11]}, {"build": "src/imguizmo", "jsonFile": "directory-src.imguizmo-Debug-9bcb9b657c0f5182d0bc.json", "minimumCMakeVersion": {"string": "2.8.12"}, "parentIndex": 1, "projectIndex": 16, "source": "src/imguizmo", "targetIndexes": [12]}, {"build": "src/hidapi", "jsonFile": "directory-src.hidapi-Debug-eeeea532b01509598633.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 1, "projectIndex": 1, "source": "src/hidapi", "targetIndexes": [9]}, {"build": "src/slic3r", "childIndexes": [22, 23], "jsonFile": "directory-src.slic3r-Debug-f076308bbe3f9abebf14.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 1, "projectIndex": 17, "source": "src/slic3r", "targetIndexes": [16]}, {"build": "src/slic3r/GUI/DeviceCore", "jsonFile": "directory-src.slic3r.GUI.DeviceCore-Debug-542c9f6f93220a8dd322.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 21, "projectIndex": 17, "source": "src/slic3r/GUI/DeviceCore"}, {"build": "src/slic3r/GUI/DeviceTab", "jsonFile": "directory-src.slic3r.GUI.DeviceTab-Debug-2d5d5807aa8bb80999f9.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 21, "projectIndex": 17, "source": "src/slic3r/GUI/DeviceTab"}], "name": "Debug", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "BambuStudio", "targetIndexes": [5, 6, 7]}, {"childIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "directoryIndexes": [1, 2, 10, 16, 20], "name": "BambuStudio-native", "parentIndex": 0, "targetIndexes": [0, 9, 13]}, {"directoryIndexes": [3], "name": "admesh", "parentIndex": 1, "targetIndexes": [3]}, {"directoryIndexes": [4], "name": "nowide", "parentIndex": 1, "targetIndexes": [20]}, {"directoryIndexes": [5], "name": "clipper", "parentIndex": 1, "targetIndexes": [4]}, {"directoryIndexes": [6], "name": "Clipper2", "parentIndex": 1, "targetIndexes": [1]}, {"directoryIndexes": [7], "name": "miniz", "parentIndex": 1, "targetIndexes": [19]}, {"directoryIndexes": [8], "name": "<PERSON><PERSON><PERSON>", "parentIndex": 1, "targetIndexes": [18]}, {"directoryIndexes": [9], "name": "glu-libtess", "parentIndex": 1, "targetIndexes": [8]}, {"directoryIndexes": [11], "name": "<PERSON>y", "parentIndex": 1, "targetIndexes": [2]}, {"directoryIndexes": [12], "name": "semver", "parentIndex": 1, "targetIndexes": [21]}, {"directoryIndexes": [13], "name": "libigl", "parentIndex": 1}, {"directoryIndexes": [14], "name": "HintsToPot", "parentIndex": 1, "targetIndexes": [10]}, {"directoryIndexes": [15], "name": "mcut", "parentIndex": 1, "targetIndexes": [17]}, {"directoryIndexes": [17], "name": "libslic3r", "parentIndex": 1, "targetIndexes": [14, 15]}, {"directoryIndexes": [18], "name": "imgui", "parentIndex": 1, "targetIndexes": [11]}, {"directoryIndexes": [19], "name": "<PERSON><PERSON><PERSON><PERSON>", "parentIndex": 1, "targetIndexes": [12]}, {"directoryIndexes": [21, 22, 23], "name": "libslic3r_gui", "parentIndex": 1, "targetIndexes": [16]}], "targets": [{"directoryIndex": 1, "id": "BambuStudio::@145eef247bfb46b6828c", "jsonFile": "target-BambuStudio-Debug-ec728e4da02867fa6753.json", "name": "BambuStudio", "projectIndex": 1}, {"directoryIndex": 6, "id": "Clipper2::@9bf50c2026546175e5f3", "jsonFile": "target-Clipper2-Debug-c25e9eec0858d9ddf73e.json", "name": "Clipper2", "projectIndex": 5}, {"directoryIndex": 11, "id": "Shiny::@117cb4be1eb20ad07e6e", "jsonFile": "target-Shiny-Debug-d7f831e9e79b6c1d9c76.json", "name": "<PERSON>y", "projectIndex": 9}, {"directoryIndex": 3, "id": "admesh::@db36e4f74c66f2bf2254", "jsonFile": "target-admesh-Debug-665f9746b6694bb904eb.json", "name": "admesh", "projectIndex": 2}, {"directoryIndex": 5, "id": "clipper::@81359c37bfb1d12160f2", "jsonFile": "target-clipper-Debug-382ab3c842e8faa61581.json", "name": "clipper", "projectIndex": 4}, {"directoryIndex": 0, "id": "gettext_make_pot::@6890427a1f51a3e7e1df", "jsonFile": "target-gettext_make_pot-Debug-75db453f351e16e71ce2.json", "name": "gettext_make_pot", "projectIndex": 0}, {"directoryIndex": 0, "id": "gettext_merge_po_with_pot::@6890427a1f51a3e7e1df", "jsonFile": "target-gettext_merge_po_with_pot-Debug-c96316f1cc2048c06f05.json", "name": "gettext_merge_po_with_pot", "projectIndex": 0}, {"directoryIndex": 0, "id": "gettext_po_to_mo::@6890427a1f51a3e7e1df", "jsonFile": "target-gettext_po_to_mo-Debug-983a4e608a717084d0d0.json", "name": "gettext_po_to_mo", "projectIndex": 0}, {"directoryIndex": 9, "id": "glu-libtess::@86057b0eb26356d52b06", "jsonFile": "target-glu-libtess-Debug-a2bd89de14f4e0c820c4.json", "name": "glu-libtess", "projectIndex": 8}, {"directoryIndex": 20, "id": "hidapi::@1f7e0fad1acc4ee8b79a", "jsonFile": "target-hidapi-Debug-618eb97e6154c95ed5cd.json", "name": "<PERSON><PERSON>i", "projectIndex": 1}, {"directoryIndex": 14, "id": "hintsToPot::@4b0869cd280e687ffba6", "jsonFile": "target-hintsToPot-Debug-d29a06e4ae74e9acf222.json", "name": "hintsToPot", "projectIndex": 12}, {"directoryIndex": 18, "id": "imgui::@f0bb1e571c12c8d458e0", "jsonFile": "target-imgui-Debug-f722670c5f3824f6e434.json", "name": "imgui", "projectIndex": 15}, {"directoryIndex": 19, "id": "imguizmo::@67d194951d60576ffe05", "jsonFile": "target-imguizmo-Debug-8b955c165f16c203311a.json", "name": "<PERSON><PERSON><PERSON><PERSON>", "projectIndex": 16}, {"directoryIndex": 16, "id": "libnest2d::@8f4a5c5a495c7f0c1447", "jsonFile": "target-libnest2d-Debug-5ea763a32401d9d0fcb4.json", "name": "libnest2d", "projectIndex": 1}, {"directoryIndex": 17, "id": "libslic3r::@87deb467dcbeda20dc6b", "jsonFile": "target-libslic3r-Debug-1f12e5f01d3529fbac18.json", "name": "libslic3r", "projectIndex": 14}, {"directoryIndex": 17, "id": "libslic3r_cgal::@87deb467dcbeda20dc6b", "jsonFile": "target-libslic3r_cgal-Debug-fd86a3335746d158b4df.json", "name": "libslic3r_cgal", "projectIndex": 14}, {"directoryIndex": 21, "id": "libslic3r_gui::@1086e93791e4332ab3a6", "jsonFile": "target-libslic3r_gui-Debug-6540617d7889a0f54238.json", "name": "libslic3r_gui", "projectIndex": 17}, {"directoryIndex": 15, "id": "mcut::@a392ea28d34c20f491b4", "jsonFile": "target-mcut-Debug-329065fe0e2105ec0660.json", "name": "mcut", "projectIndex": 13}, {"directoryIndex": 8, "id": "minilzo_static::@a95d82a94b3f20e643da", "jsonFile": "target-minilzo_static-Debug-9738d11f76f734644a7a.json", "name": "minilzo_static", "projectIndex": 7}, {"directoryIndex": 7, "id": "miniz_static::@8c4610aaeb52d7e4118f", "jsonFile": "target-miniz_static-Debug-3defe9d5a8cc074457be.json", "name": "miniz_static", "projectIndex": 6}, {"directoryIndex": 4, "id": "nowide::@34b20850f9a50713ec38", "jsonFile": "target-nowide-Debug-37a49fffabe2a7f222b8.json", "name": "nowide", "projectIndex": 3}, {"directoryIndex": 12, "id": "semver::@9d48bd82e9816d725e2b", "jsonFile": "target-semver-Debug-58ef0a8d6888322955a2.json", "name": "semver", "projectIndex": 10}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug", "source": "/Users/<USER>/Documents/augment-projects/BambuStudio"}, "version": {"major": 2, "minor": 7}}