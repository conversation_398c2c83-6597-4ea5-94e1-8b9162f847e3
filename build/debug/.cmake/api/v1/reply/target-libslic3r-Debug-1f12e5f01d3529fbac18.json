{"archive": {}, "artifacts": [{"path": "src/libslic3r/liblibslic3r.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_options", "add_compile_definitions", "add_definitions", "target_compile_definitions", "include_directories", "target_include_directories", "target_precompile_headers", "add_precompiled_header"], "files": ["src/libslic3r/CMakeLists.txt", "CMakeLists.txt", "cmake/modules/PrecompiledHeader.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 480, "parent": 0}, {"command": 1, "file": 0, "line": 574, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 139, "parent": 3}, {"command": 2, "file": 1, "line": 173, "parent": 3}, {"command": 2, "file": 1, "line": 257, "parent": 3}, {"command": 2, "file": 1, "line": 259, "parent": 3}, {"command": 2, "file": 1, "line": 264, "parent": 3}, {"command": 2, "file": 1, "line": 271, "parent": 3}, {"command": 2, "file": 1, "line": 275, "parent": 3}, {"command": 1, "file": 0, "line": 613, "parent": 0}, {"command": 3, "file": 1, "line": 130, "parent": 3}, {"command": 4, "file": 1, "line": 96, "parent": 3}, {"command": 4, "file": 1, "line": 92, "parent": 3}, {"command": 5, "file": 0, "line": 535, "parent": 0}, {"command": 4, "file": 1, "line": 339, "parent": 3}, {"command": 4, "file": 1, "line": 342, "parent": 3}, {"command": 6, "file": 1, "line": 322, "parent": 3}, {"command": 6, "file": 1, "line": 324, "parent": 3}, {"command": 7, "file": 0, "line": 536, "parent": 0}, {"command": 7, "file": 0, "line": 542, "parent": 0}, {"command": 6, "file": 1, "line": 491, "parent": 3}, {"command": 6, "file": 1, "line": 326, "parent": 3}, {"command": 7, "file": 0, "line": 537, "parent": 0}, {"command": 9, "file": 0, "line": 625, "parent": 0}, {"command": 8, "file": 2, "line": 257, "parent": 25}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics"}, {"backtrace": 4, "fragment": "-f<PERSON>-char"}, {"backtrace": 5, "fragment": "-DDEBUG"}, {"backtrace": 6, "fragment": "-Werror=return-type"}, {"backtrace": 7, "fragment": "-Wno-unused-function"}, {"backtrace": 7, "fragment": "-Wno-unused-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-label"}, {"backtrace": 7, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 8, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 9, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 10, "fragment": "-Wno-error=enum-constexpr-conversion"}, {"backtrace": 11, "fragment": "-DOPENVDB_ABI_VERSION_NUMBER=8"}, {"fragment": "-Winvalid-pch -Xclang -emit-pch -Xclang -include -Xclang /Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch.hxx -x c++-header"}], "defines": [{"backtrace": 2, "define": "BOOST_ASIO_DISABLE_KQUEUE"}, {"backtrace": 2, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 2, "define": "BOOST_ATOMIC_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CHRONO_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_CONTAINER_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CONTAINER_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_STATIC_LINK=1"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_LOCALE_NO_LIB"}, {"backtrace": 2, "define": "BOOST_LOCALE_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_LOG_NO_LIB"}, {"backtrace": 2, "define": "BOOST_LOG_STATIC_LINK"}, {"backtrace": 12, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 2, "define": "BOOST_RANDOM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_RANDOM_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 2, "define": "BOOST_THREAD_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_THREAD_USE_LIB"}, {"backtrace": 2, "define": "LIBNEST2D_GEOMETRIES_libslic3r"}, {"backtrace": 2, "define": "LIBNEST2D_OPTIMIZER_nlopt"}, {"backtrace": 2, "define": "LIBNEST2D_STATIC"}, {"backtrace": 2, "define": "LIBNEST2D_THREADING_tbb"}, {"backtrace": 2, "define": "OPENVDB_OPENEXR_STATICLIB"}, {"backtrace": 2, "define": "OPENVDB_STATICLIB"}, {"backtrace": 13, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 14, "define": "SLIC3R_GUI"}, {"backtrace": 15, "define": "TBB_USE_CAPTURED_EXCEPTION=0"}, {"backtrace": 2, "define": "TBB_USE_DEBUG"}, {"backtrace": 16, "define": "UNICODE"}, {"backtrace": 15, "define": "USE_TBB"}, {"backtrace": 16, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 12, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 16, "define": "_UNICODE"}, {"backtrace": 17, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 16, "define": "wxUSE_UNICODE"}], "frameworks": [{"path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework"}, {"path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ModelIO.framework"}], "includes": [{"backtrace": 18, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 19, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 20, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libslic3r"}, {"backtrace": 20, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r"}, {"backtrace": 21, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencascade"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libnest2d/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/miniz"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/glu-libtess/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/clipper2/Clipper2Lib/include"}, {"backtrace": 22, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}, {"backtrace": 23, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 24, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencv4"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/OpenEXR"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2, 2, 2, 2, 11], "standard": "17"}, "precompileHeaders": [{"backtrace": 26, "header": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libslic3r/pchheader.hpp"}], "sourceIndexes": [0]}, {"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics"}, {"backtrace": 4, "fragment": "-f<PERSON>-char"}, {"backtrace": 5, "fragment": "-DDEBUG"}, {"backtrace": 6, "fragment": "-Werror=return-type"}, {"backtrace": 7, "fragment": "-Wno-unused-function"}, {"backtrace": 7, "fragment": "-Wno-unused-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-label"}, {"backtrace": 7, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 8, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 9, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 10, "fragment": "-Wno-error=enum-constexpr-conversion"}, {"backtrace": 11, "fragment": "-DOPENVDB_ABI_VERSION_NUMBER=8"}, {"fragment": "-Winvalid-pch -Xclang -include-pch -Xclang /Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch.hxx.pch -Xclang -include -Xclang /Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch.hxx"}], "defines": [{"backtrace": 2, "define": "BOOST_ASIO_DISABLE_KQUEUE"}, {"backtrace": 2, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 2, "define": "BOOST_ATOMIC_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CHRONO_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_CONTAINER_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CONTAINER_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_STATIC_LINK=1"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_LOCALE_NO_LIB"}, {"backtrace": 2, "define": "BOOST_LOCALE_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_LOG_NO_LIB"}, {"backtrace": 2, "define": "BOOST_LOG_STATIC_LINK"}, {"backtrace": 12, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 2, "define": "BOOST_RANDOM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_RANDOM_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 2, "define": "BOOST_THREAD_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_THREAD_USE_LIB"}, {"backtrace": 2, "define": "LIBNEST2D_GEOMETRIES_libslic3r"}, {"backtrace": 2, "define": "LIBNEST2D_OPTIMIZER_nlopt"}, {"backtrace": 2, "define": "LIBNEST2D_STATIC"}, {"backtrace": 2, "define": "LIBNEST2D_THREADING_tbb"}, {"backtrace": 2, "define": "OPENVDB_OPENEXR_STATICLIB"}, {"backtrace": 2, "define": "OPENVDB_STATICLIB"}, {"backtrace": 13, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 14, "define": "SLIC3R_GUI"}, {"backtrace": 15, "define": "TBB_USE_CAPTURED_EXCEPTION=0"}, {"backtrace": 2, "define": "TBB_USE_DEBUG"}, {"backtrace": 16, "define": "UNICODE"}, {"backtrace": 15, "define": "USE_TBB"}, {"backtrace": 16, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 12, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 16, "define": "_UNICODE"}, {"backtrace": 17, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 16, "define": "wxUSE_UNICODE"}], "frameworks": [{"path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework"}, {"path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ModelIO.framework"}], "includes": [{"backtrace": 18, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 19, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 20, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libslic3r"}, {"backtrace": 20, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r"}, {"backtrace": 21, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencascade"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libnest2d/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/miniz"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/glu-libtess/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/clipper2/Clipper2Lib/include"}, {"backtrace": 22, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}, {"backtrace": 23, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 24, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencv4"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/OpenEXR"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2, 2, 2, 2, 11], "standard": "17"}, "precompileHeaders": [{"backtrace": 26, "header": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libslic3r/pchheader.hpp"}], "sourceIndexes": [1, 3, 8, 9, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 36, 38, 40, 42, 44, 46, 50, 53, 55, 57, 59, 61, 64, 66, 68, 70, 72, 74, 76, 78, 80, 82, 84, 86, 88, 90, 92, 94, 96, 99, 100, 102, 104, 106, 109, 111, 113, 115, 117, 119, 121, 124, 126, 127, 129, 131, 133, 135, 137, 139, 141, 143, 145, 147, 149, 151, 153, 155, 157, 159, 161, 163, 166, 168, 171, 173, 175, 178, 179, 183, 186, 188, 190, 192, 194, 196, 198, 201, 202, 205, 207, 210, 211, 213, 216, 217, 220, 222, 224, 226, 229, 230, 232, 234, 236, 238, 240, 242, 244, 246, 248, 251, 252, 254, 256, 257, 259, 261, 262, 263, 265, 266, 268, 269, 271, 273, 274, 277, 280, 281, 283, 285, 288, 290, 292, 296, 297, 299, 302, 305, 307, 309, 313, 314, 316, 318, 320, 322, 324, 327, 330, 332, 334, 343, 346, 348, 349, 352, 354, 357, 359, 362, 364, 366, 370, 372, 374, 377, 379, 381, 383, 385, 387, 389, 391, 393, 395, 403, 407, 409, 412, 415, 417, 419, 422, 424, 426, 428, 430, 432, 437]}, {"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIC -fcolor-diagnostics"}, {"backtrace": 4, "fragment": "-f<PERSON>-char"}, {"backtrace": 5, "fragment": "-DDEBUG"}, {"backtrace": 6, "fragment": "-Werror=return-type"}, {"backtrace": 7, "fragment": "-Wno-unused-function"}, {"backtrace": 7, "fragment": "-Wno-unused-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 7, "fragment": "-Wno-unused-label"}, {"backtrace": 7, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 8, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 9, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 10, "fragment": "-Wno-error=enum-constexpr-conversion"}, {"backtrace": 11, "fragment": "-DOPENVDB_ABI_VERSION_NUMBER=8"}], "defines": [{"backtrace": 2, "define": "BOOST_ASIO_DISABLE_KQUEUE"}, {"backtrace": 2, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 2, "define": "BOOST_ATOMIC_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CHRONO_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_CONTAINER_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CONTAINER_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_STATIC_LINK=1"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_LOCALE_NO_LIB"}, {"backtrace": 2, "define": "BOOST_LOCALE_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_LOG_NO_LIB"}, {"backtrace": 2, "define": "BOOST_LOG_STATIC_LINK"}, {"backtrace": 12, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 2, "define": "BOOST_RANDOM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_RANDOM_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 2, "define": "BOOST_THREAD_STATIC_LINK"}, {"backtrace": 2, "define": "BOOST_THREAD_USE_LIB"}, {"backtrace": 2, "define": "LIBNEST2D_GEOMETRIES_libslic3r"}, {"backtrace": 2, "define": "LIBNEST2D_OPTIMIZER_nlopt"}, {"backtrace": 2, "define": "LIBNEST2D_STATIC"}, {"backtrace": 2, "define": "LIBNEST2D_THREADING_tbb"}, {"backtrace": 2, "define": "OPENVDB_OPENEXR_STATICLIB"}, {"backtrace": 2, "define": "OPENVDB_STATICLIB"}, {"backtrace": 13, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 14, "define": "SLIC3R_GUI"}, {"backtrace": 15, "define": "TBB_USE_CAPTURED_EXCEPTION=0"}, {"backtrace": 2, "define": "TBB_USE_DEBUG"}, {"backtrace": 16, "define": "UNICODE"}, {"backtrace": 15, "define": "USE_TBB"}, {"backtrace": 16, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 12, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 16, "define": "_UNICODE"}, {"backtrace": 17, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 16, "define": "wxUSE_UNICODE"}], "frameworks": [{"path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework"}, {"path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ModelIO.framework"}], "includes": [{"backtrace": 18, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 19, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 20, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libslic3r"}, {"backtrace": 20, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r"}, {"backtrace": 21, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencascade"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libnest2d/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/miniz"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/glu-libtess/include"}, {"backtrace": 2, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/clipper2/Clipper2Lib/include"}, {"backtrace": 22, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}, {"backtrace": 23, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 24, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencv4"}, {"backtrace": 2, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/OpenEXR"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2, 2, 2, 2, 11], "standard": "17"}, "precompileHeaders": [{"backtrace": 26, "header": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libslic3r/pchheader.hpp"}], "sourceIndexes": [433, 435]}], "dependencies": [{"backtrace": 2, "id": "admesh::@db36e4f74c66f2bf2254"}, {"backtrace": 2, "id": "nowide::@34b20850f9a50713ec38"}, {"backtrace": 2, "id": "clipper::@81359c37bfb1d12160f2"}, {"backtrace": 2, "id": "Clipper2::@9bf50c2026546175e5f3"}, {"backtrace": 2, "id": "miniz_static::@8c4610aaeb52d7e4118f"}, {"backtrace": 2, "id": "glu-libtess::@86057b0eb26356d52b06"}, {"backtrace": 2, "id": "semver::@9d48bd82e9816d725e2b"}, {"backtrace": 2, "id": "mcut::@a392ea28d34c20f491b4"}, {"backtrace": 2, "id": "libslic3r_cgal::@87deb467dcbeda20dc6b"}], "id": "libslic3r::@87deb467dcbeda20dc6b", "name": "libslic3r", "nameOnDisk": "liblibslic3r.a", "paths": {"build": "src/libslic3r", "source": "src/libslic3r"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 437]}, {"name": "", "sourceIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 100, 101, 102, 103, 104, 105, 106, 107, 108, 157, 158, 159, 160, 161, 162, 163, 164, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 418, 419, 420, 421, 422, 423, 424, 425, 426, 433]}, {"name": "Algorithm\\LineSegmentation", "sourceIndexes": [9, 10]}, {"name": "Fill", "sourceIndexes": [64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 96, 97, 98, 99]}, {"name": "Fill\\Lightning", "sourceIndexes": [88, 89, 90, 91, 92, 93, 94, 95]}, {"name": "Format", "sourceIndexes": [109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 434, 435]}, {"name": "GCode", "sourceIndexes": [127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 427, 428]}, {"name": "Geometry", "sourceIndexes": [165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181]}, {"name": "Support", "sourceIndexes": [283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294]}, {"name": "Execution", "sourceIndexes": [336, 337, 338]}, {"name": "Optimize", "sourceIndexes": [339, 340, 341]}, {"name": "SLA", "sourceIndexes": [342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375]}, {"name": "Arachne\\BeadingStrategy", "sourceIndexes": [376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391]}, {"name": "Arachne\\utils", "sourceIndexes": [392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407]}, {"name": "<PERSON><PERSON><PERSON>", "sourceIndexes": [408, 409, 410, 411, 412, 413, 414, 415]}, {"name": "<PERSON><PERSON><PERSON>", "sourceIndexes": [416, 417]}, {"name": "Interlocking", "sourceIndexes": [429, 430, 431, 432]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [436, 438]}, {"name": "Precompile Header File", "sourceIndexes": [439]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "path": "build/debug/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch.hxx.cxx", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ArcFitter.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ArcFitter.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/pchheader.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/pchheader.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/AABBTreeIndirect.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/AABBTreeLines.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/AABBMesh.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/AABBMesh.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Algorithm/LineSegmentation/LineSegmentation.cpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "src/libslic3r/Algorithm/LineSegmentation/LineSegmentation.hpp", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "src/libslic3r/AnyPtr.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/AStar.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/BoundingBox.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/BoundingBox.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/BridgeDetector.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/BridgeDetector.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/FaceDetector.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/FaceDetector.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Brim.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Brim.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/BuildVolume.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/BuildVolume.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Calib.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Calib.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Circle.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Circle.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/clipper.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/clipper.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ClipperUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ClipperUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Clipper2Utils.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Clipper2Utils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Color.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Color.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/CommonDefs.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Config.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Config.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/CurveAnalyzer.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/CurveAnalyzer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/CutUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/CutUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/EdgeGrid.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/EdgeGrid.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ElephantFootCompensation.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ElephantFootCompensation.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Emboss.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Emboss.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/EmbossShape.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/enum_bitmask.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ExPolygon.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ExPolygon.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ExPolygonSerialize.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ExPolygonsIndex.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ExPolygonsIndex.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Extruder.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Extruder.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ExtrusionEntity.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ExtrusionEntity.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ExtrusionEntityCollection.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ExtrusionEntityCollection.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ExtrusionSimulator.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ExtrusionSimulator.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/FileParserError.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/Fill.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/Fill.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/Fill3DHoneycomb.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/Fill3DHoneycomb.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/FillAdaptive.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/FillAdaptive.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/FillBase.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/FillBase.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/FillConcentric.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/FillConcentric.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/FillConcentricInternal.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/FillConcentricInternal.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/FillCrossHatch.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/FillCrossHatch.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/FillHoneycomb.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/FillHoneycomb.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/FillGyroid.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/FillGyroid.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/FillPlanePath.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/FillPlanePath.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/FillLine.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/FillLine.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/FillLightning.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/FillLightning.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/Lightning/DistanceField.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/libslic3r/Fill/Lightning/DistanceField.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/Lightning/Generator.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/libslic3r/Fill/Lightning/Generator.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/Lightning/Layer.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/libslic3r/Fill/Lightning/Layer.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/Lightning/TreeNode.cpp", "sourceGroupIndex": 4}, {"backtrace": 1, "path": "src/libslic3r/Fill/Lightning/TreeNode.hpp", "sourceGroupIndex": 4}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/FillRectilinear.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/FillRectilinear.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "path": "src/libslic3r/Fill/FillFloatingConcentric.hpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Fill/FillFloatingConcentric.cpp", "sourceGroupIndex": 3}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Flow.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Flow.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Frustum.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Frustum.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/FlushVolCalc.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/FlushVolCalc.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/FuzzySkin.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/FuzzySkin.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/format.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Format/3mf.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/libslic3r/Format/3mf.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Format/bbs_3mf.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/libslic3r/Format/bbs_3mf.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Format/AMF.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/libslic3r/Format/AMF.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Format/OBJ.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/libslic3r/Format/OBJ.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Format/objparser.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/libslic3r/Format/objparser.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Format/STEP.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/libslic3r/Format/STEP.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Format/STL.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/libslic3r/Format/STL.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/libslic3r/Format/SL1.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Format/SL1.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "src/libslic3r/Format/svg.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Format/svg.cpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/ThumbnailData.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/ThumbnailData.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/GCodeEditor.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/GCodeEditor.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/PostProcessor.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/PostProcessor.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/PrintExtents.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/PrintExtents.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/RetractWhenCrossingPerimeters.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/RetractWhenCrossingPerimeters.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/SpiralVase.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/SpiralVase.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/SeamPlacer.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/SeamPlacer.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/ToolOrdering.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/ToolOrdering.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/WipeTower.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/WipeTower.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/GCodeProcessor.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/GCodeProcessor.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/AvoidCrossingPerimeters.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/AvoidCrossingPerimeters.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/ConflictChecker.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/ConflictChecker.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/Smoothing.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/Smoothing.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/CoolingBuffer.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/CoolingBuffer.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/TimelapsePosPicker.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/GCode/TimelapsePosPicker.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/GCode.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCodeReader.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/GCodeReader.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCodeWriter.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/GCodeWriter.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Geometry.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Geometry.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Geometry/Bicubic.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Geometry/Circle.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/libslic3r/Geometry/Circle.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Geometry/ConvexHull.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/libslic3r/Geometry/ConvexHull.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/libslic3r/Geometry/Curves.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Geometry/MedialAxis.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/libslic3r/Geometry/MedialAxis.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Geometry/Voronoi.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/libslic3r/Geometry/Voronoi.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Geometry/VoronoiOffset.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/libslic3r/Geometry/VoronoiOffset.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/libslic3r/Geometry/VoronoiUtils.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Geometry/VoronoiUtils.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Geometry/VoronoiUtilsCgal.cpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/libslic3r/Geometry/VoronoiUtilsCgal.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/libslic3r/Geometry/VoronoiVisualUtils.hpp", "sourceGroupIndex": 7}, {"backtrace": 1, "path": "src/libslic3r/Int128.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/InternalBridgeDetector.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/InternalBridgeDetector.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/JumpPointSearch.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/JumpPointSearch.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/KDTreeIndirect.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Layer.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Layer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/LayerRegion.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/libslic3r.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Line.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Line.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/BlacklistedLibraryCheck.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/BlacklistedLibraryCheck.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/LocalesUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/LocalesUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Model.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Model.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ModelArrange.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ModelArrange.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/MultiMaterialSegmentation.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/MultiMaterialSegmentation.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Measure.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Measure.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/MeasureUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/CustomGCode.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/CustomGCode.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Arrange.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arrange.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/NormalUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/NormalUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ObjColorUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ObjColorUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Orient.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Orient.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/MultiPoint.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/MultiPoint.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/MutablePriorityQueue.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ObjectID.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ObjectID.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/NSVGUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/NSVGUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ParameterUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ParameterUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/PerimeterGenerator.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/PerimeterGenerator.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/OverhangDetector.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/OverhangDetector.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/PlaceholderParser.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/PlaceholderParser.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Platform.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Platform.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Point.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Point.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Polygon.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Polygon.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/MutablePolygon.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/MutablePolygon.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/PolygonTrimmer.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/PolygonTrimmer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Polyline.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Polyline.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Preset.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Preset.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/PresetBundle.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/PresetBundle.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ProjectTask.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ProjectTask.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/PrincipalComponents2D.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/PrincipalComponents2D.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/AppConfig.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/AppConfig.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Print.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Print.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/PrintApply.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/PrintBase.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/PrintBase.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/PrintConfig.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/PrintConfig.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/PrintObject.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/PrintObjectSlice.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/PrintRegion.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/PNGReadWrite.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/PNGReadWrite.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/QuadricEdgeCollapse.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/QuadricEdgeCollapse.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Semver.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ShortEdgeCollapse.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ShortEdgeCollapse.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/ShortestPath.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ShortestPath.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLAPrint.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLAPrintSteps.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/SLAPrintSteps.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/SLAPrint.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Slicing.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Slicing.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/SlicesToTriangleMesh.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SlicesToTriangleMesh.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SlicingAdaptive.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/SlicingAdaptive.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Support/SupportCommon.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/libslic3r/Support/SupportCommon.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Support/SupportMaterial.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/libslic3r/Support/SupportMaterial.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/libslic3r/Support/TreeSupport.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Support/TreeSupport.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/libslic3r/Support/TreeSupport3D.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Support/TreeSupport3D.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/libslic3r/Support/TreeModelVolumes.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Support/TreeModelVolumes.cpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/libslic3r/Support/TreeSupportCommon.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/libslic3r/Support/SupportParameters.hpp", "sourceGroupIndex": 8}, {"backtrace": 1, "path": "src/libslic3r/MinimumSpanningTree.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/MinimumSpanningTree.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Surface.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Surface.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SurfaceCollection.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/SurfaceCollection.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/SurfaceMesh.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SVG.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/SVG.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Technologies.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Tesselate.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Tesselate.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/TriangleMesh.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/TriangleMesh.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/TriangleMeshSlicer.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/TriangleMeshSlicer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/MeshSplitImpl.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/TriangulateWall.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/TriangulateWall.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/utils.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Utils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Time.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Time.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Timer.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Timer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Thread.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Thread.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/TriangleSelector.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/TriangleSelector.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/TriangleSetSampling.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/TriangleSetSampling.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/MTUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/VariableWidth.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/VariableWidth.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Zipper.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Zipper.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/MinAreaBoundingBox.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/MinAreaBoundingBox.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/miniz_extension.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/miniz_extension.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/MarchingSquares.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Execution/Execution.hpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/libslic3r/Execution/ExecutionSeq.hpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/libslic3r/Execution/ExecutionTBB.hpp", "sourceGroupIndex": 9}, {"backtrace": 1, "path": "src/libslic3r/Optimize/Optimizer.hpp", "sourceGroupIndex": 10}, {"backtrace": 1, "path": "src/libslic3r/Optimize/NLoptOptimizer.hpp", "sourceGroupIndex": 10}, {"backtrace": 1, "path": "src/libslic3r/Optimize/BruteforceOptimizer.hpp", "sourceGroupIndex": 10}, {"backtrace": 1, "path": "src/libslic3r/SLA/Pad.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/Pad.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/SupportTreeBuilder.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/SupportTreeMesher.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/SupportTreeMesher.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/SupportTreeBuildsteps.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/SupportTreeBuildsteps.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/SupportTreeBuilder.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/Concurrency.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/SupportTree.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/SupportTree.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/Rotfinder.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/Rotfinder.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/BoostAdapter.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/SpatIndex.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/SpatIndex.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/RasterBase.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/RasterBase.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/AGGRaster.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/RasterToPolygons.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/RasterToPolygons.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/ConcaveHull.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/ConcaveHull.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/Hollowing.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/Hollowing.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/JobController.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/SupportPoint.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/SupportPointGenerator.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/SupportPointGenerator.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/IndexedMesh.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/IndexedMesh.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/Clustering.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/SLA/Clustering.cpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/SLA/ReprojectPointsOnMesh.hpp", "sourceGroupIndex": 11}, {"backtrace": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/BeadingStrategy.hpp", "sourceGroupIndex": 12}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Ara<PERSON>ne/BeadingStrategy/BeadingStrategy.cpp", "sourceGroupIndex": 12}, {"backtrace": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/BeadingStrategyFactory.hpp", "sourceGroupIndex": 12}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/BeadingStrategyFactory.cpp", "sourceGroupIndex": 12}, {"backtrace": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/DistributedBeadingStrategy.hpp", "sourceGroupIndex": 12}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/DistributedBeadingStrategy.cpp", "sourceGroupIndex": 12}, {"backtrace": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/LimitedBeadingStrategy.hpp", "sourceGroupIndex": 12}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/LimitedBeadingStrategy.cpp", "sourceGroupIndex": 12}, {"backtrace": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/OuterWallInsetBeadingStrategy.hpp", "sourceGroupIndex": 12}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/OuterWallInsetBeadingStrategy.cpp", "sourceGroupIndex": 12}, {"backtrace": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/RedistributeBeadingStrategy.hpp", "sourceGroupIndex": 12}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/RedistributeBeadingStrategy.cpp", "sourceGroupIndex": 12}, {"backtrace": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/WideningBeadingStrategy.hpp", "sourceGroupIndex": 12}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/WideningBeadingStrategy.cpp", "sourceGroupIndex": 12}, {"backtrace": 1, "path": "src/libslic3r/Arachne/BeadingStrategy/OuterWallContourStrategy.hpp", "sourceGroupIndex": 12}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Ara<PERSON>ne/BeadingStrategy/OuterWallContourStrategy.cpp", "sourceGroupIndex": 12}, {"backtrace": 1, "path": "src/libslic3r/Arachne/utils/ExtrusionJunction.hpp", "sourceGroupIndex": 13}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/utils/ExtrusionJunction.cpp", "sourceGroupIndex": 13}, {"backtrace": 1, "path": "src/libslic3r/Arachne/utils/ExtrusionLine.hpp", "sourceGroupIndex": 13}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/utils/ExtrusionLine.cpp", "sourceGroupIndex": 13}, {"backtrace": 1, "path": "src/libslic3r/Arachne/utils/HalfEdge.hpp", "sourceGroupIndex": 13}, {"backtrace": 1, "path": "src/libslic3r/Arachne/utils/HalfEdgeGraph.hpp", "sourceGroupIndex": 13}, {"backtrace": 1, "path": "src/libslic3r/Arachne/utils/HalfEdgeNode.hpp", "sourceGroupIndex": 13}, {"backtrace": 1, "path": "src/libslic3r/Arachne/utils/SparseGrid.hpp", "sourceGroupIndex": 13}, {"backtrace": 1, "path": "src/libslic3r/Arachne/utils/SparsePointGrid.hpp", "sourceGroupIndex": 13}, {"backtrace": 1, "path": "src/libslic3r/Arachne/utils/SparseLineGrid.hpp", "sourceGroupIndex": 13}, {"backtrace": 1, "path": "src/libslic3r/Arachne/utils/SquareGrid.hpp", "sourceGroupIndex": 13}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/utils/SquareGrid.cpp", "sourceGroupIndex": 13}, {"backtrace": 1, "path": "src/libslic3r/Arachne/utils/PolygonsPointIndex.hpp", "sourceGroupIndex": 13}, {"backtrace": 1, "path": "src/libslic3r/Arachne/utils/PolygonsSegmentIndex.hpp", "sourceGroupIndex": 13}, {"backtrace": 1, "path": "src/libslic3r/Arachne/utils/PolylineStitcher.hpp", "sourceGroupIndex": 13}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/utils/PolylineStitcher.cpp", "sourceGroupIndex": 13}, {"backtrace": 1, "path": "src/libslic3r/Arachne/SkeletalTrapezoidation.hpp", "sourceGroupIndex": 14}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/SkeletalTrapezoidation.cpp", "sourceGroupIndex": 14}, {"backtrace": 1, "path": "src/libslic3r/Arachne/SkeletalTrapezoidationEdge.hpp", "sourceGroupIndex": 14}, {"backtrace": 1, "path": "src/libslic3r/Arachne/SkeletalTrapezoidationGraph.hpp", "sourceGroupIndex": 14}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/SkeletalTrapezoidationGraph.cpp", "sourceGroupIndex": 14}, {"backtrace": 1, "path": "src/libslic3r/Arachne/SkeletalTrapezoidationJoint.hpp", "sourceGroupIndex": 14}, {"backtrace": 1, "path": "src/libslic3r/Arachne/WallToolPaths.hpp", "sourceGroupIndex": 14}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Arachne/WallToolPaths.cpp", "sourceGroupIndex": 14}, {"backtrace": 1, "path": "src/libslic3r/Shape/TextShape.hpp", "sourceGroupIndex": 15}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Shape/TextShape.cpp", "sourceGroupIndex": 15}, {"backtrace": 1, "path": "src/libslic3r/RegionExpansion.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/RegionExpansion.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/ClipperZUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/FlushVolPredictor.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/FlushVolPredictor.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/FilamentGroup.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/FilamentGroup.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/FilamentGroupUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/FilamentGroupUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/GCode/ToolOrderUtils.hpp", "sourceGroupIndex": 6}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/GCode/ToolOrderUtils.cpp", "sourceGroupIndex": 6}, {"backtrace": 1, "path": "src/libslic3r/Interlocking/InterlockingGenerator.hpp", "sourceGroupIndex": 16}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Interlocking/InterlockingGenerator.cpp", "sourceGroupIndex": 16}, {"backtrace": 1, "path": "src/libslic3r/Interlocking/VoxelUtils.hpp", "sourceGroupIndex": 16}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/Interlocking/VoxelUtils.cpp", "sourceGroupIndex": 16}, {"backtrace": 1, "compileGroupIndex": 2, "path": "src/libslic3r/MacUtils.mm", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/libslic3r/Format/ModelIO.hpp", "sourceGroupIndex": 5}, {"backtrace": 1, "compileGroupIndex": 2, "path": "src/libslic3r/Format/ModelIO.mm", "sourceGroupIndex": 5}, {"backtrace": 1, "path": "build/debug/src/libslic3r/libslic3r_version.h", "sourceGroupIndex": 17}, {"backtrace": 1, "compileGroupIndex": 1, "path": "src/libslic3r/OpenVDBUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/libslic3r/OpenVDBUtils.hpp", "sourceGroupIndex": 17}, {"backtrace": 0, "path": "build/debug/src/libslic3r/CMakeFiles/libslic3r.dir/cmake_pch.hxx", "sourceGroupIndex": 18}], "type": "STATIC_LIBRARY"}