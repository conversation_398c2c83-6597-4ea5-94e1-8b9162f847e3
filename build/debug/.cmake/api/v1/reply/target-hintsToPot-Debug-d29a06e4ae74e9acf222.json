{"artifacts": [{"path": "src/hints/hintsToPot"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "boostcfg_find_component", "set_property", "add_compile_options", "add_compile_definitions", "add_definitions", "include_directories"], "files": ["src/hints/CMakeLists.txt", "CMakeLists.txt", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_asio-1.84.0/boost_asio-targets.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_asio-1.84.0/boost_asio-config.cmake", "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_log-1.84.0/boost_log-config.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfig.cmake", "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindBoost.cmake", "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindIconv.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_locale-1.84.0/boost_locale-config.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_thread-1.84.0/boost_thread-targets.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_thread-1.84.0/boost_thread-config.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_log-1.84.0/boost_log-targets.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0/boost_iostreams-targets.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0/boost_iostreams-config.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 413, "parent": 2}, {"command": 4, "file": 1, "line": 376, "parent": 2}, {"file": 7, "parent": 4}, {"command": 4, "file": 7, "line": 610, "parent": 5}, {"file": 6, "parent": 6}, {"command": 6, "file": 6, "line": 128, "parent": 7}, {"command": 4, "file": 6, "line": 67, "parent": 8}, {"file": 5, "parent": 9}, {"command": 5, "file": 5, "line": 93, "parent": 10}, {"command": 4, "file": 4, "line": 76, "parent": 11}, {"file": 3, "parent": 12}, {"command": 3, "file": 3, "line": 30, "parent": 13}, {"file": 2, "parent": 14}, {"command": 2, "file": 2, "line": 61, "parent": 15}, {"command": 6, "file": 6, "line": 128, "parent": 7}, {"command": 4, "file": 6, "line": 67, "parent": 17}, {"file": 9, "parent": 18}, {"command": 5, "file": 9, "line": 26, "parent": 19}, {"command": 4, "file": 4, "line": 76, "parent": 20}, {"file": 8, "parent": 21}, {"command": 7, "file": 8, "line": 185, "parent": 22}, {"command": 6, "file": 6, "line": 128, "parent": 7}, {"command": 4, "file": 6, "line": 67, "parent": 24}, {"file": 11, "parent": 25}, {"command": 3, "file": 11, "line": 83, "parent": 26}, {"file": 10, "parent": 27}, {"command": 2, "file": 10, "line": 61, "parent": 28}, {"command": 3, "file": 5, "line": 117, "parent": 10}, {"file": 12, "parent": 30}, {"command": 2, "file": 12, "line": 61, "parent": 31}, {"command": 6, "file": 6, "line": 128, "parent": 7}, {"command": 4, "file": 6, "line": 67, "parent": 33}, {"file": 14, "parent": 34}, {"command": 3, "file": 14, "line": 64, "parent": 35}, {"file": 13, "parent": 36}, {"command": 2, "file": 13, "line": 61, "parent": 37}, {"command": 8, "file": 1, "line": 139, "parent": 2}, {"command": 8, "file": 1, "line": 173, "parent": 2}, {"command": 8, "file": 1, "line": 257, "parent": 2}, {"command": 8, "file": 1, "line": 259, "parent": 2}, {"command": 8, "file": 1, "line": 264, "parent": 2}, {"command": 8, "file": 1, "line": 271, "parent": 2}, {"command": 8, "file": 1, "line": 275, "parent": 2}, {"command": 1, "file": 0, "line": 7, "parent": 0}, {"command": 9, "file": 1, "line": 130, "parent": 2}, {"command": 10, "file": 1, "line": 96, "parent": 2}, {"command": 10, "file": 1, "line": 92, "parent": 2}, {"command": 10, "file": 1, "line": 339, "parent": 2}, {"command": 10, "file": 1, "line": 342, "parent": 2}, {"command": 11, "file": 1, "line": 322, "parent": 2}, {"command": 11, "file": 1, "line": 324, "parent": 2}, {"command": 11, "file": 1, "line": 326, "parent": 2}, {"command": 11, "file": 1, "line": 491, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIE -fcolor-diagnostics"}, {"backtrace": 39, "fragment": "-f<PERSON>-char"}, {"backtrace": 40, "fragment": "-DDEBUG"}, {"backtrace": 41, "fragment": "-Werror=return-type"}, {"backtrace": 42, "fragment": "-Wno-unused-function"}, {"backtrace": 42, "fragment": "-Wno-unused-variable"}, {"backtrace": 42, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 42, "fragment": "-Wno-unused-label"}, {"backtrace": 42, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 43, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 44, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 45, "fragment": "-Wno-error=enum-constexpr-conversion"}], "defines": [{"backtrace": 46, "define": "BOOST_ASIO_DISABLE_KQUEUE"}, {"backtrace": 46, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 46, "define": "BOOST_ATOMIC_STATIC_LINK"}, {"backtrace": 46, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 46, "define": "BOOST_CHRONO_STATIC_LINK"}, {"backtrace": 46, "define": "BOOST_CONTAINER_NO_LIB"}, {"backtrace": 46, "define": "BOOST_CONTAINER_STATIC_LINK"}, {"backtrace": 46, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 46, "define": "BOOST_DATE_TIME_STATIC_LINK"}, {"backtrace": 46, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 46, "define": "BOOST_FILESYSTEM_STATIC_LINK=1"}, {"backtrace": 46, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 46, "define": "BOOST_IOSTREAMS_STATIC_LINK"}, {"backtrace": 46, "define": "BOOST_LOCALE_NO_LIB"}, {"backtrace": 46, "define": "BOOST_LOCALE_STATIC_LINK"}, {"backtrace": 46, "define": "BOOST_LOG_NO_LIB"}, {"backtrace": 46, "define": "BOOST_LOG_STATIC_LINK"}, {"backtrace": 47, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 46, "define": "BOOST_RANDOM_NO_LIB"}, {"backtrace": 46, "define": "BOOST_RANDOM_STATIC_LINK"}, {"backtrace": 46, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 46, "define": "BOOST_THREAD_STATIC_LINK"}, {"backtrace": 46, "define": "BOOST_THREAD_USE_LIB"}, {"backtrace": 48, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 49, "define": "SLIC3R_GUI"}, {"backtrace": 50, "define": "UNICODE"}, {"backtrace": 50, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 47, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 50, "define": "_UNICODE"}, {"backtrace": 51, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 50, "define": "wxUSE_UNICODE"}], "includes": [{"backtrace": 52, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 53, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 54, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 55, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}, {"backtrace": 46, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}], "language": "CXX", "languageStandard": {"backtraces": [46, 46, 46, 46, 46], "standard": "17"}, "sourceIndexes": [0]}], "id": "hintsToPot::@4b0869cd280e687ffba6", "link": {"commandFragments": [{"fragment": "-Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/opt/homebrew/lib", "role": "libraries"}, {"backtrace": 3, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_log.a", "role": "libraries"}, {"backtrace": 3, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_filesystem.a", "role": "libraries"}, {"backtrace": 16, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_coroutine.a", "role": "libraries"}, {"backtrace": 16, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_context.a", "role": "libraries"}, {"backtrace": 3, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_locale.a", "role": "libraries"}, {"backtrace": 3, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_thread.a", "role": "libraries"}, {"backtrace": 23, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libiconv.tbd", "role": "libraries"}, {"backtrace": 3, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_chrono.a", "role": "libraries"}, {"backtrace": 3, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_atomic.a", "role": "libraries"}, {"backtrace": 3, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_date_time.a", "role": "libraries"}, {"backtrace": 29, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_exception.a", "role": "libraries"}, {"backtrace": 29, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_container.a", "role": "libraries"}, {"backtrace": 3, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_iostreams.a", "role": "libraries"}, {"backtrace": 32, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_random.a", "role": "libraries"}, {"backtrace": 38, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libz.tbd", "role": "libraries"}, {"backtrace": 38, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libbz2.tbd", "role": "libraries"}, {"backtrace": 38, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/liblzma.tbd", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/homebrew/lib/libzstd.1.5.7.dylib", "role": "libraries"}], "language": "CXX"}, "name": "hintsToPot", "nameOnDisk": "hintsToPot", "paths": {"build": "src/hints", "source": "src/hints"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/hints/HintsToPot.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}