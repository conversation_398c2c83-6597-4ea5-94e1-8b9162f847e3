{"artifacts": [{"path": "src/BambuStudio"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "link_directories", "include", "target_link_libraries", "set_target_properties", "find_package", "find_dependency", "boostcfg_find_component", "set_property", "use_CGAL_GMP_support", "CGAL_setup_CGAL_dependencies", "CGAL_setup_target_dependencies", "add_compile_options", "add_compile_definitions", "add_definitions", "include_directories"], "files": ["src/CMakeLists.txt", "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/UsewxWidgets.cmake", "src/libslic3r/CMakeLists.txt", "src/miniz/CMakeLists.txt", "CMakeLists.txt", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_asio-1.84.0/boost_asio-targets.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_asio-1.84.0/boost_asio-config.cmake", "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_log-1.84.0/boost_log-config.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfig.cmake", "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindBoost.cmake", "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindIconv.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_locale-1.84.0/boost_locale-config.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_thread-1.84.0/boost_thread-targets.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_thread-1.84.0/boost_thread-config.cmake", "src/qhull/CMakeLists.txt", "src/glu-libtess/CMakeLists.txt", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_SetupGMP.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_SetupCGALDependencies.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_setup_target_dependencies.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGALConfig.cmake", "src/clipper2/CMakeLists.txt", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencv4/OpenCVModules.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencv4/OpenCVConfig.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_log-1.84.0/boost_log-targets.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0/boost_iostreams-targets.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0/boost_iostreams-config.cmake", "cmake/modules/FindOpenVDB.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Blosc/BloscTargets.cmake", "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Blosc/BloscConfig.cmake", "cmake/modules/FindNLopt.cmake", "src/slic3r/CMakeLists.txt", "src/minilzo/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 113, "parent": 0}, {"command": 1, "file": 0, "line": 297, "parent": 0}, {"command": 3, "file": 0, "line": 58, "parent": 0}, {"file": 1, "parent": 3}, {"command": 2, "file": 1, "line": 51, "parent": 4}, {"command": 4, "file": 0, "line": 128, "parent": 0}, {"command": 4, "file": 0, "line": 133, "parent": 0}, {"command": 4, "file": 0, "line": 145, "parent": 0}, {"command": 4, "file": 0, "line": 153, "parent": 0}, {"file": 2}, {"command": 4, "file": 2, "line": 574, "parent": 10}, {"file": 3}, {"command": 4, "file": 3, "line": 15, "parent": 12}, {"file": 4}, {"command": 4, "file": 4, "line": 413, "parent": 14}, {"command": 6, "file": 4, "line": 376, "parent": 14}, {"file": 10, "parent": 16}, {"command": 6, "file": 10, "line": 610, "parent": 17}, {"file": 9, "parent": 18}, {"command": 8, "file": 9, "line": 128, "parent": 19}, {"command": 6, "file": 9, "line": 67, "parent": 20}, {"file": 8, "parent": 21}, {"command": 7, "file": 8, "line": 93, "parent": 22}, {"command": 6, "file": 7, "line": 76, "parent": 23}, {"file": 6, "parent": 24}, {"command": 3, "file": 6, "line": 30, "parent": 25}, {"file": 5, "parent": 26}, {"command": 5, "file": 5, "line": 61, "parent": 27}, {"command": 8, "file": 9, "line": 128, "parent": 19}, {"command": 6, "file": 9, "line": 67, "parent": 29}, {"file": 12, "parent": 30}, {"command": 7, "file": 12, "line": 26, "parent": 31}, {"command": 6, "file": 7, "line": 76, "parent": 32}, {"file": 11, "parent": 33}, {"command": 9, "file": 11, "line": 185, "parent": 34}, {"command": 8, "file": 9, "line": 128, "parent": 19}, {"command": 6, "file": 9, "line": 67, "parent": 36}, {"file": 14, "parent": 37}, {"command": 3, "file": 14, "line": 83, "parent": 38}, {"file": 13, "parent": 39}, {"command": 5, "file": 13, "line": 61, "parent": 40}, {"file": 15}, {"command": 4, "file": 15, "line": 22, "parent": 42}, {"file": 16}, {"command": 4, "file": 16, "line": 33, "parent": 44}, {"command": 6, "file": 2, "line": 492, "parent": 10}, {"file": 20, "parent": 46}, {"command": 12, "file": 20, "line": 170, "parent": 47}, {"command": 11, "file": 19, "line": 3, "parent": 48}, {"command": 10, "file": 18, "line": 80, "parent": 49}, {"command": 4, "file": 17, "line": 70, "parent": 50}, {"command": 4, "file": 17, "line": 73, "parent": 50}, {"file": 21}, {"command": 4, "file": 21, "line": 41, "parent": 53}, {"command": 6, "file": 2, "line": 493, "parent": 10}, {"file": 23, "parent": 55}, {"command": 3, "file": 23, "line": 126, "parent": 56}, {"file": 22, "parent": 57}, {"command": 5, "file": 22, "line": 78, "parent": 58}, {"command": 4, "file": 2, "line": 600, "parent": 10}, {"command": 4, "file": 2, "line": 613, "parent": 10}, {"command": 3, "file": 8, "line": 117, "parent": 22}, {"file": 24, "parent": 62}, {"command": 5, "file": 24, "line": 61, "parent": 63}, {"command": 8, "file": 9, "line": 128, "parent": 19}, {"command": 6, "file": 9, "line": 67, "parent": 65}, {"file": 26, "parent": 66}, {"command": 3, "file": 26, "line": 64, "parent": 67}, {"file": 25, "parent": 68}, {"command": 5, "file": 25, "line": 61, "parent": 69}, {"command": 6, "file": 4, "line": 585, "parent": 14}, {"file": 27, "parent": 71}, {"command": 5, "file": 27, "line": 554, "parent": 72}, {"command": 6, "file": 27, "line": 433, "parent": 72}, {"file": 29, "parent": 74}, {"command": 3, "file": 29, "line": 3, "parent": 75}, {"file": 28, "parent": 76}, {"command": 5, "file": 28, "line": 82, "parent": 77}, {"command": 6, "file": 4, "line": 577, "parent": 14}, {"file": 30, "parent": 79}, {"command": 5, "file": 30, "line": 116, "parent": 80}, {"file": 31}, {"command": 4, "file": 31, "line": 655, "parent": 82}, {"file": 32}, {"command": 4, "file": 32, "line": 17, "parent": 84}, {"command": 4, "file": 4, "line": 448, "parent": 14}, {"command": 13, "file": 4, "line": 139, "parent": 14}, {"command": 13, "file": 4, "line": 173, "parent": 14}, {"command": 13, "file": 4, "line": 257, "parent": 14}, {"command": 13, "file": 4, "line": 259, "parent": 14}, {"command": 13, "file": 4, "line": 264, "parent": 14}, {"command": 13, "file": 4, "line": 271, "parent": 14}, {"command": 13, "file": 4, "line": 275, "parent": 14}, {"command": 14, "file": 4, "line": 130, "parent": 14}, {"command": 15, "file": 4, "line": 96, "parent": 14}, {"command": 15, "file": 4, "line": 92, "parent": 14}, {"command": 15, "file": 4, "line": 339, "parent": 14}, {"command": 9, "file": 1, "line": 55, "parent": 4}, {"command": 15, "file": 4, "line": 342, "parent": 14}, {"command": 16, "file": 4, "line": 322, "parent": 14}, {"command": 16, "file": 4, "line": 324, "parent": 14}, {"command": 16, "file": 0, "line": 30, "parent": 0}, {"command": 16, "file": 4, "line": 491, "parent": 14}, {"command": 16, "file": 4, "line": 326, "parent": 14}, {"command": 16, "file": 1, "line": 46, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -pthread -g -std=gnu++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=14.0 -fPIE -fcolor-diagnostics -F/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/System/Library/Frameworks "}, {"backtrace": 87, "fragment": "-f<PERSON>-char"}, {"backtrace": 88, "fragment": "-DDEBUG"}, {"backtrace": 89, "fragment": "-Werror=return-type"}, {"backtrace": 90, "fragment": "-Wno-unused-function"}, {"backtrace": 90, "fragment": "-Wno-unused-variable"}, {"backtrace": 90, "fragment": "-Wno-unused-but-set-variable"}, {"backtrace": 90, "fragment": "-Wno-unused-label"}, {"backtrace": 90, "fragment": "-Wno-unused-local-typedefs"}, {"backtrace": 91, "fragment": "-Wno-ignored-attributes"}, {"backtrace": 92, "fragment": "-Wno-deprecated-declarations"}, {"backtrace": 93, "fragment": "-Wno-error=enum-constexpr-conversion"}, {"backtrace": 6, "fragment": "-DOPENVDB_ABI_VERSION_NUMBER=8"}], "defines": [{"backtrace": 6, "define": "BOOST_ASIO_DISABLE_KQUEUE"}, {"backtrace": 6, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 6, "define": "BOOST_ATOMIC_STATIC_LINK"}, {"backtrace": 6, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 6, "define": "BOOST_CHRONO_STATIC_LINK"}, {"backtrace": 6, "define": "BOOST_CONTAINER_NO_LIB"}, {"backtrace": 6, "define": "BOOST_CONTAINER_STATIC_LINK"}, {"backtrace": 6, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 6, "define": "BOOST_DATE_TIME_STATIC_LINK"}, {"backtrace": 6, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 6, "define": "BOOST_FILESYSTEM_STATIC_LINK=1"}, {"backtrace": 6, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 6, "define": "BOOST_IOSTREAMS_STATIC_LINK"}, {"backtrace": 6, "define": "BOOST_LOCALE_NO_LIB"}, {"backtrace": 6, "define": "BOOST_LOCALE_STATIC_LINK"}, {"backtrace": 6, "define": "BOOST_LOG_NO_LIB"}, {"backtrace": 6, "define": "BOOST_LOG_STATIC_LINK"}, {"backtrace": 94, "define": "BOOST_NO_CXX98_FUNCTION_BASE"}, {"backtrace": 6, "define": "BOOST_RANDOM_NO_LIB"}, {"backtrace": 6, "define": "BOOST_RANDOM_STATIC_LINK"}, {"backtrace": 6, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 6, "define": "BOOST_THREAD_STATIC_LINK"}, {"backtrace": 6, "define": "BOOST_THREAD_USE_LIB"}, {"backtrace": 8, "define": "CURL_STATICLIB"}, {"backtrace": 8, "define": "GLEW_STATIC"}, {"backtrace": 6, "define": "LIBNEST2D_GEOMETRIES_libslic3r"}, {"backtrace": 6, "define": "LIBNEST2D_OPTIMIZER_nlopt"}, {"backtrace": 6, "define": "LIBNEST2D_STATIC"}, {"backtrace": 6, "define": "LIBNEST2D_THREADING_tbb"}, {"backtrace": 6, "define": "OPENVDB_OPENEXR_STATICLIB"}, {"backtrace": 6, "define": "OPENVDB_STATICLIB"}, {"backtrace": 95, "define": "SLIC3R_DESKTOP_INTEGRATION"}, {"backtrace": 96, "define": "SLIC3R_GUI"}, {"backtrace": 6, "define": "TBB_USE_CAPTURED_EXCEPTION=0"}, {"backtrace": 6, "define": "TBB_USE_DEBUG"}, {"backtrace": 97, "define": "UNICODE"}, {"backtrace": 6, "define": "USE_TBB"}, {"backtrace": 97, "define": "WXINTL_NO_GETTEXT_MACRO"}, {"backtrace": 98, "define": "WXUSINGDLL"}, {"backtrace": 94, "define": "_HAS_AUTO_PTR_ETC=0"}, {"backtrace": 97, "define": "_UNICODE"}, {"backtrace": 98, "define": "__WXMAC__"}, {"backtrace": 98, "define": "__WXOSX_COCOA__"}, {"backtrace": 98, "define": "__WXOSX__"}, {"backtrace": 8, "define": "wxDEBUG_LEVEL=0"}, {"backtrace": 99, "define": "wxNO_UNSAFE_WXSTRING_CONV"}, {"backtrace": 97, "define": "wxUSE_UNICODE"}], "frameworks": [{"backtrace": 8, "isSystem": true, "path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework"}], "includes": [{"backtrace": 100, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src"}, {"backtrace": 101, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/platform"}, {"backtrace": 102, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/hidapi/include"}, {"backtrace": 6, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src/libslic3r"}, {"backtrace": 6, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencascade"}, {"backtrace": 6, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libnest2d/include"}, {"backtrace": 6, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/miniz"}, {"backtrace": 6, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/glu-libtess/include"}, {"backtrace": 6, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/clipper2/Clipper2Lib/include"}, {"backtrace": 8, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/minilzo"}, {"backtrace": 103, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/eigen"}, {"backtrace": 104, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio/src/libigl"}, {"backtrace": 105, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/wx/include/osx_cocoa-unicode-static-3.1"}, {"backtrace": 105, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/wx-3.1"}, {"backtrace": 6, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include"}, {"backtrace": 6, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/opencv4"}, {"backtrace": 6, "isSystem": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include/OpenEXR"}, {"backtrace": 8, "isSystem": true, "path": "/opt/homebrew/include"}], "language": "CXX", "languageStandard": {"backtraces": [6, 6, 6, 6, 6, 6], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 8, "id": "imgui::@f0bb1e571c12c8d458e0"}, {"backtrace": 6, "id": "admesh::@db36e4f74c66f2bf2254"}, {"backtrace": 6, "id": "nowide::@34b20850f9a50713ec38"}, {"backtrace": 6, "id": "clipper::@81359c37bfb1d12160f2"}, {"backtrace": 6, "id": "Clipper2::@9bf50c2026546175e5f3"}, {"backtrace": 6, "id": "miniz_static::@8c4610aaeb52d7e4118f"}, {"backtrace": 8, "id": "minilzo_static::@a95d82a94b3f20e643da"}, {"backtrace": 6, "id": "glu-libtess::@86057b0eb26356d52b06"}, {"backtrace": 6, "id": "semver::@9d48bd82e9816d725e2b"}, {"backtrace": 6, "id": "mcut::@a392ea28d34c20f491b4"}, {"backtrace": 6, "id": "libnest2d::@8f4a5c5a495c7f0c1447"}, {"backtrace": 6, "id": "libslic3r_cgal::@87deb467dcbeda20dc6b"}, {"backtrace": 8, "id": "imguizmo::@67d194951d60576ffe05"}, {"backtrace": 8, "id": "hidapi::@1f7e0fad1acc4ee8b79a"}, {"backtrace": 8, "id": "libslic3r_gui::@1086e93791e4332ab3a6"}], "id": "BambuStudio::@145eef247bfb46b6828c", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "-Wall -Wno-reorder -Werror=partial-availability -Werror=unguarded-availability -Werror=unguarded-availability-new -pthread -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-F/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/System/Library/Frameworks", "role": "frameworkPath"}, {"backtrace": 5, "fragment": "-L/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib -Wl,-rpath,/opt/homebrew/lib", "role": "libraries"}, {"backtrace": 6, "fragment": "src/libslic3r/liblibslic3r.a", "role": "libraries"}, {"backtrace": 7, "fragment": "-liconv -framework IOKit", "role": "libraries"}, {"backtrace": 7, "fragment": "-framework CoreFoundation", "role": "libraries"}, {"backtrace": 7, "fragment": "-framework AVFoundation", "role": "libraries"}, {"backtrace": 7, "fragment": "-framework AVKit", "role": "libraries"}, {"backtrace": 7, "fragment": "-framework CoreMedia", "role": "libraries"}, {"backtrace": 7, "fragment": "-framework VideoToolbox", "role": "libraries"}, {"backtrace": 7, "fragment": "-lc++", "role": "libraries"}, {"backtrace": 8, "fragment": "src/slic3r/liblibslic3r_gui.a", "role": "libraries"}, {"backtrace": 9, "fragment": "-framework OpenGL", "role": "libraries"}, {"backtrace": 6, "fragment": "src/libslic3r/liblibslic3r.a", "role": "libraries"}, {"backtrace": 11, "fragment": "src/libnest2d/liblibnest2d.a", "role": "libraries"}, {"backtrace": 6, "fragment": "src/libslic3r/liblibslic3r.a", "role": "libraries"}, {"backtrace": 11, "fragment": "src/libnest2d/liblibnest2d.a", "role": "libraries"}, {"backtrace": 11, "fragment": "src/admesh/libadmesh.a", "role": "libraries"}, {"backtrace": 13, "fragment": "src/miniz/libminiz_static.a", "role": "libraries"}, {"backtrace": 15, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_log.a", "role": "libraries"}, {"backtrace": 15, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_filesystem.a", "role": "libraries"}, {"backtrace": 28, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_coroutine.a", "role": "libraries"}, {"backtrace": 28, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_context.a", "role": "libraries"}, {"backtrace": 15, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_locale.a", "role": "libraries"}, {"backtrace": 15, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_thread.a", "role": "libraries"}, {"backtrace": 35, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libiconv.tbd", "role": "libraries"}, {"backtrace": 15, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_chrono.a", "role": "libraries"}, {"backtrace": 15, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_atomic.a", "role": "libraries"}, {"backtrace": 15, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_date_time.a", "role": "libraries"}, {"backtrace": 41, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_exception.a", "role": "libraries"}, {"backtrace": 41, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_container.a", "role": "libraries"}, {"backtrace": 11, "fragment": "src/clipper/libclipper.a", "role": "libraries"}, {"backtrace": 11, "fragment": "src/boost/libnowide.a", "role": "libraries"}, {"backtrace": 11, "fragment": "src/glu-libtess/libglu-libtess.a", "role": "libraries"}, {"backtrace": 43, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libqhullcpp.a", "role": "libraries"}, {"backtrace": 43, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libqhullstatic_r.a", "role": "libraries"}, {"backtrace": 45, "fragment": "-lm", "role": "libraries"}, {"backtrace": 11, "fragment": "src/semver/libsemver.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libtbbmalloc.a", "role": "libraries"}, {"backtrace": 11, "fragment": "src/libslic3r/liblibslic3r_cgal.a", "role": "libraries"}, {"backtrace": 51, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libgmpxx.a", "role": "libraries"}, {"backtrace": 52, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libmpfr.a", "role": "libraries"}, {"backtrace": 52, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libgmp.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libpng.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKXDESTEP.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKSTEP.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKSTEP209.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKSTEPAttr.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKSTEPBase.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKXCAF.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKXSBase.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKVCAF.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKCAF.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKLCAF.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKCDF.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKV3d.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKService.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKMesh.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKBO.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKPrim.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKHLR.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKShHealing.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKTopAlgo.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKGeomAlgo.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKBRep.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKGeomBase.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKG3d.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKG2d.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKMath.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libTKernel.a", "role": "libraries"}, {"backtrace": 11, "fragment": "src/clipper2/libClipper2.a", "role": "libraries"}, {"backtrace": 54, "fragment": "-lm", "role": "libraries"}, {"backtrace": 11, "fragment": "src/mcut/libmcutd.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libopencv_world.a", "role": "libraries"}, {"backtrace": 59, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/opencv4/3rdparty/libtegra_hal.a", "role": "libraries"}, {"backtrace": 59, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/opencv4/3rdparty/liblibpng.a", "role": "libraries"}, {"backtrace": 59, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/opencv4/3rdparty/liblibtiff.a", "role": "libraries"}, {"backtrace": 59, "fragment": "-framework AppKit", "role": "libraries"}, {"backtrace": 60, "fragment": "-lfreetype", "role": "libraries"}, {"fragment": "-framework Foundation", "role": "libraries"}, {"fragment": "-framework ModelIO", "role": "libraries"}, {"backtrace": 61, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libopenvdb.a", "role": "libraries"}, {"backtrace": 15, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_iostreams.a", "role": "libraries"}, {"backtrace": 64, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libboost_random.a", "role": "libraries"}, {"backtrace": 70, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libbz2.tbd", "role": "libraries"}, {"backtrace": 70, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/liblzma.tbd", "role": "libraries"}, {"backtrace": 70, "fragment": "/opt/homebrew/lib/libzstd.1.5.7.dylib", "role": "libraries"}, {"backtrace": 73, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libHalf-2_5.a", "role": "libraries"}, {"backtrace": 78, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libblosc.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libtbb.a", "role": "libraries"}, {"backtrace": 81, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libnlopt.a", "role": "libraries"}, {"backtrace": 83, "fragment": "src/imguizmo/libimguizmo.a", "role": "libraries"}, {"backtrace": 83, "fragment": "src/imgui/libimgui.a", "role": "libraries"}, {"backtrace": 85, "fragment": "src/minilzo/libminilzo_static.a", "role": "libraries"}, {"backtrace": 83, "fragment": "/opt/homebrew/lib/libGLEW.a", "role": "libraries"}, {"fragment": "-framework OpenGL", "role": "libraries"}, {"fragment": "-framework OpenGL", "role": "libraries"}, {"backtrace": 83, "fragment": "src/hidapi/libhidapi.a", "role": "libraries"}, {"backtrace": 83, "fragment": "-L/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib", "role": "libraries"}, {"backtrace": 83, "fragment": "-pthread", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_gl-3.1.a", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_webview-3.1.a", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_aui-3.1.a", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_baseu_net-3.1.a", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_media-3.1.a", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_html-3.1.a", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_core-3.1.a", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_baseu-3.1.a", "role": "libraries"}, {"backtrace": 83, "fragment": "-weak_framework AVKit", "role": "libraries"}, {"backtrace": 83, "fragment": "-framework WebKit", "role": "libraries"}, {"backtrace": 83, "fragment": "-lwx_osx_cocoau_core-3.1", "role": "libraries"}, {"backtrace": 83, "fragment": "-lwx_baseu-3.1", "role": "libraries"}, {"backtrace": 83, "fragment": "-lz", "role": "libraries"}, {"backtrace": 83, "fragment": "-framework AudioToolbox", "role": "libraries"}, {"backtrace": 83, "fragment": "-framework WebKit", "role": "libraries"}, {"backtrace": 83, "fragment": "-lwx_osx_cocoau_core-3.1", "role": "libraries"}, {"backtrace": 83, "fragment": "-lwx_baseu-3.1", "role": "libraries"}, {"backtrace": 83, "fragment": "-lz", "role": "libraries"}, {"backtrace": 83, "fragment": "-framework AudioToolbox", "role": "libraries"}, {"backtrace": 7, "fragment": "-framework AVFoundation", "role": "libraries"}, {"backtrace": 7, "fragment": "-framework CoreMedia", "role": "libraries"}, {"backtrace": 83, "fragment": "-lwxregexu-3.1", "role": "libraries"}, {"backtrace": 83, "fragment": "-liconv", "role": "libraries"}, {"backtrace": 83, "fragment": "-framework Security", "role": "libraries"}, {"backtrace": 83, "fragment": "-framework Carbon", "role": "libraries"}, {"backtrace": 83, "fragment": "-framework QuartzCore", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libpng.a", "role": "libraries"}, {"backtrace": 83, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libz.tbd", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libtiff.a", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libjpeg.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libexpat.a", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libglfw3.a", "role": "libraries"}, {"backtrace": 7, "fragment": "-framework CoreFoundation", "role": "libraries"}, {"backtrace": 83, "fragment": "-framework Cocoa", "role": "libraries"}, {"backtrace": 83, "fragment": "-framework IOKit", "role": "libraries"}, {"backtrace": 86, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libcurl.a", "role": "libraries"}, {"backtrace": 11, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libz.tbd", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libssl.a", "role": "libraries"}, {"backtrace": 83, "fragment": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libcrypto.a", "role": "libraries"}, {"fragment": "-framework DiskArbitration", "role": "libraries"}], "language": "CXX"}, "name": "BambuStudio", "nameOnDisk": "BambuStudio", "paths": {"build": "src", "source": "src"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/BambuStudio.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/BambuStudio.hpp", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}