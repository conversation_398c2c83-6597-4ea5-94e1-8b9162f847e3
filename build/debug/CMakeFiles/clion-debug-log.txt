/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cmake -DCMAKE_BUILD_TYPE=Debug -<PERSON><PERSON><PERSON>_MAKE_PROGRAM=/Applications/CLion.app/Contents/bin/ninja/mac/aarch64/ninja -G Ninja -DCMAKE_PREFIX_PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local -S /Users/<USER>/Documents/augment-projects/BambuStudio -B /Users/<USER>/Documents/augment-projects/BambuStudio/build/debug
-- CMAKE_OSX_DEPLOYMENT_TARGET: 14.0
-- SLIC3R_ASAN: OFF
-- SLIC3R_BUILD_SANDBOXES: OFF
-- SLIC3R_BUILD_TESTS: OFF
-- SLIC3R_DESKTOP_INTEGRATION: ON
-- SLIC3R_ENC_CHECK: OFF
-- SLIC3R_FHS: OFF
-- SLIC3R_GTK: 2
-- SLIC3R_GUI: ON
-- SLIC3R_MSVC_COMPILE_PARALLEL: ON
-- SLIC3R_MSVC_PDB: ON
-- SLIC3R_PCH: ON
-- SLIC3R_PERL_XS: OFF
-- SLIC3R_PROFILE: OFF
-- SLIC3R_STATIC: ON
-- SLIC3R_WX_STABLE: OFF
-- SLIC3R_STATIC: ON
-- CMAKE_PREFIX_PATH: /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local (from cache or command line)
-- PROJECT_SOURCE_DIR: /Users/<USER>/Documents/augment-projects/BambuStudio
-- CMAKE_MODULE_PATH: /Users/<USER>/Documents/augment-projects/BambuStudio/cmake/modules/
OS X SDK Path: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk
OS X Deployment Target: 14.0
-- LIBDIR: /Users/<USER>/Documents/augment-projects/BambuStudio/src
-- LIBDIR_BIN: /Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src
CMake Warning (dev) at CMakeLists.txt:376 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfig.cmake (found suitable version "1.84.0", minimum required is "1.83.0") found components: system filesystem thread log locale regex chrono atomic date_time iostreams
-- Boost::boost exists
-- TBB found in /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/TBB
-- Found OpenSSL: /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libcrypto.a (found version "3.1.2")
-- Found OpenSSL: /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libcrypto.a (found suitable version "3.1.2", minimum required is "3")
-- Found ZLIB: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libz.tbd (found suitable version "1.2.12", minimum required is "1")
-- Found ZLIB: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libz.tbd (found version "1.2.12")
CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:546 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

CMake Warning (dev) at CMakeLists.txt:561 (add_custom_command):
  The following keywords are not supported when using
  add_custom_command(TARGET): DEPENDS.

  Policy CMP0175 is not set: add_custom_command() rejects invalid arguments.
  Run "cmake --help-policy CMP0175" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found NLopt in '/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib'.
-- Using NLopt include directory '/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include'.
-- Using NLopt library '/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libnlopt.a'.
CMake Deprecation Warning at cmake/modules/FindOpenVDB.cmake:126 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.
Call Stack (most recent call first):
  CMakeLists.txt:585 (find_package)


-- OpenVDB ABI Version: 8
-- TBB found in /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/TBB
CMake Warning (dev) at cmake/modules/FindOpenVDB.cmake:372 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  CMakeLists.txt:585 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfig.cmake (found version "1.84.0") found components: iostreams system
-- OpenVDB libraries: /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libopenvdb.a
CMake Deprecation Warning at src/admesh/CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


CMake Deprecation Warning at src/boost/CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


CMake Deprecation Warning at src/clipper/CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


CMake Deprecation Warning at src/miniz/CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


CMake Deprecation Warning at src/minilzo/CMakeLists.txt:2 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


-- Minilzo using bundled version...
CMake Deprecation Warning at src/glu-libtess/CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


-- Using qhull from system.
CMake Deprecation Warning at src/Shiny/CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


CMake Deprecation Warning at src/semver/CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


CMake Deprecation Warning at src/libigl/CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


-- IGL NOT found, using bundled version...
-- [MCUT] version: 1.2.0
-- [MCUT] MCUT_INCLUDE_DIR=/Users/<USER>/Documents/augment-projects/BambuStudio/src/mcut/include
-- [MCUT] compilation_flags=
-- [MCUT] preprocessor_defs=-DMCUT_WITH_COMPUTE_HELPER_THREADPOOL=1
-- [MCUT] extra_libs=Threads::Threads
-- [MCUT] create target: name=mcut type=STATIC
-- [MCUT] MCUT_LIB_PATH=$<TARGET_FILE:mcut>
CMake Warning (dev) at src/mcut/CMakeLists.txt:327 (install):
  Policy CMP0177 is not set: install() DESTINATION paths are normalized.  Run
  "cmake --help-policy CMP0177" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.
This warning is for project developers.  Use -Wno-dev to suppress it.

-- Boost_INCLUDE_DIRS = /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include
CMake Warning at /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGALConfig.cmake:92 (message):
  CGAL_DATA_DIR cannot be deduced, set the variable CGAL_DATA_DIR to set the
  default value of CGAL::data_file_path()
Call Stack (most recent call first):
  src/libslic3r/CMakeLists.txt:492 (find_package)


-- Using header-only CGAL
-- Targetting Ninja
-- Using /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ compiler.
CMake Warning (dev) at /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_GeneratorSpecificSettings.cmake:42 (exec_program):
  Policy CMP0153 is not set: The exec_program command should not be called.
  Run "cmake --help-policy CMP0153" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.

  Use execute_process() instead.
Call Stack (most recent call first):
  /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/FindGMP.cmake:11 (include)
  /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_SetupGMP.cmake:24 (find_package)
  /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_SetupCGALDependencies.cmake:36 (include)
  /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGALConfig.cmake:153 (include)
  src/libslic3r/CMakeLists.txt:492 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- DARWIN_VERSION=24
-- Mac Leopard detected
CMake Warning (dev) at /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_SetupBoost.cmake:20 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_SetupCGALDependencies.cmake:47 (include)
  /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGALConfig.cmake:153 (include)
  src/libslic3r/CMakeLists.txt:492 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfig.cmake (found suitable version "1.84.0", minimum required is "1.48")
-- Boost include dirs: /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/include
-- Boost libraries:    
-- Adding precompiled header pchheader.hpp to target libslic3r.
-- PCH skipping sources: MacUtils.mm;Format/ModelIO.mm
CMake Deprecation Warning at src/imgui/CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


CMake Deprecation Warning at src/imguizmo/CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.10 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value.  Or, use the <min>...<max> syntax
  to tell CMake that the project requires at least <min> but has been updated
  to work with policies introduced by <max> or earlier.


-- wx-config path: /Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/bin/wx-config
-- wx libs: -L/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib;-pthread;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_gl-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_webview-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_aui-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_baseu_net-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_media-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_html-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_osx_cocoau_core-3.1.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libwx_baseu-3.1.a;-framework WebKit;-framework AVFoundation;-framework CoreMedia;-weak_framework AVKit;-lwx_osx_cocoau_core-3.1;-lwx_baseu-3.1;libz.tbd;-framework AudioToolbox;-framework WebKit;libz.tbd;-lwxregexu-3.1;libiconv.tbd;-framework CoreFoundation;-framework Security;-framework Carbon;-framework Cocoa;-framework IOKit;-framework QuartzCore;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libpng.a;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libz.tbd;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libtiff.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libjpeg.a;/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/libexpat.a
-- Adding precompiled header pchheader.hpp to target libslic3r_gui.
-- PCH skipping sources: Utils/RetinaHelperImpl.mm;Utils/MacDarkMode.mm;GUI/RemovableDriveManagerMM.mm;GUI/Mouse3DHandlerMac.mm;GUI/InstanceCheckMac.mm;GUI/wxMediaCtrl2.mm
-- libslic3r-CMAKE_BUILD_TYPE: Debug
-- CMAKE_CURRENT_BINARY_DIR: /Users/<USER>/Documents/augment-projects/BambuStudio/build/debug/src
-- Configuring done (0.7s)
-- Generating done (0.1s)
-- Build files have been written to: /Users/<USER>/Documents/augment-projects/BambuStudio/build/debug
