{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cmake", "cpack": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cpack", "ctest": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/ctest", "root": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 6, "string": "3.31.6", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-f3e46596e2c12206346b.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-9e53d2f2d0adfe417135.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-7b4a9ab68f8620ccb08a.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-ec1725459884363f77ed.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-9e53d2f2d0adfe417135.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-7b4a9ab68f8620ccb08a.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-f3e46596e2c12206346b.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, "toolchains-v1": {"jsonFile": "toolchains-v1-ec1725459884363f77ed.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}