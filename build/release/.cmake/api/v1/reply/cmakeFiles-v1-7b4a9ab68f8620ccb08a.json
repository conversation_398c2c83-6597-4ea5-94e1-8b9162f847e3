{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/release/CMakeFiles/3.31.6/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Darwin-Initialize.cmake"}, {"isGenerated": true, "path": "build/release/CMakeFiles/3.31.6/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/release/CMakeFiles/3.31.6/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Darwin.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Apple-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Linker/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Linker/AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Apple-AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Apple-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Linker/AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Linker/AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang.cmake"}, {"path": "version.inc"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/GNUInstallDirs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindBoost.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfig.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_headers-1.84.0/boost_headers-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_headers-1.84.0/boost_headers-config.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_headers-1.84.0/boost_headers-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_system-1.84.0/boost_system-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_system-1.84.0/boost_system-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_assert-1.84.0/boost_assert-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_assert-1.84.0/boost_assert-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_config-1.84.0/boost_config-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_config-1.84.0/boost_config-config.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_config-1.84.0/boost_config-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_assert-1.84.0/boost_assert-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_throw_exception-1.84.0/boost_throw_exception-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_throw_exception-1.84.0/boost_throw_exception-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_throw_exception-1.84.0/boost_throw_exception-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_variant2-1.84.0/boost_variant2-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_variant2-1.84.0/boost_variant2-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_mp11-1.84.0/boost_mp11-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_mp11-1.84.0/boost_mp11-config.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_mp11-1.84.0/boost_mp11-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_variant2-1.84.0/boost_variant2-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_winapi-1.84.0/boost_winapi-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_winapi-1.84.0/boost_winapi-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_predef-1.84.0/boost_predef-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_predef-1.84.0/boost_predef-config.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_predef-1.84.0/boost_predef-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_winapi-1.84.0/boost_winapi-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_system-1.84.0/boost_system-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_filesystem-1.84.0/boost_filesystem-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_filesystem-1.84.0/boost_filesystem-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_container_hash-1.84.0/boost_container_hash-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_container_hash-1.84.0/boost_container_hash-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_describe-1.84.0/boost_describe-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_describe-1.84.0/boost_describe-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_describe-1.84.0/boost_describe-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_container_hash-1.84.0/boost_container_hash-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_core-1.84.0/boost_core-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_core-1.84.0/boost_core-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_static_assert-1.84.0/boost_static_assert-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_static_assert-1.84.0/boost_static_assert-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_static_assert-1.84.0/boost_static_assert-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_core-1.84.0/boost_core-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_detail-1.84.0/boost_detail-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_detail-1.84.0/boost_detail-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_preprocessor-1.84.0/boost_preprocessor-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_preprocessor-1.84.0/boost_preprocessor-config.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_preprocessor-1.84.0/boost_preprocessor-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_type_traits-1.84.0/boost_type_traits-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_type_traits-1.84.0/boost_type_traits-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_type_traits-1.84.0/boost_type_traits-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_detail-1.84.0/boost_detail-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_io-1.84.0/boost_io-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_io-1.84.0/boost_io-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_io-1.84.0/boost_io-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iterator-1.84.0/boost_iterator-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iterator-1.84.0/boost_iterator-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_concept_check-1.84.0/boost_concept_check-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_concept_check-1.84.0/boost_concept_check-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_concept_check-1.84.0/boost_concept_check-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_function_types-1.84.0/boost_function_types-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_function_types-1.84.0/boost_function_types-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_mpl-1.84.0/boost_mpl-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_mpl-1.84.0/boost_mpl-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_utility-1.84.0/boost_utility-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_utility-1.84.0/boost_utility-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_utility-1.84.0/boost_utility-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_mpl-1.84.0/boost_mpl-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_function_types-1.84.0/boost_function_types-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_fusion-1.84.0/boost_fusion-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_fusion-1.84.0/boost_fusion-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_tuple-1.84.0/boost_tuple-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_tuple-1.84.0/boost_tuple-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_tuple-1.84.0/boost_tuple-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_typeof-1.84.0/boost_typeof-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_typeof-1.84.0/boost_typeof-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_typeof-1.84.0/boost_typeof-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_functional-1.84.0/boost_functional-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_functional-1.84.0/boost_functional-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_function-1.84.0/boost_function-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_function-1.84.0/boost_function-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_bind-1.84.0/boost_bind-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_bind-1.84.0/boost_bind-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_bind-1.84.0/boost_bind-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_function-1.84.0/boost_function-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_functional-1.84.0/boost_functional-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_fusion-1.84.0/boost_fusion-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_optional-1.84.0/boost_optional-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_optional-1.84.0/boost_optional-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_move-1.84.0/boost_move-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_move-1.84.0/boost_move-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_move-1.84.0/boost_move-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_optional-1.84.0/boost_optional-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_smart_ptr-1.84.0/boost_smart_ptr-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_smart_ptr-1.84.0/boost_smart_ptr-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_smart_ptr-1.84.0/boost_smart_ptr-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iterator-1.84.0/boost_iterator-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_atomic-1.84.0/boost_atomic-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_atomic-1.84.0/boost_atomic-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_align-1.84.0/boost_align-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_align-1.84.0/boost_align-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_align-1.84.0/boost_align-targets.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_atomic-1.84.0/boost_atomic-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_atomic-1.84.0/boost_atomic-targets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_filesystem-1.84.0/boost_filesystem-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_filesystem-1.84.0/boost_filesystem-targets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_thread-1.84.0/boost_thread-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_thread-1.84.0/boost_thread-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_chrono-1.84.0/boost_chrono-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_chrono-1.84.0/boost_chrono-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_integer-1.84.0/boost_integer-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_integer-1.84.0/boost_integer-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_integer-1.84.0/boost_integer-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_ratio-1.84.0/boost_ratio-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_ratio-1.84.0/boost_ratio-config.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_ratio-1.84.0/boost_ratio-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_chrono-1.84.0/boost_chrono-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_chrono-1.84.0/boost_chrono-targets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_container-1.84.0/boost_container-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_container-1.84.0/boost_container-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_intrusive-1.84.0/boost_intrusive-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_intrusive-1.84.0/boost_intrusive-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_intrusive-1.84.0/boost_intrusive-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_container-1.84.0/boost_container-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_container-1.84.0/boost_container-targets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_date_time-1.84.0/boost_date_time-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_date_time-1.84.0/boost_date_time-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_algorithm-1.84.0/boost_algorithm-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_algorithm-1.84.0/boost_algorithm-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_array-1.84.0/boost_array-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_array-1.84.0/boost_array-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_array-1.84.0/boost_array-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_exception-1.84.0/boost_exception-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_exception-1.84.0/boost_exception-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_exception-1.84.0/boost_exception-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_exception-1.84.0/boost_exception-targets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_range-1.84.0/boost_range-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_range-1.84.0/boost_range-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_conversion-1.84.0/boost_conversion-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_conversion-1.84.0/boost_conversion-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_conversion-1.84.0/boost_conversion-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_regex-1.84.0/boost_regex-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_regex-1.84.0/boost_regex-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_regex-1.84.0/boost_regex-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_range-1.84.0/boost_range-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_unordered-1.84.0/boost_unordered-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_unordered-1.84.0/boost_unordered-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_unordered-1.84.0/boost_unordered-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_algorithm-1.84.0/boost_algorithm-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_lexical_cast-1.84.0/boost_lexical_cast-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_lexical_cast-1.84.0/boost_lexical_cast-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_numeric_conversion-1.84.0/boost_numeric_conversion-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_numeric_conversion-1.84.0/boost_numeric_conversion-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_numeric_conversion-1.84.0/boost_numeric_conversion-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_lexical_cast-1.84.0/boost_lexical_cast-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_tokenizer-1.84.0/boost_tokenizer-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_tokenizer-1.84.0/boost_tokenizer-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_tokenizer-1.84.0/boost_tokenizer-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_date_time-1.84.0/boost_date_time-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_date_time-1.84.0/boost_date_time-targets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_thread-1.84.0/boost_thread-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_thread-1.84.0/boost_thread-targets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_log-1.84.0/boost_log-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_log-1.84.0/boost_log-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_parameter-1.84.0/boost_parameter-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_parameter-1.84.0/boost_parameter-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_parameter-1.84.0/boost_parameter-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_phoenix-1.84.0/boost_phoenix-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_phoenix-1.84.0/boost_phoenix-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_proto-1.84.0/boost_proto-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_proto-1.84.0/boost_proto-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_proto-1.84.0/boost_proto-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_phoenix-1.84.0/boost_phoenix-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_type_index-1.84.0/boost_type_index-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_type_index-1.84.0/boost_type_index-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_type_index-1.84.0/boost_type_index-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_asio-1.84.0/boost_asio-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_asio-1.84.0/boost_asio-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_context-1.84.0/boost_context-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_context-1.84.0/boost_context-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_pool-1.84.0/boost_pool-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_pool-1.84.0/boost_pool-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_pool-1.84.0/boost_pool-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_context-1.84.0/boost_context-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_context-1.84.0/boost_context-targets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_coroutine-1.84.0/boost_coroutine-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_coroutine-1.84.0/boost_coroutine-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_coroutine-1.84.0/boost_coroutine-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_coroutine-1.84.0/boost_coroutine-targets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_asio-1.84.0/boost_asio-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_interprocess-1.84.0/boost_interprocess-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_interprocess-1.84.0/boost_interprocess-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_interprocess-1.84.0/boost_interprocess-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_random-1.84.0/boost_random-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_random-1.84.0/boost_random-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_dynamic_bitset-1.84.0/boost_dynamic_bitset-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_dynamic_bitset-1.84.0/boost_dynamic_bitset-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_dynamic_bitset-1.84.0/boost_dynamic_bitset-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_random-1.84.0/boost_random-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_random-1.84.0/boost_random-targets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_spirit-1.84.0/boost_spirit-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_spirit-1.84.0/boost_spirit-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_endian-1.84.0/boost_endian-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_endian-1.84.0/boost_endian-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_endian-1.84.0/boost_endian-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_variant-1.84.0/boost_variant-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_variant-1.84.0/boost_variant-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_variant-1.84.0/boost_variant-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_spirit-1.84.0/boost_spirit-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_log-1.84.0/boost_log-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_log-1.84.0/boost_log-targets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_locale-1.84.0/boost_locale-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_locale-1.84.0/boost_locale-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindIconv.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakePushCheckState.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_locale-1.84.0/boost_locale-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_locale-1.84.0/boost_locale-targets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_regex-1.84.0/boost_regex-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_regex-1.84.0/boost_regex-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_regex-1.84.0/boost_regex-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_chrono-1.84.0/boost_chrono-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_chrono-1.84.0/boost_chrono-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_chrono-1.84.0/boost_chrono-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_atomic-1.84.0/boost_atomic-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_atomic-1.84.0/boost_atomic-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_atomic-1.84.0/boost_atomic-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_date_time-1.84.0/boost_date_time-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_date_time-1.84.0/boost_date_time-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_date_time-1.84.0/boost_date_time-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0/boost_iostreams-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0/boost_iostreams-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindBZip2.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckSymbolExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakePushCheckState.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindLibLZMA.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/zstd/zstdConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/zstd/zstdConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/zstd/zstdTargets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/zstd/zstdTargets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0/boost_iostreams-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0/boost_iostreams-targets-release.cmake"}, {"path": "cmake/modules/FindTBB.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/TBB/TBBConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/TBB/TBBConfig.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/TBB/TBBTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/TBB/TBBTargets-release.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindOpenSSL.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CURL/CURLConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CURL/CURLConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindOpenSSL.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CURL/CURLTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CURL/CURLTargets-release.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindEXPAT.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPNG.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindOpenGL.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"path": "cmake/modules/FindGLEW.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glew/glew-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glew/glew-targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glew/glew-targets-release.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glew/CopyImportedTargetProperties.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/glfw3/glfw3ConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/glfw3/glfw3Config.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/glfw3/glfw3Targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/glfw3/glfw3Targets-release.cmake"}, {"path": "cmake/modules/Findcereal.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/share/cmake/cereal/cereal-config.cmake"}, {"path": "cmake/modules/FindNLopt.cmake"}, {"path": "cmake/modules/FindOpenVDB.cmake"}, {"path": "cmake/modules/OpenVDBUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/IlmBase/IlmBaseConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/IlmBase/IlmBaseConfig.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/IlmBase/IlmBaseTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/IlmBase/IlmBaseTargets-release.cmake"}, {"path": "cmake/modules/FindTBB.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/TBB/TBBConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/TBB/TBBConfig.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/TBB/TBBTargets.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindBoost.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfig.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_headers-1.84.0/boost_headers-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_headers-1.84.0/boost_headers-config.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_headers-1.84.0/boost_headers-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0/boost_iostreams-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0/boost_iostreams-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindBZip2.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckSymbolExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakePushCheckState.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindLibLZMA.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/zstd/zstdConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/zstd/zstdConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/zstd/zstdTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_iostreams-1.84.0/boost_iostreams-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_system-1.84.0/boost_system-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_system-1.84.0/boost_system-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_system-1.84.0/boost_system-targets.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/GetPrerequisites.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Blosc/BloscConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Blosc/BloscConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Blosc/BloscTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Blosc/BloscTargets-release.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"path": "cmake/modules/CheckAtomic.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckLibraryExists.cmake"}, {"path": "src/platform/unix/fhs.hpp.in"}, {"path": "src/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindwxWidgets.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/UsewxWidgets.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindJPEG.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindTIFF.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"path": "src/platform/msw/BambuStudio.rc.in"}, {"path": "src/platform/msw/BambuStudio.manifest.in"}, {"path": "src/platform/osx/Info.plist.in"}, {"path": "src/build-utils/CMakeLists.txt"}, {"path": "src/admesh/CMakeLists.txt"}, {"path": "src/boost/CMakeLists.txt"}, {"path": "src/clipper/CMakeLists.txt"}, {"path": "src/clipper2/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/GNUInstallDirs.cmake"}, {"path": "src/miniz/CMakeLists.txt"}, {"path": "src/minilzo/CMakeLists.txt"}, {"path": "src/glu-libtess/CMakeLists.txt"}, {"path": "src/qhull/CMakeLists.txt"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Qhull/QhullConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Qhull/QhullConfig.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Qhull/QhullTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Qhull/QhullTargets-release.cmake"}, {"path": "src/Shiny/CMakeLists.txt"}, {"path": "src/semver/CMakeLists.txt"}, {"path": "src/libigl/CMakeLists.txt"}, {"path": "src/hints/CMakeLists.txt"}, {"path": "src/mcut/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakePrintHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CPack.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CPackComponent.cmake"}, {"isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Templates/CPackConfig.cmake.in"}, {"isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Templates/CPackConfig.cmake.in"}, {"path": "src/libnest2d/CMakeLists.txt"}, {"path": "src/libslic3r/CMakeLists.txt"}, {"path": "cmake/modules/PrecompiledHeader.cmake"}, {"path": "src/libslic3r/libslic3r_version.h.in"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGALConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGALConfigBuildVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGALConfig.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_CreateSingleSourceCGALProgram.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_add_test.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_Macros.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_VersionUtils.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_Common.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_Macros.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_TweakFindBoost.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_SCM.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_SetupCGALDependencies.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_SetupGMP.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/FindGMP.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_GeneratorSpecificSettings.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/FindMPFR.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_GeneratorSpecificSettings.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/FindGMPXX.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/FindGMP.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_GeneratorSpecificSettings.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_SetupBoost.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_TweakFindBoost.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindBoost.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/Boost-1.84.0/BoostConfig.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_headers-1.84.0/boost_headers-config-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_headers-1.84.0/boost_headers-config.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/boost_headers-1.84.0/boost_headers-targets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_setup_target_dependencies.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGALConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGALConfigBuildVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_target_use_TBB.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/CGAL/CGAL_enable_end_of_configuration_hook.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencv4/OpenCVConfig-version.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencv4/OpenCVConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencv4/OpenCVModules.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencv4/OpenCVModules-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEConfigVersion.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEConfig.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADECompileDefinitionsAndFlags-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEFoundationClassesTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEFoundationClassesTargets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEModelingDataTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEModelingDataTargets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEModelingAlgorithmsTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEModelingAlgorithmsTargets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEVisualizationTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEVisualizationTargets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEApplicationFrameworkTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEApplicationFrameworkTargets-release.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEDataExchangeTargets.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local/lib/cmake/opencascade/OpenCASCADEDataExchangeTargets-release.cmake"}, {"path": "src/imgui/CMakeLists.txt"}, {"path": "src/imguizmo/CMakeLists.txt"}, {"path": "src/hidapi/CMakeLists.txt"}, {"path": "src/slic3r/CMakeLists.txt"}, {"path": "cmake/modules/PrecompiledHeader.cmake"}, {"path": "src/slic3r/GUI/DeviceCore/CMakeLists.txt"}, {"path": "src/slic3r/GUI/DeviceTab/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "/Users/<USER>/Documents/augment-projects/BambuStudio/build/release", "source": "/Users/<USER>/Documents/augment-projects/BambuStudio"}, "version": {"major": 1, "minor": 1}}