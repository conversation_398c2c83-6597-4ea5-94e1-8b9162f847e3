<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="BackendCodeEditorMiscSettings">
    <option name="/Default/Housekeeping/FeatureSuggestion/FeatureSuggestionManager/DisabledSuggesters/=SwitchToGoToActionSuggester/@EntryIndexedValue" value="true" type="bool" />
    <option name="/Default/Housekeeping/GlobalSettingsUpgraded/IsUpgraded/@EntryValue" value="true" type="bool" />
    <option name="/Default/RiderDebugger/RiderRestoreDecompile/RestoreDecompileSetting/@EntryValue" value="false" type="bool" />
  </component>
  <component name="CMakePresetLoader">{
  &quot;useNewFormat&quot;: true
}</component>
  <component name="CMakeProjectFlavorService">
    <option name="flavorId" value="CMakePlainProjectFlavor" />
  </component>
  <component name="CMakeReloadState">
    <option name="reloaded" value="true" />
  </component>
  <component name="CMakeRunConfigurationManager">
    <generated>
      <config projectName="BambuStudio" targetName="BambuStudio" />
      <config projectName="BambuStudio" targetName="mcut" />
      <config projectName="BambuStudio" targetName="libslic3r_gui" />
      <config projectName="BambuStudio" targetName="Clipper2" />
      <config projectName="BambuStudio" targetName="gettext_merge_po_with_pot" />
      <config projectName="BambuStudio" targetName="gettext_po_to_mo" />
      <config projectName="BambuStudio" targetName="imgui" />
      <config projectName="BambuStudio" targetName="Shiny" />
      <config projectName="BambuStudio" targetName="nowide" />
      <config projectName="BambuStudio" targetName="libslic3r" />
      <config projectName="BambuStudio" targetName="libslic3r_cgal" />
      <config projectName="BambuStudio" targetName="clipper" />
      <config projectName="BambuStudio" targetName="hintsToPot" />
      <config projectName="BambuStudio" targetName="glu-libtess" />
      <config projectName="BambuStudio" targetName="gettext_make_pot" />
      <config projectName="BambuStudio" targetName="minilzo_static" />
      <config projectName="BambuStudio" targetName="miniz_static" />
      <config projectName="BambuStudio" targetName="imguizmo" />
      <config projectName="BambuStudio" targetName="hidapi" />
      <config projectName="BambuStudio" targetName="libnest2d" />
      <config projectName="BambuStudio" targetName="semver" />
      <config projectName="BambuStudio" targetName="admesh" />
    </generated>
  </component>
  <component name="CMakeSettings">
    <configurations>
      <configuration PROFILE_NAME="debug" ENABLED="true" GENERATION_DIR="build/debug" CONFIG_NAME="Debug" GENERATION_OPTIONS="-G Ninja -DCMAKE_PREFIX_PATH=/Users/<USER>/Documents/augment-projects/BambuStudio_dep/usr/local" />
      <configuration PROFILE_NAME="release" ENABLED="false" FROM_PRESET="true" GENERATION_DIR="$PROJECT_DIR$/build/release" />
      <configuration PROFILE_NAME="xcode-debug" ENABLED="false" FROM_PRESET="true" GENERATION_DIR="$PROJECT_DIR$/build/xcode" />
      <configuration PROFILE_NAME="debug - debug" ENABLED="false" FROM_PRESET="true" GENERATION_DIR="$PROJECT_DIR$/build/debug" />
      <configuration PROFILE_NAME="release - release" ENABLED="false" FROM_PRESET="true" GENERATION_DIR="$PROJECT_DIR$/build/release" />
      <configuration PROFILE_NAME="xcode-debug - xcode-debug" ENABLED="false" FROM_PRESET="true" GENERATION_DIR="$PROJECT_DIR$/build/xcode" />
    </configurations>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="557a40fd-e575-43fa-a018-e64cf33ecbc5" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/CMakeLists.txt" beforeDir="false" afterPath="$PROJECT_DIR$/CMakeLists.txt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="CMakeBuildProfile:debug" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="32mx4pprQL9Vy343EMidf6Zow92" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;CMake 应用程序.BambuStudio.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.RadMigrateCodeStyle&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.west.config.association.type.startup.service&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/Documents/augment-projects/BambuStudio&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;CMakeSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="CMake 应用程序.BambuStudio">
    <configuration default="true" type="CLionExternalRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true">
      <method v="2">
        <option name="CLION.EXTERNAL.BUILD" enabled="true" />
      </method>
    </configuration>
    <configuration name="BambuStudio" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="BambuStudio" CONFIG_NAME="debug" RUN_TARGET_PROJECT_NAME="BambuStudio" RUN_TARGET_NAME="BambuStudio">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Clipper2" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="Clipper2" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Shiny" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="Shiny" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="admesh" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="admesh" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="clipper" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="clipper" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="gettext_make_pot" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="gettext_make_pot" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="gettext_merge_po_with_pot" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="gettext_merge_po_with_pot" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="gettext_po_to_mo" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="gettext_po_to_mo" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="glu-libtess" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="glu-libtess" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hidapi" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="hidapi" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hintsToPot" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="hintsToPot" CONFIG_NAME="debug" RUN_TARGET_PROJECT_NAME="BambuStudio" RUN_TARGET_NAME="hintsToPot">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="imgui" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="imgui" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="imguizmo" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="imguizmo" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="libnest2d" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="libnest2d" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="libslic3r" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="libslic3r" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="libslic3r_cgal" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="libslic3r_cgal" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="libslic3r_gui" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="libslic3r_gui" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="mcut" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="mcut" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="minilzo_static" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="minilzo_static" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="miniz_static" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="miniz_static" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="nowide" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="nowide" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="semver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="semver" CONFIG_NAME="debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="CMake 应用程序.BambuStudio Debug" />
      <item itemvalue="CMake 应用程序.gettext_make_pot" />
      <item itemvalue="CMake 应用程序.gettext_merge_po_with_pot" />
      <item itemvalue="CMake 应用程序.gettext_po_to_mo" />
      <item itemvalue="CMake 应用程序.BambuStudio" />
      <item itemvalue="CMake 应用程序.admesh" />
      <item itemvalue="CMake 应用程序.nowide" />
      <item itemvalue="CMake 应用程序.clipper" />
      <item itemvalue="CMake 应用程序.Clipper2" />
      <item itemvalue="CMake 应用程序.glu-libtess" />
      <item itemvalue="CMake 应用程序.hidapi" />
      <item itemvalue="CMake 应用程序.hintsToPot" />
      <item itemvalue="CMake 应用程序.imgui" />
      <item itemvalue="CMake 应用程序.imguizmo" />
      <item itemvalue="CMake 应用程序.libnest2d" />
      <item itemvalue="CMake 应用程序.libslic3r" />
      <item itemvalue="CMake 应用程序.libslic3r_cgal" />
      <item itemvalue="CMake 应用程序.mcut" />
      <item itemvalue="CMake 应用程序.minilzo_static" />
      <item itemvalue="CMake 应用程序.miniz_static" />
      <item itemvalue="CMake 应用程序.semver" />
      <item itemvalue="CMake 应用程序.Shiny" />
      <item itemvalue="CMake 应用程序.libslic3r_gui" />
    </list>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="557a40fd-e575-43fa-a018-e64cf33ecbc5" name="更改" comment="" />
      <created>1758039360270</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1758039360270</updated>
      <workItem from="1758039361439" duration="1096000" />
      <workItem from="1758088283368" duration="24000" />
      <workItem from="1758088319549" duration="137000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VCPKGProject">
    <isAutomaticCheckingOnLaunch value="false" />
    <isAutomaticFoundErrors value="true" />
    <isAutomaticReloadCMake value="true" />
  </component>
</project>