<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="BambuStudio Debug" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="BambuStudio" TARGET_NAME="BambuStudio_app_gui" CONFIG_NAME="debug" RUN_TARGET_PROJECT_NAME="BambuStudio" RUN_TARGET_NAME="BambuStudio_app_gui">
    <method v="2">
      <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
    </method>
  </configuration>
</component>
